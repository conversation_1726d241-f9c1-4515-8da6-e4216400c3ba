package com.nti56.dcm.server.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.math.BigDecimal;
import lombok.Data;

/**
 * <p>
 * 盘点结果项表 实体类
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @Ddate 2024-05-08 13:49:40
 * @since JDK 1.8
 */
@Data
@TableName("stocktaking_result_item")
public class StocktakingResultItemEntity extends BaseEntity{
        /**
        * 所属盘点单id
        */
        private Long stocktakingOrderId;

        /**
        * 盘点货位id
        */
        private Long sparesLocationId;

        /**
        * 盘点备件id
        */
        private Long sparesId;

        /**
        * 原库存数量
        */
        private BigDecimal originQuantity;

        /**
        * 盘点数量
        */
        private BigDecimal stocktakingQuantity;

        /**
        * 盈亏数量=盘点数量-原库存数量，等于0相符，大于0盘盈，小于0盘亏
        */
        private BigDecimal diffQuantity;



}
