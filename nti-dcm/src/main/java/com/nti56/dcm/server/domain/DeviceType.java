package com.nti56.dcm.server.domain;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.bean.copier.CopyOptions;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.google.common.collect.HashMultiset;
import com.google.common.collect.Multiset;
import com.nti56.dcm.server.constant.DictConstant;
import com.nti56.dcm.server.domain.enums.ConnectOtEnum;
import com.nti56.dcm.server.domain.enums.DeviceStateEnum;
import com.nti56.dcm.server.domain.enums.FileBizTypeEnum;
import com.nti56.dcm.server.entity.DeviceEntity;
import com.nti56.dcm.server.entity.DeviceTypeEntity;
import com.nti56.dcm.server.entity.DeviceTypeRelationEntity;
import com.nti56.dcm.server.entity.FileEntity;
import com.nti56.dcm.server.model.dto.DeviceTypeDto;
import com.nti56.dcm.server.model.dto.OtDeviceTemplateDto;
import com.nti56.dcm.server.model.vo.DeviceVo;
import com.nti56.dcm.server.model.vo.FileVo;
import com.nti56.nlink.common.base.FieldValue;
import com.nti56.nlink.common.base.UniqueConstraint;
import com.nti56.nlink.common.fetcher.CommonFetcher;
import com.nti56.nlink.common.fetcher.Preloader;
import com.nti56.nlink.common.util.Result;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;

@Slf4j
public class DeviceType {
    
    public static final SerialNumber SERIALNUMBER = new SerialNumber("DEVTY");

    @Getter
    private DeviceTypeEntity entity;
    
    @Getter
    private Long id;
    
    @Getter
    private Long tenantId;
    
    @Getter
    private String name;
    
    @Getter
    @Setter
    private List<DeviceVo> devices;

    private DeviceTypeEntity inheritType;

    @Getter
    private Boolean allType;

    @Getter
    private List<DeviceEntity> needUpdateExtendInfoDeviceList;

    private static final UniqueConstraint typeUniqueConstraint = new UniqueConstraint("type_name","id_type");

    public static Result<DeviceTypeDto> getAllInfos(Long typeId, CommonFetcher commonFetcher) {

        if (Objects.isNull(typeId)) {
            return Result.error("类型ID为空！");
        }
        DeviceTypeEntity deviceTypeEntity = commonFetcher.get(typeId, DeviceTypeEntity.class);
        if (Objects.isNull(deviceTypeEntity)) {
            return Result.error("设备类型不存在！");
        }
        DeviceTypeDto deviceTypeDto = BeanUtil.copyProperties(deviceTypeEntity, DeviceTypeDto.class, "params", "workData", "taskData", "energyData");
        String params = deviceTypeEntity.getParams();
        if (StrUtil.isNotBlank(params)) {
            List<Map<String, Object>> maps = JSON.parseObject(params, new TypeReference<List<Map<String, Object>>>() {
            });
            deviceTypeDto.setParams(maps);
        }
        if (StrUtil.isNotBlank(deviceTypeEntity.getTaskData())) {
            List<Map<String, Object>> maps = JSON.parseObject(deviceTypeEntity.getTaskData(), new TypeReference<List<Map<String, Object>>>() {
            });
            deviceTypeDto.setTaskData(maps);
        }
        if (StrUtil.isNotBlank(deviceTypeEntity.getWorkData())) {
            List<Map<String, Object>> maps = JSON.parseObject(deviceTypeEntity.getWorkData(), new TypeReference<List<Map<String, Object>>>() {
            });
            deviceTypeDto.setWorkData(maps);
        }
        if (StrUtil.isNotBlank(deviceTypeEntity.getEnergyData())) {
            List<Map<String, Object>> maps = JSON.parseObject(deviceTypeEntity.getEnergyData(), new TypeReference<List<Map<String, Object>>>() {
            });
            deviceTypeDto.setEnergyData(maps);
        }

        List<DeviceTypeRelationEntity> typeRelationEntities = commonFetcher.list("device_type_id", typeId, DeviceTypeRelationEntity.class);
        List<OtDeviceTemplateDto> deviceTemplateList = new ArrayList<>();
        if (CollectionUtil.isNotEmpty(typeRelationEntities)) {
            typeRelationEntities.forEach(item -> {
                OtDeviceTemplateDto otDeviceTemplateDto = new OtDeviceTemplateDto();
                otDeviceTemplateDto.setId(item.getDeviceTemplateId());
                otDeviceTemplateDto.setName(item.getDeviceTemplateName());
                deviceTemplateList.add(otDeviceTemplateDto);
            });
            deviceTypeDto.setDeviceTemplateList(deviceTemplateList);
            deviceTypeDto.setRelationTemplateName(String.join(",", typeRelationEntities.stream().map(DeviceTypeRelationEntity::getDeviceTemplateName).collect(Collectors.toList())));
        }
       /* List<DeviceTypeEntity> son = commonFetcher.list("parent_id", typeId, DeviceTypeEntity.class);
        if(CollectionUtil.isNotEmpty(son)){
            deviceTypeDto.setHasNext(true);
        }else {
            deviceTypeDto.setHasNext(false);
        }*/
        //获取自己的文档和图片
        List<FileEntity> allFiles = commonFetcher.list("biz_id", typeId, FileEntity.class);
        if (CollectionUtil.isNotEmpty(allFiles)) {
            Map<Integer, List<FileEntity>> fileCollect = allFiles.stream().collect(Collectors.groupingBy(FileEntity::getBizType));
            fileCollect.forEach((k, v) -> {
                FileBizTypeEnum fileBizTypeEnum = FileBizTypeEnum.typeOfValue(k);
                switch (fileBizTypeEnum) {
                    case DEVICE_TYPE_PICTURE:
                        deviceTypeDto.setImageFiles(BeanUtil.copyToList(v, FileVo.class).stream().sorted(Comparator.comparing(FileVo::getCreateTime).reversed()).collect(Collectors.toList()));
                        break;
                    case DEVICE_TYPE_DOC:
                        deviceTypeDto.setDocumentFiles(BeanUtil.copyToList(v, FileVo.class).stream().sorted(Comparator.comparing(FileVo::getCreateTime).reversed()).collect(Collectors.toList()));
                        break;
                    default:
                        log.warn("设备类型不支持的文件类型");
                }
            });
        }
       /* if (!Objects.isNull(deviceTypeDto.getParentId())) {
            DeviceTypeEntity parentEntity = commonFetcher.get(deviceTypeDto.getParentId(), DeviceTypeEntity.class);
            deviceTypeDto.setParentTypeName(parentEntity.getTypeName());
            //获取全部参数信息
            deviceTypeDto.setInheritParams(getInheritParams(deviceTypeDto.getParentId(), commonFetcher));
            //获取设备图片、文档
            List<FileEntity> inheritFiles = getInheritFiles(deviceTypeDto.getParentId(), commonFetcher);
            if (CollectionUtil.isNotEmpty(inheritFiles)) {
                Map<Integer, List<FileEntity>> collect = inheritFiles.stream().collect(Collectors.groupingBy(FileEntity::getBizType));
                collect.forEach((k, v) -> {
                    FileBizTypeEnum fileBizTypeEnum = FileBizTypeEnum.typeOfValue(k);
                    switch (fileBizTypeEnum) {
                        case DEVICE_TYPE_PICTURE:
                            deviceTypeDto.setInheritImageFiles(BeanUtil.copyToList(v, FileVo.class).stream().sorted(Comparator.comparing(FileVo::getCreateTime).reversed()).collect(Collectors.toList()));
                            break;
                        case DEVICE_TYPE_DOC:
                            deviceTypeDto.setInheritDocumentFiles(BeanUtil.copyToList(v, FileVo.class).stream().sorted(Comparator.comparing(FileVo::getCreateTime).reversed()).collect(Collectors.toList()));
                            break;
                        default:
                            log.warn("设备类型不支持的文件类型");
                    }
                });
            }
        }*/
        return Result.ok(deviceTypeDto);
    }


    public static List<Map<String, Object>> getInheritParams(Long typeId, CommonFetcher commonFetcher) {
        List<Map<String, Object>> result = new ArrayList<>();
        collectInheritParams(typeId, result, commonFetcher);
        return result;
    }

    private static void collectInheritParams(Long typeId, List<Map<String, Object>> result, CommonFetcher commonFetcher) {
        DeviceTypeEntity deviceTypeEntity = commonFetcher.get(typeId, DeviceTypeEntity.class);
        if (!Objects.isNull(deviceTypeEntity)) {
            if (StrUtil.isNotBlank(deviceTypeEntity.getParams())) {
                List<Map<String, Object>> maps = JSON.parseObject(deviceTypeEntity.getParams(), new TypeReference<List<Map<String, Object>>>() {
                });
                result.addAll(maps);
            }
        }
//        Long parentId = Optional.ofNullable(deviceTypeEntity).map(DeviceTypeEntity::getGroupId).orElse(null);
//        if (!Objects.isNull(parentId)) {
//            collectInheritParams(parentId, result, commonFetcher);
//        }
    }

    public static Result<DeviceType> checkCreate(DeviceTypeDto dto, CommonFetcher commonFetcher) {
        if (StrUtil.isBlank(dto.getTypeName())) {
            return Result.error("设备类型名称不能为空！");
        }
        if (dto.getTypeName().contains("/")) {
            return Result.error("设备类型名称不能包含符号\"/\"！");
        }
        // 校验同级别下是否存在设备类型重名
        try {
            DeviceTypeEntity repeat = commonFetcher.get(typeUniqueConstraint.buildUnique(new FieldValue(dto.getTypeName()),new FieldValue(dto.getIdType())), DeviceTypeEntity.class);
            if (ObjUtil.isNotNull(repeat)) {
                return Result.error("设备类型名称已存在：" + dto.getTypeName());
            }
        }catch (Exception e){
            return Result.error("设备类型名称已存在：" + dto.getTypeName());
        }
        if (CollectionUtil.isNotEmpty(dto.getParams())) {
            List<String> paramNameList = dto.getParams().stream().flatMap(i -> i.keySet().stream()).collect(Collectors.toList());
//            List<String> inheritParamNameList = new ArrayList<>();
//            if (!Objects.isNull(dto.getParentId())) {
//                //获取全部参数信息
//                List<Map<String, Object>> inheritParams = getInheritParams(dto.getParentId(), commonFetcher);
//                inheritParamNameList = inheritParams.stream().flatMap(i -> i.keySet().stream()).collect(Collectors.toList());
//            }
//            paramNameList.addAll(inheritParamNameList);
            if (paramNameList.stream().distinct().collect(Collectors.toList()).size() != paramNameList.size()) {
                return Result.error("存在重复的设备信息字段名称！");
            }
        }
        DeviceTypeEntity entity = new DeviceTypeEntity();
        BeanUtils.copyProperties(dto, entity, "params");
        if (!Objects.isNull(dto.getParams())) {
            entity.setParams(JSON.toJSONString(dto.getParams()));
        }
        DeviceType deviceType = new DeviceType();
        deviceType.entity = entity;
        return Result.ok(deviceType);
    }


    public static Result<DeviceType> checkUpdate(DeviceTypeDto deviceTypeDto, CommonFetcher commonFetcher) {

        if (Objects.isNull(deviceTypeDto.getId())) {
            return Result.error("设备类型ID不能为空！");
        }
        if (deviceTypeDto.getTypeName().contains("/")) {
            return Result.error("设备类型名称不能包含符号\"/\"！");
        }

        DeviceTypeEntity oldEntity = commonFetcher.get(deviceTypeDto.getId(), DeviceTypeEntity.class);
        if (Objects.isNull(oldEntity)) {
            return Result.error("不存在的设备类型！");
        }
        // 校验同级别下是否存在设备类型重名
        try {
            DeviceTypeEntity repeat = commonFetcher.get(typeUniqueConstraint.buildUnique(new FieldValue(deviceTypeDto.getTypeName()),new FieldValue(deviceTypeDto.getIdType())), DeviceTypeEntity.class);
            if (ObjUtil.isNotNull(repeat) && !ObjectUtil.equals(repeat.getId(),deviceTypeDto.getId())  ) {
                return Result.error("设备类型名称已存在：" + deviceTypeDto.getTypeName());
            }
        }catch (Exception e){
            return Result.error("设备类型名称已存在：" + deviceTypeDto.getTypeName());
        }
        DeviceType deviceType = new DeviceType();
        List<DeviceEntity> needUpdateList = new ArrayList<>();
        if (ObjectUtil.isNotNull(deviceTypeDto.getParams())) {
            List<Map<String, Object>> allDeviceTypeParamInfo = new ArrayList<>();
            allDeviceTypeParamInfo.addAll(deviceTypeDto.getParams());
            List<String> paramNameList = deviceTypeDto.getParams().stream().flatMap(i -> i.keySet().stream()).collect(Collectors.toList());
//            List<String> inheritParamNameList = new ArrayList<>();
//            if (!Objects.isNull(deviceTypeDto.getParentId())) {
//                //获取全部参数信息
//                List<Map<String, Object>> inheritParams = getInheritParams(deviceTypeDto.getParentId(), commonFetcher);
//                allDeviceTypeParamInfo.addAll(inheritParams);
//                inheritParamNameList = inheritParams.stream().flatMap(i -> i.keySet().stream()).collect(Collectors.toList());
//            }
//            paramNameList.addAll(inheritParamNameList);
            if (paramNameList.stream().distinct().count() != paramNameList.size()) {
                return Result.error("存在重复的设备信息字段名称！");
            }
            List<DeviceTypeEntity> rootNode = new ArrayList<>();
            rootNode.add(oldEntity);
            List<DeviceEntity> devicesList = commonFetcher.preloader(DeviceEntity.class).preload("device_type_id", rootNode.stream().map(DeviceTypeEntity::getId).collect(Collectors.toList())).list();
            for (DeviceEntity device : devicesList) {
                if (StrUtil.isNotBlank(device.getExtendInfo())) {
                    List<Map<String, Object>> extendInfoMap = JSON.parseObject(device.getExtendInfo(), new TypeReference<List<Map<String, Object>>>() {
                    });
                    List<String> extendKeyList = extendInfoMap.stream().flatMap(i -> i.keySet().stream()).collect(Collectors.toList());
                    for (String extendKey : extendKeyList) {
                        if (paramNameList.contains(extendKey)) {
                            return Result.error("修改的设备信息字段与该设备类型下的参数信息名称重复，重复设备信息字段名称:【" + extendKey + "】重复设备名称:【" + device.getDeviceName() + "】");
                        }
                    }
                }
                boolean needUpdate = false;
                List<Map<String, Object>> inheritExtendInfoMap = new ArrayList<>();
                if (StrUtil.isNotBlank(device.getInheritExtendInfo())) {
                    inheritExtendInfoMap = JSON.parseObject(device.getInheritExtendInfo(), new TypeReference<List<Map<String, Object>>>() {
                    });
                }
                List<String> inheritExtendKeyList = inheritExtendInfoMap.stream().flatMap(i -> i.keySet().stream()).collect(Collectors.toList());
                for (int i = 0; i < inheritExtendInfoMap.size(); i++) {
                    Map<String, Object> map = inheritExtendInfoMap.get(i);
                    String key = new ArrayList<>(map.keySet()).get(0);
                    if (!paramNameList.contains(key)) {
                        needUpdate = true;
                        inheritExtendInfoMap.remove(i);
                    }
                }
                for (Map<String, Object> map : allDeviceTypeParamInfo) {
                    String key = new ArrayList<>(map.keySet()).get(0);
                    if (!inheritExtendKeyList.contains(key)) {
                        needUpdate = true;
                        inheritExtendInfoMap.add(map);
                    }
                }
                if (needUpdate) {
                    DeviceEntity updateDeviceEntity = new DeviceEntity();
                    updateDeviceEntity.setId(device.getId());
                    updateDeviceEntity.setInheritExtendInfo(JSON.toJSONString(inheritExtendInfoMap));
                    needUpdateList.add(updateDeviceEntity);
                }
            }
        }

        BeanUtil.copyProperties(deviceTypeDto, oldEntity, CopyOptions.create().setIgnoreNullValue(false).setIgnoreProperties("params", "tenantId", "workData", "taskData", "energyData"));
        if (!Objects.isNull(deviceTypeDto.getParams())) {
            oldEntity.setParams(JSON.toJSONString(deviceTypeDto.getParams()));
        }
        deviceType.entity = oldEntity;
        deviceType.needUpdateExtendInfoDeviceList = needUpdateList;
        return Result.ok(deviceType);
    }


    public static Result<Void> checkDelete(Long id, CommonFetcher commonFetcher) {

        DeviceTypeEntity existType = commonFetcher.get(id, DeviceTypeEntity.class);
        if (Objects.isNull(existType)) {
            return Result.error("设备类型不存在，无法删除！");
        }
        //检查是否存放了设备
        List<DeviceEntity> spaceDevices = commonFetcher.list("device_type_id", id, DeviceEntity.class);
        if (CollectionUtil.isNotEmpty(spaceDevices)) {
            return Result.error("设备类型下存在设备，无法删除");
        }
        return Result.ok();
    }

    public static Result<DeviceType> checkInfo(Long tenantId, Long deviceTypeId, CommonFetcher commonFetcher, Boolean needPmo, CommonFetcher defaultCommonFetcher) {
        DeviceType deviceType = new DeviceType();
        if(deviceTypeId == null){
            deviceType.allType = true;
            deviceType.tenantId = tenantId;
        }else{
            DeviceTypeEntity deviceTypeEntity = commonFetcher.get(deviceTypeId, DeviceTypeEntity.class);
            if (deviceTypeEntity == null && needPmo){
                deviceTypeEntity = defaultCommonFetcher.get(deviceTypeId, DeviceTypeEntity.class);
            }
            deviceType.allType = false;
            deviceType.entity = deviceTypeEntity;
            deviceType.id = deviceTypeEntity.getId();
            deviceType.name = deviceTypeEntity.getTypeName();
            deviceType.tenantId = tenantId;
        }
        deviceType.devices = new ArrayList<>();

        return Result.ok(deviceType);
    }

    public void loadDevices(CommonFetcher commonFetcher, Boolean needPmoDevice, CommonFetcher pmoCommonFetcher, CommonFetcher defaultCommonFetcher) {
        if(allType){
            List<DeviceEntity> deviceEntities = commonFetcher.list("tenant_id", tenantId, DeviceEntity.class);
            if (needPmoDevice){
                List<DeviceEntity> pmoDevices = pmoCommonFetcher.list("is_pmo", 1, DeviceEntity.class);
                if (CollUtil.isNotEmpty(pmoDevices)) {
                    pmoDevices.forEach(item -> item.setTenantId(DictConstant.DEFAULT_TENANT_ID));
                    deviceEntities.addAll(pmoDevices);
                }
            }
            if(deviceEntities != null && deviceEntities.size() > 0){
                List<Long> deviceTypeIds = deviceEntities.stream().map(DeviceEntity::getDeviceTypeId).filter(t -> {
                    return t != null;
                }).distinct().collect(Collectors.toList());
                Preloader<DeviceTypeEntity> preloader = commonFetcher.preloader(DeviceTypeEntity.class).preload("id", deviceTypeIds);
                Preloader<DeviceTypeEntity> pmoPreloader = defaultCommonFetcher.preloader(DeviceTypeEntity.class).preload("id", deviceTypeIds);

                Map<Long, String> deviceTypeIdNameMap = new HashMap<>();
                for(Long deviceTypeId:deviceTypeIds){
                    DeviceTypeEntity t = preloader.getFirst("id", deviceTypeId);
                    if(t != null){
                        deviceTypeIdNameMap.put(deviceTypeId, t.getTypeName());
                    }else {
                        DeviceTypeEntity first = pmoPreloader.getFirst("id", deviceTypeId);
                        if(first != null){
                            deviceTypeIdNameMap.put(deviceTypeId, first.getTypeName());
                        }
                    }
                }
                this.devices = deviceEntities.stream()
                .filter(t -> {
                    return t.getDeleted() == 0;
                }).map(entity -> {
                    DeviceVo vo = new DeviceVo();
                    BeanUtils.copyProperties(entity, vo);
                    vo.setDeviceTypeId(entity.getDeviceTypeId());
                    vo.setDeviceType(deviceTypeIdNameMap.get(entity.getDeviceTypeId()));
                    return vo;
                }).collect(Collectors.toList());
            }
        }else{
            List<Long> deviceTypeIdList = new ArrayList<>();
            deviceTypeIdList.add(id);
            Preloader<DeviceEntity> preloader = commonFetcher.preloader(DeviceEntity.class)
                    .preload("device_type_id", deviceTypeIdList);
            List<DeviceEntity> deviceEntities = preloader.filter("device_type_id", id);
            if (needPmoDevice){
                List<DeviceEntity> pmoDevices = pmoCommonFetcher.preloader(DeviceEntity.class)
                        .preload("device_type_id", deviceTypeIdList).filter("device_type_id", id);
                if (CollUtil.isNotEmpty(pmoDevices)) {
                    pmoDevices.forEach(item -> item.setTenantId(DictConstant.DEFAULT_TENANT_ID));
                    if (deviceEntities == null) {
                        deviceEntities = new ArrayList<>();
                    }
                    deviceEntities.addAll(pmoDevices);
                }
            }
    
            if(deviceEntities != null && deviceEntities.size() > 0){
                this.devices = deviceEntities.stream().map(entity -> {
                    DeviceVo vo = new DeviceVo();
                    BeanUtils.copyProperties(entity, vo);
                    vo.setDeviceTypeId(id);
                    vo.setDeviceType(name);
                    return vo;
                }).collect(Collectors.toList());
            }
        }
    }

    public List<DeviceVo> getAllDevices() {
        return this.devices;
    }

    public void importDeviceState(Map<String, Integer> deviceNameStateMap,List<DeviceVo> deviceVoList) {
        if(deviceVoList == null){
            return;
        }
        for (DeviceVo vo : deviceVoList) {
            String alias = vo.getDeviceAliasName();
            Integer state = deviceNameStateMap.get(alias);
            DeviceStateEnum stateEnum = DeviceStateEnum.typeOfValue(state);
            if(stateEnum == null){
                if(ConnectOtEnum.CONNECT.getValue().equals(vo.getConnectOt())){
                    vo.setState(DeviceStateEnum.OFF_LINE);
                }
            }else{
                vo.setState(stateEnum);
            }
        }
    }

    // 设备状态计数器
    private Multiset<Integer> deviceStateCounter;
    
    // 设备状态统计
    @Getter
    private DeviceStateCount deviceStateCount;

    public void countDeviceState() {
        deviceStateCounter = HashMultiset.create();
        deviceStateCount = new DeviceStateCount();
        Integer totalCount = 0;
        if (devices != null) {
            for (DeviceVo vo : devices) {
                DeviceStateEnum state = vo.getState();
                if (state != null && vo.getConnectOt() != null && ConnectOtEnum.CONNECT.getValue().equals(vo.getConnectOt())) {
                    deviceStateCounter.add(state.getValue());
                }
            }
            totalCount = devices.stream().filter(t -> {
                return t.getConnectOt() != null && ConnectOtEnum.CONNECT.getValue().equals(t.getConnectOt());
            }).collect(Collectors.toList()).size();
        }

        Integer faultCount = deviceStateCounter.count(DeviceStateEnum.FAULT.getValue());
        Integer taskCount = deviceStateCounter.count(DeviceStateEnum.TASK.getValue());
        Integer standByCount = deviceStateCounter.count(DeviceStateEnum.STAND_BY.getValue());
        Integer offlineCount = deviceStateCounter.count(DeviceStateEnum.OFF_LINE.getValue());

        deviceStateCount.setTotalCount(totalCount);
        deviceStateCount.setFaultCount(faultCount);
        deviceStateCount.setTaskCount(taskCount);
        deviceStateCount.setStandByCount(standByCount);
        deviceStateCount.setOnlineCount(taskCount + standByCount + faultCount);
        deviceStateCount.setOfflineCount(totalCount - taskCount - standByCount - faultCount);

    }

    public DeviceStateCount getAllDeviceStateCount() {
        return deviceStateCount;
    }


    public void filterDevice(String deviceName, String deviceSerialNumber, DeviceStateEnum state) {
        if(devices != null){
            devices = devices.stream()
            .filter(t -> {
                if(deviceName == null || "".equals(deviceName)){
                    return true;
                }
                String name = t.getDeviceName();
                return name.contains(deviceName);
            })
            .filter(t -> {
                if(deviceSerialNumber == null || "".equals(deviceSerialNumber)){
                    return true;
                }
                String number = t.getSerialNumber();
                return number.contains(deviceSerialNumber);
            })
            .filter(t -> {
                if(state == null){
                    return true;
                }
                DeviceStateEnum state2 = t.getState();
                return state.equals(state2);
            })
            .collect(Collectors.toList());
        }
    }

    public void filterEmpowerDevice(Set<Long> empowerDeviceIds) {
        if(empowerDeviceIds == null){
            return;
        }
        if(devices != null){
            devices = devices.stream()
            .filter(t -> {
                return empowerDeviceIds.contains(t.getId());
            })
            .collect(Collectors.toList());
        }
    }
}
