package com.nti56.dcm.server.domain.enums;

import lombok.Getter;

public enum FieldTypeEnum {
    TEXT(1, "text", "文本"),
    OPTION(2, "option", "单选"),
    ;

    @Getter
    private Integer value;

    @Getter
    private String name;

    @Getter
    private String nameDesc;

    FieldTypeEnum(Integer value, String name, String nameDesc) {
        this.value = value;
        this.name = name;
        this.nameDesc = nameDesc;
    }

    public static FieldTypeEnum typeOfValue(Integer value){
        FieldTypeEnum[] values = FieldTypeEnum.values();
        for (FieldTypeEnum v : values) {
            if (v.value.equals(value)) {
                return v;
            }
        }
        return null;
    }

    public static FieldTypeEnum typeOfName(String name){
        FieldTypeEnum[] values = FieldTypeEnum.values();
        for (FieldTypeEnum v : values) {
            if (v.name.equals(name)) {
                return v;
            }
        }
        return null;
    }

    public static FieldTypeEnum typeOfNameDesc(String nameDesc){
        FieldTypeEnum[] values = FieldTypeEnum.values();
        for (FieldTypeEnum v : values) {
            if (v.nameDesc.equals(nameDesc)) {
                return v;
            }
        }
        return null;
    }
}
