package com.nti56.dcm.server.domain.enums;

import lombok.Getter;

public enum PlanStatusEnum {
    PROCESSING(1, "PROCESSING", "进行中"),
    FINISH(2, "FINISH", "已结束"),
    ;

    @Getter
    private Integer value;

    @Getter
    private String name;

    @Getter
    private String nameDesc;

    PlanStatusEnum(Integer value, String name, String nameDesc) {
        this.value = value;
        this.name = name;
        this.nameDesc = nameDesc;
    }

    public static PlanStatusEnum typeOfValue(Integer value){
        PlanStatusEnum[] values = PlanStatusEnum.values();
        for (PlanStatusEnum v : values) {
            if (v.value.equals(value)) {
                return v;
            }
        }
        return null;
    }

    public static PlanStatusEnum typeOfName(String name){
        PlanStatusEnum[] values = PlanStatusEnum.values();
        for (PlanStatusEnum v : values) {
            if (v.name.equals(name)) {
                return v;
            }
        }
        return null;
    }

    public static PlanStatusEnum typeOfNameDesc(String nameDesc){
        PlanStatusEnum[] values = PlanStatusEnum.values();
        for (PlanStatusEnum v : values) {
            if (v.nameDesc.equals(nameDesc)) {
                return v;
            }
        }
        return null;
    }
}
