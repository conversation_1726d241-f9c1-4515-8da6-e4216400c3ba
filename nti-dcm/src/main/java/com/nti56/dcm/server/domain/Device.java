package com.nti56.dcm.server.domain;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.bean.copier.CopyOptions;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.nti56.basic.utils.SpringUtil;
import com.nti56.dcm.server.domain.enums.FileBizTypeEnum;
import com.nti56.dcm.server.entity.DeviceEntity;
import com.nti56.dcm.server.entity.DeviceTypeEntity;
import com.nti56.dcm.server.entity.DeviceTypeMonitorEntity;
import com.nti56.dcm.server.entity.FileEntity;
import com.nti56.dcm.server.mapper.DeviceMapper;
import com.nti56.dcm.server.model.dto.DeviceDto;
import com.nti56.dcm.server.model.dto.DeviceTypeDto;
import com.nti56.dcm.server.model.vo.FileVo;
import com.nti56.nlink.common.base.FieldValue;
import com.nti56.nlink.common.base.UniqueConstraint;
import com.nti56.nlink.common.fetcher.CommonFetcher;
import com.nti56.nlink.common.util.Result;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 类说明: 备件领域对象
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2023-11-06 09:45:11
 * @since JDK 1.8
 */
@Slf4j
public class Device {

    public static final SerialNumber SERIALNUMBER = new SerialNumber("DEV");

    private static final UniqueConstraint nameUniqueConstraint = new UniqueConstraint("device_name");

    public static final UniqueConstraint serialNumberUniqueConstraint = new UniqueConstraint("serial_number");

    private DeviceEntity entity;

    private Long id;

    private Device(){}

    public static Result<Device> checkCreate(DeviceDto dto, CommonFetcher commonFetcher,Long tenantId){

        //必填校验
        if(StrUtil.isBlank(dto.getDeviceName())){
            return Result.error("设备名称不能为空！");
        }
        if(StrUtil.isBlank(dto.getDeviceTypeId())){
            return Result.error("设备类型不能为空");
        }
        if(dto.getStatus() == null){
            return Result.error("启用状况不能为空");
        }
        DeviceMapper deviceMapper = SpringUtil.getBean(DeviceMapper.class);
        List<DeviceEntity> repeatList = deviceMapper.queryNameIsRepeat(tenantId, dto.getDeviceName());
        DeviceTypeEntity deviceTypeEntity = commonFetcher.get(Long.parseLong(dto.getDeviceTypeId()), DeviceTypeEntity.class);
        // 获取继承参数信息
        List<Map<String, Object>> inheritParams = dto.getInheritExtendInfo();
        // 如果继承参数信息为空，则从类型id获取，不为空则为导入时填入的继承参数信息
        if (CollectionUtil.isEmpty(inheritParams)){
            inheritParams =  DeviceType.getInheritParams(deviceTypeEntity.getId(), commonFetcher);
        }
        if (CollectionUtil.isNotEmpty(dto.getExtendInfo())){
            List<String> paramNameList = dto.getExtendInfo().stream().flatMap(i -> i.keySet().stream()).collect(Collectors.toList());
            List<String> inheritParamNameList = new ArrayList<>();
            if (CollectionUtil.isNotEmpty(inheritParams)) {
                //获取全部参数信息
                inheritParamNameList = inheritParams.stream().flatMap(i -> i.keySet().stream()).collect(Collectors.toList());
            }
            paramNameList.addAll(inheritParamNameList);
            if (paramNameList.stream().distinct().collect(Collectors.toList()).size()!=paramNameList.size() ){
                return Result.error("存在重复的设备信息字段名称！");
            }
        }
        if(CollUtil.isNotEmpty(repeatList)){
            return Result.error("设备名称已存在：" + dto.getDeviceName());
        }
        DeviceEntity entity = new DeviceEntity();
        BeanUtil.copyProperties(dto, entity,"extendInfo","inheritExtendInfo");
        if(!Objects.isNull(dto.getExtendInfo())){
            entity.setExtendInfo(JSON.toJSONString(dto.getExtendInfo()));
        }
        if (CollectionUtil.isNotEmpty(inheritParams)) {
            entity.setInheritExtendInfo(JSON.toJSONString(inheritParams));
        }
        Device device = new Device();
        device.entity = entity;
        return Result.ok(device);
    }

    public static Result<DeviceDto> getAllInfos(Long id,CommonFetcher commonFetcher) {

        if(Objects.isNull(id)){
            return Result.error("设备ID为空！");
        }
        DeviceEntity entity = commonFetcher.get(id, DeviceEntity.class);
        if(Objects.isNull(entity)){
            return Result.error("设备不存在！");
        }
        DeviceDto deviceDto = BeanUtil.copyProperties(entity, DeviceDto.class,"extendInfo","inheritExtendInfo");
        if(StrUtil.isNotBlank(entity.getExtendInfo())){
            List<Map<String, Object>> maps = JSON.parseObject(entity.getExtendInfo(), new TypeReference<List<Map<String, Object>>>() {});
            deviceDto.setExtendInfo(maps);
        }
        if(StrUtil.isNotBlank(entity.getInheritExtendInfo())){
            List<Map<String, Object>> maps = JSON.parseObject(entity.getInheritExtendInfo(), new TypeReference<List<Map<String, Object>>>() {});
            deviceDto.setInheritExtendInfo(maps);
        }
        String deviceTypeId = deviceDto.getDeviceTypeId();
        List<FileEntity> allFiles = commonFetcher.list("biz_id", id, FileEntity.class);
        if(CollectionUtil.isNotEmpty(allFiles)){
            Map<Integer, List<FileEntity>> fileCollect = allFiles.stream().collect(Collectors.groupingBy(FileEntity::getBizType));
            fileCollect.forEach((k,v)->{
                FileBizTypeEnum fileBizTypeEnum = FileBizTypeEnum.typeOfValue(k);
                switch (fileBizTypeEnum){
                    case DEVICE_DOC:
                        deviceDto.setDeviceDocuments(BeanUtil.copyToList(v, FileVo.class).stream().sorted(Comparator.comparing(FileVo::getCreateTime).reversed()).collect(Collectors.toList()));
                        break;
                    case DEVICE_PICTURE:
                        deviceDto.setDeviceImages(BeanUtil.copyToList(v, FileVo.class).stream().sorted(Comparator.comparing(FileVo::getCreateTime).reversed()).collect(Collectors.toList()));
                        break;
                    default:
                        log.warn("设备不支持的文件类型");
                }
            });
        }
        //继承自设备类型的参数，文件、图片
        Result<DeviceTypeDto> allInfos = DeviceType.getAllInfos(Long.parseLong(deviceTypeId), commonFetcher);
        DeviceTypeDto deviceTypeDto= allInfos.getResult();
        if (allInfos.getSignal()){
            deviceDto.setTypeName(deviceTypeDto.getTypeName());
            // 设置继承设备类型文档信息
            List<FileVo> deviceTypeDocumentFiles = Optional.ofNullable(deviceTypeDto.getDocumentFiles()).orElse(new ArrayList<>());
            deviceTypeDocumentFiles.addAll(Optional.ofNullable(deviceTypeDto.getInheritDocumentFiles()).orElse(new ArrayList<>()));
            deviceDto.setInheritDocumentFiles(deviceTypeDocumentFiles);
            // 设置继承设备类型图片信息
            List<FileVo> deviceTypeImageFiles = Optional.ofNullable(deviceTypeDto.getImageFiles()).orElse(new ArrayList<>());
            deviceTypeImageFiles.addAll(Optional.ofNullable(deviceTypeDto.getInheritImageFiles()).orElse(new ArrayList<>()));
            deviceDto.setInheritImageFiles(deviceTypeImageFiles);
        }
        Result<DeviceLocation> deviceLocationResult = DeviceLocation.checkInfo(entity.getDeviceLocationId(), commonFetcher);
        deviceDto.setLocation(Optional.ofNullable(deviceLocationResult.getResult()).map(i->i.getName()).orElse(null));

        List<DeviceTypeMonitorEntity> deviceTypeMonitorList = commonFetcher.list("device_type_id", entity.getDeviceTypeId(), DeviceTypeMonitorEntity.class);
        deviceTypeMonitorList.sort(Comparator.comparing(DeviceTypeMonitorEntity::getMonitorType));
        deviceDto.setDeviceTypeMonitorList(deviceTypeMonitorList);

        return Result.ok(deviceDto);

    }

    public DeviceEntity toEntity(){
        return entity;
    }

    public static Result<Device> checkRemove(Long deviceId, CommonFetcher commonFetcher) {
        DeviceEntity entity = commonFetcher.get(deviceId, DeviceEntity.class);
        if(entity == null){
            return Result.error("设备不存在");
        }
        /*List<DeviceRelationEntity> relationEntityList = commonFetcher.list("device_id", deviceId, DeviceRelationEntity.class);
        if(CollectionUtil.isNotEmpty(relationEntityList)){
            return Result.error("设备存在依赖关系，请先解除依赖关系后再删除！");
        }*/
        Device device = new Device();
        device.id = deviceId;
        device.entity = entity;
        return Result.ok(device);
    }

    public static Result<Device> checkInfo(Long deviceId, CommonFetcher commonFetcher) {
        return null;
    }


    public static Result<Device> checkEdit(DeviceDto dto, CommonFetcher commonFetcher, Long tenantId) {
        DeviceMapper deviceMapper = SpringUtil.getBean(DeviceMapper.class);
        List<DeviceEntity> repeatList = deviceMapper.queryNameIsRepeat(tenantId, dto.getDeviceName());
        if(CollUtil.isNotEmpty(repeatList) && repeatList.stream().anyMatch(i -> !i.getId().equals(dto.getId()))){
            return Result.error("设备名称已存在：" + dto.getDeviceName());
        }
        // 获取修改后的继承参数信息
        if (CollectionUtil.isNotEmpty(dto.getExtendInfo())){
            List<String> paramNameList = dto.getExtendInfo().stream().flatMap(i -> i.keySet().stream()).collect(Collectors.toList());
            List<String> inheritParamNameList = new ArrayList<>();
            if (!Objects.isNull(dto.getInheritExtendInfo())) {
                //获取全部参数信息
                List<Map<String, Object>> inheritParams = dto.getInheritExtendInfo();
                inheritParamNameList = inheritParams.stream().flatMap(i -> i.keySet().stream()).collect(Collectors.toList());
            }
            paramNameList.addAll(inheritParamNameList);
            if (paramNameList.stream().distinct().collect(Collectors.toList()).size()!=paramNameList.size() ){
                return Result.error("存在重复的设备信息字段名称！");
            }
        }
        DeviceEntity oldEntity = commonFetcher.get(dto.getId(), DeviceEntity.class);
        if (!ObjectUtil.equals(oldEntity.getDeviceTypeId().toString(),dto.getDeviceTypeId())){
            List<Map<String, Object>> updateInheritParams = DeviceType.getInheritParams(Long.parseLong(dto.getDeviceTypeId()), commonFetcher);
            dto.setInheritExtendInfo(updateInheritParams);
        }
        BeanUtil.copyProperties(dto, oldEntity, CopyOptions.create().setIgnoreNullValue(false).setIgnoreProperties("tenantId","extendInfo","inheritExtendInfo","maintenanceExpireTime","warrantyExpireTime","planScrapTime"));
        if(dto.getExtendInfo() != null){
            oldEntity.setExtendInfo(JSON.toJSONString(dto.getExtendInfo()));
        }
        if (dto.getInheritExtendInfo() != null ){
            oldEntity.setInheritExtendInfo(JSON.toJSONString(dto.getInheritExtendInfo()));
        }
        Device device = new Device();
        device.entity = oldEntity;
        return Result.ok(device);
    }

}
