package com.nti56.dcm.server.domain.enums;

import lombok.Getter;

/**
 * 客户接入阶段枚举
 */
public enum CustomerConnectStageEnum {
    CREATE_DEVICE(1, "createDevice", "台账维护"),
    CONNECTING(2, "connecting", "物联设备接入"),
    MAINTENANCE(3, "maintenance", "设备维保"),
    ;

    @Getter
    private Integer value;

    @Getter
    private String name;

    @Getter
    private String nameDesc;

    CustomerConnectStageEnum(Integer value, String name, String nameDesc) {
        this.value = value;
        this.name = name;
        this.nameDesc = nameDesc;
    }

    public static CustomerConnectStageEnum typeOfValue(Integer value){
        CustomerConnectStageEnum[] values = CustomerConnectStageEnum.values();
        for (CustomerConnectStageEnum v : values) {
            if (v.value.equals(value)) {
                return v;
            }
        }
        return null;
    }

    public static CustomerConnectStageEnum typeOfName(String name){
        CustomerConnectStageEnum[] values = CustomerConnectStageEnum.values();
        for (CustomerConnectStageEnum v : values) {
            if (v.name.equals(name)) {
                return v;
            }
        }
        return null;
    }

    public static CustomerConnectStageEnum typeOfNameDesc(String nameDesc){
        CustomerConnectStageEnum[] values = CustomerConnectStageEnum.values();
        for (CustomerConnectStageEnum v : values) {
            if (v.nameDesc.equals(nameDesc)) {
                return v;
            }
        }
        return null;
    }
}
