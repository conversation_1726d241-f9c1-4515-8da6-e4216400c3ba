package com.nti56.dcm.server.domain.enums;

import lombok.Getter;

@Getter
public enum OrderProcessNodeEnum {
    BEGIN("1", "BEGIN", "root"),
    DISPATCH("2", "DISPATCH", "flyflow_178805691728"),
    EXECUTE("3", "EXECUTE", "flyflow_182619776581"),
    ACCEPTANCE("4", "ACCEP<PERSON>NC<PERSON>", "flyflow_185793614686"),
    VENDOR_ACCEPTANCE("5", "VENDOR_ACCEPTANCE", "flyflow_185793612345"),
    OUTSOURCE_ACCEPTANCE("6", "OUTSOURCE_ACCEPTANCE", "flyflow_472459579183"),

    ;

    @Getter
    private String value;

    @Getter
    private String name;

    @Getter
    private String nodeId;

    OrderProcessNodeEnum(String value, String name, String nodeId) {
        this.value = value;
        this.name = name;
        this.nodeId = nodeId;
    }

    public static OrderProcessNodeEnum typeOfValue(String value){
        OrderProcessNodeEnum[] values = OrderProcessNodeEnum.values();
        for (OrderProcessNodeEnum v : values) {
            if (v.value.equals(value)) {
                return v;
            }
        }
        return null;
    }

    public static OrderProcessNodeEnum typeOfName(String name){
        OrderProcessNodeEnum[] values = OrderProcessNodeEnum.values();
        for (OrderProcessNodeEnum v : values) {
            if (v.name.equals(name)) {
                return v;
            }
        }
        return null;
    }

    public static OrderProcessNodeEnum typeOfNodeId(String nodeId){
        OrderProcessNodeEnum[] values = OrderProcessNodeEnum.values();
        for (OrderProcessNodeEnum v : values) {
            if (v.nodeId.equals(nodeId)) {
                return v;
            }
        }
        return null;
    }
}
