package com.nti56.dcm.server.domain.enums;

import lombok.Getter;

public enum CompleteEnum {
    NO(0, "no", "未完成"),
    YES(1, "yes", "完成"),
    ;

    @Getter
    private Integer value;

    @Getter
    private String name;

    @Getter
    private String nameDesc;

    CompleteEnum(Integer value, String name, String nameDesc) {
        this.value = value;
        this.name = name;
        this.nameDesc = nameDesc;
    }

    public static CompleteEnum typeOfValue(Integer value){
        CompleteEnum[] values = CompleteEnum.values();
        for (CompleteEnum v : values) {
            if (v.value.equals(value)) {
                return v;
            }
        }
        return null;
    }

    public static CompleteEnum typeOfName(String name){
        CompleteEnum[] values = CompleteEnum.values();
        for (CompleteEnum v : values) {
            if (v.name.equals(name)) {
                return v;
            }
        }
        return null;
    }

    public static CompleteEnum typeOfNameDesc(String nameDesc){
        CompleteEnum[] values = CompleteEnum.values();
        for (CompleteEnum v : values) {
            if (v.nameDesc.equals(nameDesc)) {
                return v;
            }
        }
        return null;
    }
}
