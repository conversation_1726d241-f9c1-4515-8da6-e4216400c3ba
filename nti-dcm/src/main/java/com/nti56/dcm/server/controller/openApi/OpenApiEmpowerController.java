package com.nti56.dcm.server.controller.openApi;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.nti56.common.util.R;
import com.nti56.dcm.server.domain.enums.IdTypeEnum;
import com.nti56.dcm.server.model.dto.*;
import com.nti56.dcm.server.service.*;
import com.nti56.nlink.common.dto.TenantIsolation;
import com.nti56.nlink.common.util.PageParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.validation.constraints.NotNull;

/**
 * 开放api，设备授权
 */
@RestController
@RequestMapping("/openapi/empower")
@Slf4j
public class OpenApiEmpowerController {
    
    @Autowired
    private DeviceEmpowerService deviceEmpowerService;


    @GetMapping("pageEmpowerDevice")
    public R pageEmpowerDeviceApp(PageParam pageParam, QueryDeviceEmpowerDto dto, @NotNull @RequestParam(name = "tenantId")Long tenantId) {
        TenantIsolation tenantIsolation = new TenantIsolation();
        //固定今天国际租户
        tenantIsolation.setTenantId(tenantId);
        tenantIsolation.setIdType(IdTypeEnum.SUPPLIER.getValue());
        Page<DeviceDto> page = pageParam.toPage(DeviceDto.class);
        return R.result(deviceEmpowerService.pageEmpowerDeviceApp(tenantIsolation, dto, page));
    }

}
