package com.nti56.dcm.server.ding.api;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.dingtalk.api.DefaultDingTalkClient;
import com.dingtalk.api.DingTalkClient;
import com.dingtalk.api.request.OapiMessageSendToConversationRequest;
import com.dingtalk.api.response.OapiMessageSendToConversationResponse;
import com.nti56.msg.center.common.util.Result;
import com.nti56.msg.center.config.dingtalk.InsideConfig;
import com.nti56.msg.center.enums.error.NotifyErrorEnum;
import com.nti56.msg.center.msgScheduler.actuators.DingDingCardTrigger;
import com.taobao.api.ApiException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.List;

/**
 * 钉钉互动卡片推送api
 */
@Slf4j
@Component
public class DingCardApi {

    @Value("${dd.app-type}")
    private int appType;

    @Value("${dd.key}")
    private String key;

    @Value("${dd.secret}")
    private String secret;

    @Value("${dd.corp-id}")
    private String corpId;

    @Autowired
    private DingDingCardTrigger dingDingCardTrigger;

    public Result getAccessToken(InsideConfig insideConfig) {
        // object转JSONObject
        JSONObject paramsJson = JSONObject.parseObject(JSON.toJSONString(insideConfig));
        try {
            return dingDingCardTrigger.getAccessTokenByType(appType, paramsJson);
        } catch (Exception e) {
            log.error("钉钉分享获取accessToken失败。", e);
            return Result.error(NotifyErrorEnum.DING_DING_GET_ACCESS_TOKEN_FAIL.getCode(), NotifyErrorEnum.DING_DING_GET_ACCESS_TOKEN_FAIL.getMessage());
        }
    }

    /**
     * 钉钉发起OA通知
     *
     * @param dingUserId 评论发送人钉钉userId
     * @param content    评论内容
     * @param url        点击后跳转的地址
     * @param userId     发送人的ding_user_id
     * @throws ApiException
     */
    public void sendOAMessageNotifyOA(String headTitle,
                                    String bodyTitle,
                                    String content,
                                    String dingUserId,
                                    String projectId,
                                    String imageUrl,
                                    String url,
                                    String userId,
                                    List<String> cidList,
                                    Integer businessType,
                                    String btnTitle) {
        DingTalkClient client = new DefaultDingTalkClient("https://oapi.dingtalk.com/message/send_to_conversation");

        InsideConfig insideConfig = new InsideConfig();
        insideConfig.setAppKey(key);
        insideConfig.setAppSecret(secret);
        insideConfig.setCorpId(corpId);

        //钉钉接口accessToken
        Result<String> result = getAccessToken(insideConfig);
        if (!result.getSignal()) {
            return;
        }
        String accessToken = result.getResult();

        for (String cid : cidList) {
            try {
                //会话id，前端选择会话后，传递给后端
                OapiMessageSendToConversationRequest req = new OapiMessageSendToConversationRequest();
                req.setSender(dingUserId);
                req.setCid(cid);

                OapiMessageSendToConversationRequest.Oa oa = new OapiMessageSendToConversationRequest.Oa();
                OapiMessageSendToConversationRequest.Body body = new OapiMessageSendToConversationRequest.Body();
                OapiMessageSendToConversationRequest.Head head = new OapiMessageSendToConversationRequest.Head();
                OapiMessageSendToConversationRequest.ActionCard actionCard = new OapiMessageSendToConversationRequest.ActionCard();
                OapiMessageSendToConversationRequest.BtnJson btnJson = new OapiMessageSendToConversationRequest.BtnJson();
                actionCard.setBtnOrientation("1");
                btnJson.setTitle(btnTitle);
                btnJson.setActionUrl(url);
                actionCard.setBtnJsonList(Arrays.asList(btnJson));

                // 有限制长度
                head.setText(headTitle);
                head.setBgcolor("#000000");
                body.setTitle(bodyTitle);

                //body.setContent(content);
                body.setImage(imageUrl);

                oa.setMessageUrl(url);
                //oa.setPcMessageUrl(url);
                //oa.setMessageUrl("dingtalk://dingtalkclient/page/link?pc_slide=true&url=https%3A%2F%2Fcloud.nti56.com%2Fcloudnest%2Fdcm%2F%3FcorpId%3Dding5033bbcf5247e61b35c2f4657eb6378f%26clientId%3D1010203040506070809%26returnUrl%3Dhttps%3A%2F%2Fcloud.nti56.com%2Fdcm-mobile%2FpagesTabar%2FmiddlePage%2Findex%3FrequestJson%3DeyJpZFR5cGUiOjIsIm1haW50ZW5hbmNlRXhwaXJlU3RhdGUiOjEsIm1lc3NhZ2VUeXBlIjo4fQ%3D%3D");
                oa.setHead(head);
                oa.setBody(body);


                req.setOa(oa);
                req.setMsgtype("oa");
                req.setActionCard(actionCard);


                OapiMessageSendToConversationResponse response = client.execute(req, accessToken);
                if (!response.isSuccess()) {
                    log.error("钉钉分享通知异常:{}", response.getErrmsg());
                }
            } catch (ApiException e) {
                log.error("钉钉发送消息异常:{}", e.getCause());
            }
        }
    }

    public void sendOAMessageNotifyLink(String headTitle,
                                    String bodyTitle,
                                    String dingUserId,
                                    String imageUrl,
                                    String url,
                                    List<String> cidList,
                                    String btnTitle) {
        DingTalkClient client = new DefaultDingTalkClient("https://oapi.dingtalk.com/message/send_to_conversation");

        InsideConfig insideConfig = new InsideConfig();
        insideConfig.setAppKey(key);
        insideConfig.setAppSecret(secret);
        insideConfig.setCorpId(corpId);

        //钉钉接口accessToken
        Result<String> result = getAccessToken(insideConfig);
        if (!result.getSignal()) {
            return;
        }
        String accessToken = result.getResult();

        for (String cid : cidList) {
            try {
                OapiMessageSendToConversationRequest req = new OapiMessageSendToConversationRequest();
                req.setSender(dingUserId);
                req.setCid(cid);
                OapiMessageSendToConversationRequest.Msg msg = new OapiMessageSendToConversationRequest.Msg();
                // link消息
                OapiMessageSendToConversationRequest.Link link = new OapiMessageSendToConversationRequest.Link();
                link.setMessageUrl(url);
                link.setPicUrl(imageUrl);
                link.setText(bodyTitle);
                link.setTitle(headTitle);

                OapiMessageSendToConversationRequest.ActionCard actionCard = new OapiMessageSendToConversationRequest.ActionCard();
                OapiMessageSendToConversationRequest.BtnJson btnJson = new OapiMessageSendToConversationRequest.BtnJson();
                actionCard.setBtnOrientation("1");
                btnJson.setTitle(btnTitle);
                btnJson.setActionUrl(url);
                actionCard.setBtnJsonList(Arrays.asList(btnJson));

                msg.setLink(link);
                msg.setMsgtype("link");
                req.setMsg(msg);
                req.setActionCard(actionCard);

                OapiMessageSendToConversationResponse response = client.execute(req, accessToken);
                if (!response.isSuccess()) {
                    log.error("钉钉分享通知异常:{}", response.getErrmsg());
                }
            } catch (ApiException e) {
                log.error("钉钉发送消息异常:{}", e.getCause());
            }
        }
    }

}
