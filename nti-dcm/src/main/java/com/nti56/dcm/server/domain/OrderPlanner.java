package com.nti56.dcm.server.domain;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

import org.apache.commons.lang3.tuple.Pair;

import com.nti56.dcm.server.domain.enums.CreateBeforeUnitEnum;
import com.nti56.dcm.server.domain.enums.InspectModeEnum;
import com.nti56.dcm.server.domain.enums.OrderCreateStrategyEnum;
import com.nti56.dcm.server.domain.enums.PeriodInspectTypeEnum;
import com.nti56.dcm.server.entity.InspectPlanEntity;
import com.nti56.dcm.server.exception.BizException;
import com.nti56.nlink.common.util.Result;

public class OrderPlanner {
    
    public static interface EachHandler {
        void handle(LocalDateTime time);        
    }

    private static final DateTimeFormatter yyyyMMddHHmmss = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

    private InspectModeEnum inspectMode;

    private LocalDateTime singleInspectTime;

    private LocalDate periodInspectBegin;

    private LocalDate periodInspectEnd;

    private PeriodInspectTypeEnum periodInspectType;

    private LocalTime periodInspectTime;

    private Set<Integer> periodInspectDay;

    private List<LocalDateTime> multiInspectTime;

    private OrderCreateStrategyEnum orderCreateStrategy;

    private Integer createBeforeSeconds;

    private OrderPlanner(){}

    public static Result<OrderPlanner> checkInfo(InspectPlanEntity entity){
        if(entity == null){
            return Result.error("计划不能为空");
        }
        return checkInfo(
            entity.getInspectMode(), 
            entity.getSingleInspectTime(), 
            entity.getPeriodInspectBegin(), 
            entity.getPeriodInspectEnd(), 
            entity.getPeriodInspectType(), 
            entity.getPeriodInspectTime(), 
            entity.getPeriodInspectDay(), 
            entity.getMultiInspectTime(),
            entity.getOrderCreateStrategy(),
            entity.getCreateBeforeNumber(),
            entity.getCreateBeforeUnit()
        );
    }

    public static Result<OrderPlanner> checkInfo(
        Integer inspectModeValue,
        LocalDateTime singleInspectTime,
        LocalDate periodInspectBegin,
        LocalDate periodInspectEnd,
        Integer periodInspectTypeValue,
        LocalTime periodInspectTime,
        String periodInspectDay,
        String multiInspectTime,
        Integer orderCreateStrategyValue,
        Integer createBeforeNumber,
        Integer createBeforeUnitValue
    ){
        OrderPlanner orderPlanner = new OrderPlanner();

        if(inspectModeValue == null){
            return Result.error("检查模式不能为空");
        }
        InspectModeEnum inspectMode = InspectModeEnum.typeOfValue(inspectModeValue);
        if(inspectMode == null){
            return Result.error("检查模式错误");
        }
        orderPlanner.inspectMode = inspectMode;

        switch (inspectMode) {
            case ONCE: {
                if(singleInspectTime == null){
                    return Result.error("单次执行时间不能为空");
                }
                orderPlanner.singleInspectTime = singleInspectTime;
                break;
            }
            case PERIOD: {
                if(periodInspectBegin == null){
                    return Result.error("开始时间不能为空");
                }
                if(periodInspectEnd == null){
                    return Result.error("结束时间不能为空");
                }
                if(periodInspectBegin.isAfter(periodInspectEnd)){
                    throw new BizException("开始时间不能大于结束时间");
                }
                orderPlanner.periodInspectBegin = periodInspectBegin;
                orderPlanner.periodInspectEnd = periodInspectEnd;
                
                PeriodInspectTypeEnum periodInspectType = PeriodInspectTypeEnum.typeOfValue(periodInspectTypeValue);
                if(periodInspectType == null){
                    return Result.error("执行周期错误");
                }
                orderPlanner.periodInspectType = periodInspectType;
                switch (periodInspectType) {
                    case DAY: {
                        if(periodInspectTime == null){
                            return Result.error("执行时间不能为空");
                        }
                        orderPlanner.periodInspectTime = periodInspectTime;
                        break;
                    }
                    case WEEK: {
                        if(periodInspectTime == null){
                            return Result.error("执行时间不能为空");
                        }
                        orderPlanner.periodInspectTime = periodInspectTime;
                        if(periodInspectDay == null || "".equals(periodInspectDay)){
                            return Result.error("执行日不能为空");
                        }
                        orderPlanner.periodInspectDay = parsePeriodInspectDay(periodInspectDay);
                        // todo 检查只能是1-7
                        break;
                    }
                    case MONTH: {
                        if(periodInspectTime == null){
                            return Result.error("执行时间不能为空");
                        }
                        orderPlanner.periodInspectTime = periodInspectTime;
                        if(periodInspectDay == null || "".equals(periodInspectDay)){
                            return Result.error("执行日不能为空");
                        }
                        orderPlanner.periodInspectDay = parsePeriodInspectDay(periodInspectDay);
                        // todo 检查只能是1-31
                        break;
                    }
                    default: {
                        return Result.error("不支持的执行周期类型");
                    }
                }
                break;
            }
            case MULTI: {
                if(multiInspectTime == null || "".equals(multiInspectTime)){
                    return Result.error("执行时间不能为空");
                }
                Result<List<LocalDateTime>> res = parseMultiInspectTime(multiInspectTime);
                if(!res.getSignal()){
                    return Result.error(res.getMessage());
                }
                orderPlanner.multiInspectTime = res.getResult();
                break;
            }
            default: {
                return Result.error("不支持的模式");
            }
        }
        
        if(orderCreateStrategyValue == null){
            return Result.error("工单生成策略不能为空");
        }
        OrderCreateStrategyEnum orderCreateStrategy = OrderCreateStrategyEnum.typeOfValue(orderCreateStrategyValue);
        if(orderCreateStrategy == null){
            return Result.error("工单生成策略错误");
        }
        orderPlanner.orderCreateStrategy = orderCreateStrategy;

        if(OrderCreateStrategyEnum.BEFORE.equals(orderCreateStrategy)){
            if(createBeforeNumber == null){
                return Result.error("提前生成数不能为空");
            }
            if(createBeforeNumber < 0){
                return Result.error("提前生成数不能小于0");
            }
            if(createBeforeUnitValue == null){
                return Result.error("提前生成单位不能为空");
            }
            CreateBeforeUnitEnum createBeforeUnit = CreateBeforeUnitEnum.typeOfValue(createBeforeUnitValue);
            if(createBeforeUnit == null){
                return Result.error("提前生成单位不能为空");
            }
            if(CreateBeforeUnitEnum.DAY.equals(createBeforeUnit)){
                orderPlanner.createBeforeSeconds = createBeforeNumber * 24 * 60 * 60;
            }else if(CreateBeforeUnitEnum.HOUR.equals(createBeforeUnit)){
                orderPlanner.createBeforeSeconds = createBeforeNumber * 60 * 60;
            }else{
                return Result.error("提前生成单位错误");
            }
        }

        return Result.ok(orderPlanner);
    }

    public static Result<List<LocalDateTime>> parseMultiInspectTime(String multiInspectTime){
        String[] split = multiInspectTime.split(",");
        if(split == null || split.length <= 0){
            return Result.error("执行时间不能为空");
        }
        Set<LocalDateTime> timeSet = new HashSet<>();
        List<LocalDateTime> list = new ArrayList<>();
        for(String s:split){
            try {
                LocalDateTime inspectTime = LocalDateTime.parse(s, yyyyMMddHHmmss);
                if(timeSet.contains(inspectTime)){
                    return Result.error("多次执行的时间不能为重复：" + s);
                }
                timeSet.add(inspectTime);
                list.add(inspectTime);
            } catch (Exception e) {
                return Result.error("多次执行时间格式错误：" + s);
            }
        }
        list.sort(Comparator.naturalOrder());
        return Result.ok(list);
    }

    public void iterateEach(EachHandler handler){
        switch (inspectMode) {
            case ONCE: {
                handler.handle(singleInspectTime);
                break;
            }
            case PERIOD: {
                switch (periodInspectType) {
                    case DAY: {
                        LocalDate tmp = periodInspectBegin;
                        while(tmp.isBefore(periodInspectEnd) || tmp.isEqual(periodInspectEnd)){
                            LocalDateTime planInspectTime = LocalDateTime.of(tmp, periodInspectTime);
                            handler.handle(planInspectTime);
                            tmp = tmp.plusDays(1);
                        }
                        break;
                    }
                    case WEEK: {
                        LocalDate tmp = periodInspectBegin;
                        while(tmp.isBefore(periodInspectEnd) || tmp.isEqual(periodInspectEnd)){
                            Integer dayOfWeek = tmp.getDayOfWeek().getValue();
                            if(periodInspectDay.contains(dayOfWeek)){
                                LocalDateTime planInspectTime = LocalDateTime.of(tmp, periodInspectTime);
                                handler.handle(planInspectTime);
                            }
                            tmp = tmp.plusDays(1);
                        }
                        break;
                    }
                    case MONTH: {
                        LocalDate tmp = periodInspectBegin;
                        while(tmp.isBefore(periodInspectEnd) || tmp.isEqual(periodInspectEnd)){
                            Integer dayOfMonth = tmp.getDayOfMonth();
                            if(periodInspectDay.contains(dayOfMonth)){
                                LocalDateTime planInspectTime = LocalDateTime.of(tmp, periodInspectTime);
                                handler.handle(planInspectTime);
                            }
                            tmp = tmp.plusDays(1);
                        }
                        break;
                    }
                    default:
                        break;
                }
                break;
            }
            case MULTI: {
                for(LocalDateTime planInspectTime:multiInspectTime){
                    handler.handle(planInspectTime);
                }
                break;
            }
            default:
                break;
        }
    }

    public LocalDateTime getFirstCreateTime() {
        Pair<LocalDateTime, LocalDateTime> pair = nextCreatableTimeAfter(
            LocalDateTime.of(1970, 1, 1, 0, 0,0)
        );
        if(pair == null){
            return null;
        }
        return pair.getLeft();
    }

    /**
     * 计算从beginTime（不含）到endTime（不含）所有可以生成的工单的时间
     * @param beginTime 开始时间
     * @param endTime 结束时间
     * @return List<生成时间, 执行时间>
     */
    public List<Pair<LocalDateTime, LocalDateTime>> listCreatableTimeBetween(
        LocalDateTime beginTime, LocalDateTime endTime
    ){
        if(OrderCreateStrategyEnum.IMMEDIATE.equals(orderCreateStrategy)){
            throw new RuntimeException("不支持立即生成策略");
        }
        if(beginTime == null){
            throw new RuntimeException("开始时间不能为空");
        }
        if(endTime == null){
            throw new RuntimeException("结束时间不能为空");
        }
        List<Pair<LocalDateTime, LocalDateTime>> list = new ArrayList<>();

        switch (inspectMode) {
            case ONCE: {
                LocalDateTime planInspectTime = singleInspectTime;
                LocalDateTime orderCreateTime = planInspectTime.minusSeconds(createBeforeSeconds);
                if(orderCreateTime.isAfter(beginTime) 
                    && orderCreateTime.isBefore(endTime)
                ){
                    list.add(Pair.of(orderCreateTime, planInspectTime));
                }
                break;
            }
            case PERIOD: {
                switch (periodInspectType) {
                    case DAY: {
                        
                        LocalDate tmp = periodInspectBegin;
                        LocalDate beginDate = beginTime.plusSeconds(createBeforeSeconds).toLocalDate();
                        if(beginDate.isAfter(tmp)){
                            tmp = beginDate;
                        }
                        LocalDate endDate = endTime.plusSeconds(createBeforeSeconds).toLocalDate();
                        while(tmp.isBefore(periodInspectEnd) || tmp.isEqual(periodInspectEnd)){
                            if(tmp.isAfter(endDate)){
                                break;
                            }
                            LocalDateTime planInspectTime = LocalDateTime.of(tmp, periodInspectTime);
                            LocalDateTime orderCreateTime = planInspectTime.minusSeconds(createBeforeSeconds);
                            if(orderCreateTime.isAfter(beginTime) 
                                && orderCreateTime.isBefore(endTime)
                            ){
                                list.add(Pair.of(orderCreateTime, planInspectTime));
                            }
                            tmp = tmp.plusDays(1);
                        }
                        break;
                    }
                    case WEEK: {
                        LocalDate tmp = periodInspectBegin;
                        LocalDate beginDate = beginTime.plusSeconds(createBeforeSeconds).toLocalDate();
                        if(beginDate.isAfter(tmp)){
                            tmp = beginDate;
                        }
                        LocalDate endDate = endTime.plusSeconds(createBeforeSeconds).toLocalDate();
                        while(tmp.isBefore(periodInspectEnd) || tmp.isEqual(periodInspectEnd)){
                            if(tmp.isAfter(endDate)){
                                break;
                            }
                            Integer dayOfWeek = tmp.getDayOfWeek().getValue();
                            if(periodInspectDay.contains(dayOfWeek)){
                                LocalDateTime planInspectTime = LocalDateTime.of(tmp, periodInspectTime);
                                LocalDateTime orderCreateTime = planInspectTime.minusSeconds(createBeforeSeconds);
                                if(orderCreateTime.isAfter(beginTime) 
                                    && orderCreateTime.isBefore(endTime)
                                ){
                                    list.add(Pair.of(orderCreateTime, planInspectTime));
                                }
                            }
                            tmp = tmp.plusDays(1);
                        }
                        break;
                    }
                    case MONTH: {
                        LocalDate tmp = periodInspectBegin;
                        LocalDate beginDate = beginTime.plusSeconds(createBeforeSeconds).toLocalDate();
                        if(beginDate.isAfter(tmp)){
                            tmp = beginDate;
                        }
                        LocalDate endDate = endTime.plusSeconds(createBeforeSeconds).toLocalDate();
                        while(tmp.isBefore(periodInspectEnd) || tmp.isEqual(periodInspectEnd)){
                            if(tmp.isAfter(endDate)){
                                break;
                            }
                            Integer dayOfMonth = tmp.getDayOfMonth();
                            if(periodInspectDay.contains(dayOfMonth)){
                                LocalDateTime planInspectTime = LocalDateTime.of(tmp, periodInspectTime);
                                LocalDateTime orderCreateTime = planInspectTime.minusSeconds(createBeforeSeconds);
                                if(orderCreateTime.isAfter(beginTime) 
                                    && orderCreateTime.isBefore(endTime)
                                ){
                                    list.add(Pair.of(orderCreateTime, planInspectTime));
                                }
                            }
                            tmp = tmp.plusDays(1);
                        }
                        break;
                    }
                    default:
                        break;
                }
                break;
            }
            case MULTI: {
                for(LocalDateTime planInspectTime:multiInspectTime){
                    LocalDateTime orderCreateTime = planInspectTime.minusSeconds(createBeforeSeconds);
                    if(orderCreateTime.isAfter(beginTime) 
                        && orderCreateTime.isBefore(endTime)
                    ){
                        list.add(Pair.of(orderCreateTime, planInspectTime));
                    }
                }
                break;
            }
            default:
                break;
        }
        return list;
    }
    
    /**
     * 计算从beginTime（不含）后第一个生成工单的时间
     * @param beginTime 开始时间
     * @return <生成时间, 执行时间>
     */
    public Pair<LocalDateTime, LocalDateTime> nextCreatableTimeAfter(
        LocalDateTime beginTime
    ){
        if(OrderCreateStrategyEnum.IMMEDIATE.equals(orderCreateStrategy)){
            throw new RuntimeException("不支持立即生成策略");
        }
        if(beginTime == null){
            throw new RuntimeException("开始时间不能为空");
        }

        switch (inspectMode) {
            case ONCE: {
                LocalDateTime planInspectTime = singleInspectTime;
                LocalDateTime orderCreateTime = planInspectTime.minusSeconds(createBeforeSeconds);
                if(orderCreateTime.isAfter(beginTime)){
                    return Pair.of(orderCreateTime, planInspectTime);
                }
                break;
            }
            case PERIOD: {
                switch (periodInspectType) {
                    case DAY: {
                        
                        LocalDate tmp = periodInspectBegin;
                        LocalDate beginDate = beginTime.plusSeconds(createBeforeSeconds).toLocalDate();
                        if(beginDate.isAfter(tmp)){
                            tmp = beginDate;
                        }
                        while(tmp.isBefore(periodInspectEnd) || tmp.isEqual(periodInspectEnd)){
                            LocalDateTime planInspectTime = LocalDateTime.of(tmp, periodInspectTime);
                            LocalDateTime orderCreateTime = planInspectTime.minusSeconds(createBeforeSeconds);
                            if(orderCreateTime.isAfter(beginTime)){
                                return Pair.of(orderCreateTime, planInspectTime);
                            }
                            tmp = tmp.plusDays(1);
                        }
                        break;
                    }
                    case WEEK: {
                        LocalDate tmp = periodInspectBegin;
                        LocalDate beginDate = beginTime.plusSeconds(createBeforeSeconds).toLocalDate();
                        if(beginDate.isAfter(tmp)){
                            tmp = beginDate;
                        }
                        while(tmp.isBefore(periodInspectEnd) || tmp.isEqual(periodInspectEnd)){
                            Integer dayOfWeek = tmp.getDayOfWeek().getValue();
                            if(periodInspectDay.contains(dayOfWeek)){
                                LocalDateTime planInspectTime = LocalDateTime.of(tmp, periodInspectTime);
                                LocalDateTime orderCreateTime = planInspectTime.minusSeconds(createBeforeSeconds);
                                if(orderCreateTime.isAfter(beginTime)){
                                    return Pair.of(orderCreateTime, planInspectTime);
                                }
                            }
                            tmp = tmp.plusDays(1);
                        }
                        break;
                    }
                    case MONTH: {
                        LocalDate tmp = periodInspectBegin;
                        LocalDate beginDate = beginTime.plusSeconds(createBeforeSeconds).toLocalDate();
                        if(beginDate.isAfter(tmp)){
                            tmp = beginDate;
                        }
                        while(tmp.isBefore(periodInspectEnd) || tmp.isEqual(periodInspectEnd)){
                            Integer dayOfMonth = tmp.getDayOfMonth();
                            if(periodInspectDay.contains(dayOfMonth)){
                                LocalDateTime planInspectTime = LocalDateTime.of(tmp, periodInspectTime);
                                LocalDateTime orderCreateTime = planInspectTime.minusSeconds(createBeforeSeconds);
                                if(orderCreateTime.isAfter(beginTime)){
                                    return Pair.of(orderCreateTime, planInspectTime);
                                }
                            }
                            tmp = tmp.plusDays(1);
                        }
                        break;
                    }
                    default:
                        break;
                }
                break;
            }
            case MULTI: {
                for(LocalDateTime planInspectTime:multiInspectTime){
                    LocalDateTime orderCreateTime = planInspectTime.minusSeconds(createBeforeSeconds);
                    if(orderCreateTime.isAfter(beginTime)){
                        return Pair.of(orderCreateTime, planInspectTime);
                    }
                }
                break;
            }
            default:
                break;
        }
        return null;
    }
    
    private static Set<Integer> parsePeriodInspectDay(String periodInspectDayStr){
        Set<Integer> periodInspectDaySet = new HashSet<>();
        if(periodInspectDayStr != null && !"".equals(periodInspectDayStr)){
            String[] split = periodInspectDayStr.split(",");
            for(String s:split){
                periodInspectDaySet.add(Integer.valueOf(s));
            }
        }
        return periodInspectDaySet;
    }

}
