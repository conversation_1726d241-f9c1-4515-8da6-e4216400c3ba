package com.nti56.dcm.server.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 
 * @TableName device_purchase
 */
@TableName(value ="device_purchase")
@Data
@AllArgsConstructor
@NoArgsConstructor
public class DevicePurchaseEntity implements Serializable {
    /**
     * 采购记录ID
     */
    @TableId
    private Long id;

    /**
     * 设备ID
     */
    private Long deviceId;

    /**
     * 关联采购单ID
     */
    private Long purchaseId;

    /**
     * 供应商
     */
    private String vendor;

    /**
     * 采购部门
     */
    private String buyDept;

    /**
     * 采购人员
     */
    private String buyer;

    /**
     * 采购日期
     */
    private Date buyDatetime;

    /**
     * 出场日期
     */
    private Date manufactureDatetime;

    /**
     * 生产商
     */
    private String manufacturer;

    /**
     * 使用寿命
     */
    private Integer serviceLife;

    /**
     * 采购价格
     */
    private BigDecimal purchasePrice;

    /**
     * 租户ID
     */
    private Long tenantId;

    /**
     * 工程ID
     */
    private Long engineeringId;

    /**
     * 模块ID
     */
    private Long moduleId;

    /**
     * 空间ID
     */
    private Long spaceId;

    /**
     * 版本号
     */
    private Integer version;

    /**
     * 删除
     */
    private Integer deleted;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;

}