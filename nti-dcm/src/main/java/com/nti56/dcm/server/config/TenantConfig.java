package com.nti56.dcm.server.config;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.config.annotation.AsyncSupportConfigurer;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

import com.nti56.common.tenant.TenantIsolationInterceptor;


/**
 * 类说明: 
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2023-11-08 11:53:07
 * @since JDK 1.8
 */
@Configuration
public class TenantConfig implements WebMvcConfigurer {

    @Autowired
    private TenantIsolationInterceptor tenantIsolationInterceptor;

    @Override
    public void configureAsyncSupport(AsyncSupportConfigurer configurer) {
        configurer.setDefaultTimeout(1200000); // 设置超时时间为120秒
//        configurer.setDefaultTimeout(-1); // 禁用超时限制
    }

    @Override
    public void addInterceptors(InterceptorRegistry registry) {
        registry.addInterceptor(tenantIsolationInterceptor)
                .excludePathPatterns("/v3/**")
                .addPathPatterns(
                        "/**"
                       /* ,"/app/secret/**",
                        "/white/list/**"*/
                );
    }
}
