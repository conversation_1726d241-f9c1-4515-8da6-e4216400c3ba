package com.nti56.dcm.server.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.nti56.nlink.common.dto.TenantIsolation;
import com.nti56.nlink.common.util.BeanUtilsIntensifier;
import com.nti56.nlink.common.util.PageParam;
import com.nti56.common.util.R;
import com.nti56.nlink.common.util.Result;
import com.nti56.nlink.common.util.ServiceCodeEnum;
import com.nti56.dcm.server.service.StocktakingResultItemService;
import com.nti56.dcm.server.entity.StocktakingResultItemEntity;
import com.nti56.dcm.server.model.dto.StocktakingResultItemCreateDto;
import com.nti56.dcm.server.model.dto.StocktakingResultItemDto;
import com.nti56.dcm.server.model.dto.StocktakingResultItemParam;
import com.nti56.dcm.server.model.vo.StocktakingResultItemVo;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Optional;
import java.util.function.Function;

import lombok.extern.slf4j.Slf4j;


/**
 * 盘点结果项表 控制器
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2024-05-07 11:04:07
 * @since JDK 1.8
 */
@RestController
@RequestMapping("/stocktaking-result-item")
@Slf4j
public class StocktakingResultItemController {

    @Autowired
    private StocktakingResultItemService service;

    /**
     * 获取盘点项列表分页
     * @param pageParam 分页参数
     */
    @GetMapping("/page")
    public R<Page<StocktakingResultItemVo>> page(
        @RequestHeader("dcm_headers") TenantIsolation tenant, 
        PageParam pageParam,
        StocktakingResultItemParam params
    ){
        Page<StocktakingResultItemVo> page = pageParam.toPage(StocktakingResultItemVo.class);
        Result<Page<StocktakingResultItemVo>> result = service.getPage(tenant.getTenantId(), params,page);
        return R.result(result);
    }

    /**
     * 提交盘点结果
     */
    @PutMapping("/submit-result-item")
    public R<Void> submitResultItem(
        @RequestHeader("dcm_headers") TenantIsolation tenant, 
        @RequestBody StocktakingResultItemDto dto
    ){
        Result<Void> result = service.submitResultItem(tenant.getTenantId(), dto);
        return R.result(result);
    }

    /**
     * 添加盘点货位
     */
    @PostMapping("/create")
    public R<Void> create(
        @RequestHeader("dcm_headers") TenantIsolation tenant, 
        @RequestBody StocktakingResultItemCreateDto dto
    ){
        Result<Void> result = service.create(tenant.getTenantId(), dto);
        return R.result(result);
    }

    // /**
    //  * 获取列表
    //  */
    // @GetMapping("/list")
    // public R<List<StocktakingResultItemEntity>> list(@RequestHeader("dcm_headers") TenantIsolation tenant, StocktakingResultItemEntity entity){
    //     Result<List<StocktakingResultItemEntity>> result = service.list(tenant.getTenantId(), entity);
    //     return R.result(result);
    // }

    // /**
    //  * 删除对象
    //  * @param entityId 对象id
    //  */
    // @DeleteMapping("/{entityId}")
    // public R<Void> delete(@RequestHeader("dcm_headers") TenantIsolation tenant, @PathVariable Long entityId){
    //     Result<Void> result = service.deleteById(tenant.getTenantId(), entityId);
    //     return R.result(result);
    // }

    // /**
    //  * 获取对象
    //  * @param entityId 对象id
    //  */
    // @GetMapping("/{entityId}")
    // public R<StocktakingResultItemEntity> get(@RequestHeader("dcm_headers") TenantIsolation tenant, @PathVariable Long entityId){
    //     Result<StocktakingResultItemEntity> result = service.getById(tenant.getTenantId(), entityId);
    //     return R.result(result);
    // }
    
}
