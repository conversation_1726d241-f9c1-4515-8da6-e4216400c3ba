package com.nti56.dcm.server.controller;


import com.nti56.common.util.R;
import com.nti56.dcm.server.entity.FaultTypeEntity;
import com.nti56.dcm.server.model.dto.FaultTypeDto;
import com.nti56.dcm.server.model.dto.FaultTypeTreeDto;
import com.nti56.dcm.server.service.IFaultTypeService;
import com.nti56.nlink.common.dto.TenantIsolation;
import com.nti56.nlink.common.util.Result;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <p>
 * 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-02-22
 */
@RestController
@RequestMapping("/faultType")
@Tag(name = "故障类型")
public class FaultTypeController {

    @Autowired
    private IFaultTypeService faultTypeService;


    @GetMapping("/getTree")
    @Operation(summary = "获取故障类型树形数据")
    public R getTree(@RequestHeader("dcm_headers") TenantIsolation tenantIsolation, FaultTypeDto dto) {
        Result<List<FaultTypeTreeDto>> result = faultTypeService.getTree(tenantIsolation, dto);
        return R.result(result);
    }


    @PostMapping("")
    @Operation(summary = "新增故障类型")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "返回值",
                    content = {@Content(mediaType = MediaType.APPLICATION_JSON_VALUE,
                            schema = @Schema(implementation = FaultTypeEntity.class)
                    )})
    })
    public R createFaultType(@RequestHeader("dcm_headers") TenantIsolation tenantIsolation,
                              @RequestBody @Validated FaultTypeDto dto) {
        return R.result(faultTypeService.create(dto, tenantIsolation));
    }


    @PutMapping("/{id}")
    @Operation(summary = "修改故障类型")
    public R editFaultType(@RequestHeader("dcm_headers") TenantIsolation tenantIsolation,
                         @RequestBody @Validated FaultTypeDto dto) {
        return R.result(faultTypeService.edit(dto, tenantIsolation));
    }

    @DeleteMapping("/{id}")
    @Operation(summary = "根据id删除故障类型")
    public R deleteFaultType(@RequestHeader("dcm_headers") TenantIsolation tenantIsolation,
                              @PathVariable Long id) {
        return R.result(faultTypeService.deleteById(id, tenantIsolation));
    }

}
