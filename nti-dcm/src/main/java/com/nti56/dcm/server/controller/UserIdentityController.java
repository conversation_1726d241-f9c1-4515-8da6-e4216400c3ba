package com.nti56.dcm.server.controller;

import com.nti56.dcm.server.service.UserIdentityService;
import com.nti56.nlink.common.dto.TenantIsolation;
import com.nti56.nlink.common.util.*;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.apache.ibatis.annotations.Param;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * <p>
 * 用户默认身份表 控制器
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @Ddate 2024-04-09 11:22:55
 * @since JDK 1.8
 */
@RestController
@RequestMapping("/userIdentity")
@Tag(name = "用户默认身份表模块")
public class UserIdentityController {

    @Autowired
    private UserIdentityService service;

    @PostMapping("setting")
    @Operation(summary = "设置默认身份")
    public R setting(@RequestHeader("dcm_headers") TenantIsolation tenantIsolation, @Param("idType") Integer idType){
        return R.result(service.setting(tenantIsolation, idType));
    }
    
}
