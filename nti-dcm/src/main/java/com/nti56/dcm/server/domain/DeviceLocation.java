package com.nti56.dcm.server.domain;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;

import com.google.common.collect.HashMultiset;
import com.google.common.collect.Multiset;
import com.nti56.dcm.server.constant.DictConstant;
import com.nti56.dcm.server.domain.enums.ConnectOtEnum;
import com.nti56.dcm.server.domain.enums.DeviceStateEnum;
import com.nti56.dcm.server.entity.DeviceEntity;
import com.nti56.dcm.server.entity.DeviceLocationEntity;
import com.nti56.dcm.server.model.dto.DeviceLocationDto;
import com.nti56.dcm.server.model.vo.DeviceVo;
import com.nti56.nlink.common.base.FieldValue;
import com.nti56.nlink.common.base.UniqueConstraint;
import com.nti56.nlink.common.fetcher.CommonFetcher;
import com.nti56.nlink.common.fetcher.Preloader;
import com.nti56.nlink.common.util.Result;

import lombok.Getter;

import org.springframework.beans.BeanUtils;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;

public class DeviceLocation {

    @Getter
    private DeviceLocationEntity entity;

    @Getter
    private Boolean isRoot;

    @Getter
    private Long id;

    @Getter
    private String name;

    // 直接子空间领域
    @Getter
    private List<DeviceLocation> subs;

    // 直接子设备实体
    @Getter
    private List<DeviceVo> subDevices;

    // 直接子设备状态计数器
    private Multiset<Integer> subDeviceStateCounter;

    // 直接子设备状态统计
    @Getter
    private DeviceStateCount subDeviceStateCount;

    // 所有子设备状态统计
    @Getter
    private DeviceStateCount allDeviceStateCount;

    private static final UniqueConstraint locationUniqueConstraint = new UniqueConstraint("location");
    private static final UniqueConstraint locationParentConstraint = new UniqueConstraint("parent_id");

    public static Result<DeviceLocation> checkCreate(DeviceLocationDto dto, CommonFetcher commonFetcher) {
        if (StrUtil.isBlank(dto.getLocation())) {
            return Result.error("空间名称不能为空");
        }

        // 校验同级别下是否存在设备空间重名
        Optional<DeviceLocationEntity> repeat = commonFetcher.list("parent_id", dto.getParentId(), DeviceLocationEntity.class)
                .stream()
                .filter(entity -> ObjectUtil.equals(entity.getIdType(), dto.getIdType())&& entity.getLocation().equals(dto.getLocation()))
                .findFirst();
        if (repeat.isPresent()){
            return Result.error("同级别下已存在空间：" + dto.getLocation());
        }
        /*if(dto.getParentId() != null){
            if (exceedsInheritanceDepth(dto.getParentId(), 4, commonFetcher)) {
                return Result.error("继承层级最多不能超过5层");
            }
        }*/
        DeviceLocationEntity entity = new DeviceLocationEntity();
        BeanUtils.copyProperties(dto, entity);
        DeviceLocation deviceLocation = new DeviceLocation();
        deviceLocation.entity = entity;
        return Result.ok(deviceLocation);
    }

    private static boolean exceedsInheritanceDepth(Long parentId, int maxDepth, CommonFetcher commonFetcher) {
        int depth = 0;
        while (parentId != null && depth <= maxDepth) {
            UniqueConstraint.Unique unique = locationParentConstraint.buildUnique(
                    new FieldValue(parentId)
            );
            DeviceLocationEntity parent = commonFetcher.get(unique, DeviceLocationEntity.class);
            if (parent != null) {
                parentId = parent.getParentId();
                depth++;
            } else {
                parentId = null; // 停止循环，因为找不到父类型
            }
        }
        return depth > maxDepth;
    }

    public static Result<DeviceLocation> checkUpdate(DeviceLocationDto dto, CommonFetcher commonFetcher) {

        if (Objects.isNull(dto.getId())) {
            return Result.error("空间ID不能为空！");
        }

        if (StrUtil.isBlank(dto.getLocation())) {
            return Result.error("空间名称不能为空！");
        }
        DeviceLocationEntity oldEntity = commonFetcher.get(dto.getId(), DeviceLocationEntity.class);
        if (Objects.isNull(oldEntity)) {
            return Result.error("不存在的空间！");
        }

        // 校验修改后同级别下是否存在设备空间重名
        Optional<DeviceLocationEntity> repeat = commonFetcher.list("parent_id", oldEntity.getParentId(), DeviceLocationEntity.class)
                .stream()
                .filter(entity -> ObjectUtil.equals(entity.getIdType(), dto.getIdType())&& !entity.getId().equals(oldEntity.getId()) && entity.getLocation().equals(dto.getLocation()))
                .findFirst();
        if (repeat.isPresent()){
            return Result.error("同级别下已存在空间：" + dto.getLocation());
        }

        oldEntity.setLocation(dto.getLocation());
        DeviceLocation deviceLocation = new DeviceLocation();
        deviceLocation.entity = oldEntity;
        return Result.ok(deviceLocation);
    }

    public static Result<List<Long>> checkDelete(Long id, CommonFetcher commonFetcher) {

        Result<DeviceLocation> deviceLocationResult = checkInfo(id, commonFetcher);
        if (!deviceLocationResult.getSignal()) {
            return Result.error(deviceLocationResult.getMessage());
        }
        DeviceLocation deviceLocation = deviceLocationResult.getResult();
        deviceLocation.loadSubs(commonFetcher);
        // 所有空间id
        List<Long> locationIdList = new ArrayList<>();
        // 本空间id
        locationIdList.add(deviceLocation.getId());
        // 子孙空间id
        List<DeviceLocation> flattenSubs = deviceLocation.getFlattenSubs();
        List<Long> flattenSubIdList = flattenSubs.stream().map(DeviceLocation::getId).collect(Collectors.toList());
        locationIdList.addAll(flattenSubIdList);
        if (locationIdList.size()>1){
            return Result.error("空间下存在子空间，无法删除");
        }
        // 获取所有空间下的设备
        Preloader<DeviceEntity> preloader = commonFetcher.preloader(DeviceEntity.class)
                .preload("device_location_id", locationIdList);
        if (CollectionUtil.isNotEmpty(preloader.list())){
            return Result.error("空间下存在设备，无法删除");
        }
        return Result.ok(flattenSubIdList);
    }

    public static Result<DeviceLocation> checkInfo(Long id, CommonFetcher commonFetcher) {
        if (id == null) {
            // 
            DeviceLocation rootDeviceLocation = new DeviceLocation();
            rootDeviceLocation.isRoot = true;

            return Result.ok(rootDeviceLocation);
        }
        DeviceLocationEntity existLocation = commonFetcher.get(id, DeviceLocationEntity.class);
        if (Objects.isNull(existLocation)) {
            return Result.error("空间不存在，无法删除！");
        }
        DeviceLocation deviceLocation = new DeviceLocation();
        deviceLocation.isRoot = false;
        deviceLocation.entity = existLocation;
        deviceLocation.id = existLocation.getId();
        deviceLocation.name = existLocation.getLocation();
        return Result.ok(deviceLocation);
    }


    /**
     * 加载子孙空间
     */
    public Result<Void> loadSubs(CommonFetcher commonFetcher) {
        List<DeviceLocationEntity> subEntityList;
        if (isRoot) {
            subEntityList = commonFetcher.list("parent_id", null, DeviceLocationEntity.class);
        } else {
            subEntityList = commonFetcher.list("parent_id", entity.getId(), DeviceLocationEntity.class);
        }
        this.subs = new ArrayList<>();
        if (subEntityList != null && subEntityList.size() > 0) {
            for (DeviceLocationEntity subEntity : subEntityList) {
                Result<DeviceLocation> r = checkInfo(subEntity.getId(), commonFetcher);
                if (!r.getSignal()) {
                    return Result.error(r.getMessage());
                }
                DeviceLocation sub = r.getResult();
                sub.loadSubs(commonFetcher);
                this.subs.add(sub);
            }
        }
        return Result.ok();
    }

    /**
     * 获取打平的子孙空间位置，不含自己
     */
    public List<DeviceLocation> getFlattenSubs() {
        List<DeviceLocation> flattenSubs = new ArrayList<>();
        if (subs == null || subs.size() <= 0) {
            return flattenSubs;
        }
        for (DeviceLocation sub : subs) {
            flattenSubs.add(sub);
            List<DeviceLocation> subFlattenSubs = sub.getFlattenSubs();
            if (subFlattenSubs != null && subFlattenSubs.size() > 0) {
                flattenSubs.addAll(subFlattenSubs);
            }
        }
        return flattenSubs;
    }

    /**
     * 加载设备，自己空间和子孙空间下的设备
     */
    public Result<Void> loadDevices(CommonFetcher commonFetcher, Boolean needPmo, CommonFetcher pmoCommonFetcher) {
        // 所有空间id
        List<Long> locationIdList = new ArrayList<>();
        // 本空间id
        if (!isRoot) {
            locationIdList.add(entity.getId());
        }
        // 子孙空间id
        List<DeviceLocation> flattenSubs = getFlattenSubs();
        flattenSubs.forEach(t -> {
            locationIdList.add(t.getEntity().getId());
        });
        // 获取所有空间下的设备
        Preloader<DeviceEntity> preloader = commonFetcher.preloader(DeviceEntity.class)
                .preload("device_location_id", locationIdList);
        Preloader<DeviceEntity> pmoDevices;
        if (needPmo){
            pmoDevices = pmoCommonFetcher.preloader(DeviceEntity.class)
                    .preload("inner_location_id", locationIdList);
        } else {
            pmoDevices = null;
        }
        flattenSubs.forEach(t -> {
            List<DeviceEntity> deviceEntities = preloader.filter("device_location_id", t.getId());
            if (pmoDevices!=null){
                List<DeviceEntity> pmoDeviceEntities = pmoDevices.filter("inner_location_id", t.getId());
                if (CollUtil.isNotEmpty(pmoDeviceEntities)){
                    pmoDeviceEntities.forEach(i->i.setTenantId(DictConstant.DEFAULT_TENANT_ID));
                    if (CollUtil.isEmpty(deviceEntities)){
                        deviceEntities = new ArrayList<>();
                    }
                    deviceEntities.addAll(pmoDeviceEntities);
                }
            }

            if (deviceEntities != null && deviceEntities.size() > 0) {
                deviceEntities.sort(Comparator.comparing(DeviceEntity::getCreateTime).reversed());
                t.subDevices = deviceEntities.stream().map(entity -> {
                    DeviceVo vo = new DeviceVo();
                    BeanUtils.copyProperties(entity, vo);
                    return vo;
                }).collect(Collectors.toList());
            }
        });
        // 获取本空间下的设备
        if(isRoot){
            List<DeviceEntity> list = commonFetcher.list("device_location_id", null, DeviceEntity.class);
            if (needPmo){
                List<DeviceEntity> pmoList = pmoCommonFetcher.list("inner_location_id", null, DeviceEntity.class);
                if (CollUtil.isNotEmpty(pmoList)){
                    pmoList.forEach(i->i.setTenantId(DictConstant.DEFAULT_TENANT_ID));
                    if (CollUtil.isEmpty(list)){
                        list = new ArrayList<>();
                    }
                    list.addAll(pmoList);
                }
            }
            if (list != null && list.size() > 0) {
                list.sort(Comparator.comparing(DeviceEntity::getCreateTime).reversed());
                this.subDevices = list.stream().map(entity -> {
                    DeviceVo vo = new DeviceVo();
                    BeanUtils.copyProperties(entity, vo);
                    return vo;
                }).collect(Collectors.toList());
            }
        }else{
            List<DeviceEntity> deviceEntities = preloader.filter("device_location_id", id);

            if (pmoDevices!=null){
                List<DeviceEntity> pmoDeviceEntities = pmoDevices.filter("inner_location_id", id);
                if (CollUtil.isNotEmpty(pmoDeviceEntities)){
                    pmoDeviceEntities.forEach(i->i.setTenantId(DictConstant.DEFAULT_TENANT_ID));
                    if (CollUtil.isEmpty(deviceEntities)){
                        deviceEntities = new ArrayList<>();
                    }
                    deviceEntities.addAll(pmoDeviceEntities);
                }
            }
            if (deviceEntities != null && deviceEntities.size() > 0) {
                deviceEntities.sort(Comparator.comparing(DeviceEntity::getCreateTime).reversed());
                this.subDevices = deviceEntities.stream()
                .map(entity -> {
                    DeviceVo vo = new DeviceVo();
                    BeanUtils.copyProperties(entity, vo);
                    return vo;
                }).collect(Collectors.toList());
            }
        }
        return Result.ok();
    }

    /**
     * 获取打平的子孙设备，含自己的子设备
     */
    public List<DeviceVo> getAllSubDevices() {
        List<DeviceVo> allSubs = new ArrayList<>();
        if (subDevices != null) {
            allSubs.addAll(subDevices);
        }
        if (subs == null || subs.size() <= 0) {
            return allSubs;
        }
        for (DeviceLocation sub : subs) {
            allSubs.addAll(sub.getAllSubDevices());
        }
        return allSubs;
    }

    /**
     * 导入设备状态
     */
    public void importDeviceState(Map<String, Integer> deviceNameStateMap) {
        List<DeviceVo> allSubDevices = getAllSubDevices();
        if (allSubDevices == null || allSubDevices.size() <= 0) {
            return;
        }
        for (DeviceVo vo : allSubDevices) {
            String alias = vo.getDeviceAliasName();
            Integer state = deviceNameStateMap.get(alias);
            DeviceStateEnum stateEnum = DeviceStateEnum.typeOfValue(state);
            if(stateEnum == null){
                if(ConnectOtEnum.CONNECT.getValue().equals(vo.getConnectOt())){
                    vo.setState(DeviceStateEnum.OFF_LINE);
                }
            }else{
                vo.setState(stateEnum);
            }
        }
    }

   
    /**
     * 统计底下设备状态
     */
    public void countDeviceState() {
        subDeviceStateCounter = HashMultiset.create();
        subDeviceStateCount = new DeviceStateCount();
        Integer totalCount = 0;
        if (subDevices != null) {
            for (DeviceVo vo : subDevices) {
                DeviceStateEnum state = vo.getState();
                if (state != null && vo.getConnectOt() != null && ConnectOtEnum.CONNECT.getValue().equals(vo.getConnectOt())) {
                    subDeviceStateCounter.add(state.getValue());
                }
            }
            totalCount = subDevices.stream().filter(t -> {
                return t.getConnectOt() != null && ConnectOtEnum.CONNECT.getValue().equals(t.getConnectOt());
            }).collect(Collectors.toList()).size();
        }

        Integer faultCount = subDeviceStateCounter.count(DeviceStateEnum.FAULT.getValue());
        Integer taskCount = subDeviceStateCounter.count(DeviceStateEnum.TASK.getValue());
        Integer standByCount = subDeviceStateCounter.count(DeviceStateEnum.STAND_BY.getValue());
        Integer offlineCount = subDeviceStateCounter.count(DeviceStateEnum.OFF_LINE.getValue());

        subDeviceStateCount.setTotalCount(totalCount);
        subDeviceStateCount.setFaultCount(faultCount);
        subDeviceStateCount.setTaskCount(taskCount);
        subDeviceStateCount.setStandByCount(standByCount);
        subDeviceStateCount.setOnlineCount(taskCount + standByCount + faultCount);
        subDeviceStateCount.setOfflineCount(totalCount - taskCount - standByCount - faultCount);

        allDeviceStateCount = new DeviceStateCount();
        allDeviceStateCount.addCount(subDeviceStateCount);
        if (subs != null && subs.size() > 0) {
            for (DeviceLocation sub : subs) {
                sub.countDeviceState();
                allDeviceStateCount.addCount(sub.allDeviceStateCount);
            }
        }

    }

    public void filterDevice(String deviceName, String deviceSerialNumber, DeviceStateEnum state) {
        if(subDevices != null){
            subDevices = subDevices.stream()
            .filter(t -> {
                if(deviceName == null || "".equals(deviceName)){
                    return true;
                }
                String name = t.getDeviceName();
                return name.contains(deviceName);
            })
            .filter(t -> {
                if(deviceSerialNumber == null || "".equals(deviceSerialNumber)){
                    return true;
                }
                String number = t.getSerialNumber();
                return number.contains(deviceSerialNumber);
            })
            .filter(t -> {
                if(state == null){
                    return true;
                }
                DeviceStateEnum state2 = t.getState();
                return state.equals(state2);
            })
            .collect(Collectors.toList());
        }
        if (subs != null && subs.size() > 0) {
            for (DeviceLocation sub : subs) {
                sub.filterDevice(deviceName, deviceSerialNumber, state);
            }
        }
    }

    public void filterEmpowerDevice(Set<Long> empowerDeviceIds) {
        if(empowerDeviceIds == null){
            return;
        }
        if(subDevices != null){
            subDevices = subDevices.stream()
            .filter(t -> {
                return empowerDeviceIds.contains(t.getId());
            })
            .collect(Collectors.toList());
        }
        if (subs != null && subs.size() > 0) {
            for (DeviceLocation sub : subs) {
                sub.filterEmpowerDevice(empowerDeviceIds);
            }
        }
    }
}
