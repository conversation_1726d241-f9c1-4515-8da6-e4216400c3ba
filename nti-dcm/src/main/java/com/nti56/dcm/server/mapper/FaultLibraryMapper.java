package com.nti56.dcm.server.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.nti56.dcm.server.entity.FaultLibraryEntity;
import com.nti56.dcm.server.model.dto.FaultLibraryDto;
import com.nti56.dcm.server.model.vo.FaultLibraryVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 故障库表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-04 14:42:27
 */
@Mapper
public interface FaultLibraryMapper extends BaseMapper<FaultLibraryEntity> {

    List<FaultLibraryVo> listFaultLibrary(@Param("dto") FaultLibraryDto dto);

    List<FaultLibraryVo> listFaultLibraryByPmo(@Param("dto") FaultLibraryDto dto);

}
