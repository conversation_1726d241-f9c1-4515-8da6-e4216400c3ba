package com.nti56.dcm.server.controller;


import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.nti56.common.util.R;
import com.nti56.dcm.server.entity.WorkGroupUserEntity;
import com.nti56.dcm.server.model.dto.DeviceLocationDto;
import com.nti56.dcm.server.model.dto.WorkGroupDto;
import com.nti56.dcm.server.model.dto.WorkGroupUserAddDto;
import com.nti56.dcm.server.model.dto.WorkGroupUserEditDto;
import com.nti56.dcm.server.service.IWorkGroupUserService;
import com.nti56.nlink.common.dto.TenantIsolation;
import com.nti56.nlink.common.util.PageParam;
import com.nti56.nlink.common.util.Result;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <p>
 * 班组成员用户表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-02-26
 */
@RestController
@RequestMapping("/workGroupUser")
@Tag(name = "班组成员成员")
public class WorkGroupUserController {

    @Autowired
    private IWorkGroupUserService workGroupUserService;

    @GetMapping("/page")
    @Operation(summary = "获取班组成员分页")
    public R<Page<WorkGroupUserEntity>> locationPage(@RequestHeader("dcm_headers") TenantIsolation tenantIsolation, PageParam pageParam, WorkGroupUserEntity workGroupUserEntity) {
        Page<WorkGroupUserEntity> page = pageParam.toPage(WorkGroupUserEntity.class);
        Result<Page<WorkGroupUserEntity>> result = workGroupUserService.getPage(workGroupUserEntity, page, tenantIsolation);
        return R.result(result);
    }

    @GetMapping("/getAddList")
    @Operation(summary = "获取待新增的班组成员")
    public R getAddList(@RequestHeader("dcm_headers") TenantIsolation tenantIsolation, WorkGroupDto dto) {
        Result<WorkGroupUserAddDto> result = workGroupUserService.getAddList(dto, tenantIsolation);
        return R.result(result);
    }

    @GetMapping("/clearUser")
    @Operation(summary = "清除无用用户")
    public void clearUser() {
         workGroupUserService.clearHasRemovedUser();
    }

    @PostMapping("")
    @Operation(summary = "新增班组成员")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "返回值",
                    content = {@Content(mediaType = MediaType.APPLICATION_JSON_VALUE,
                            schema = @Schema(implementation = WorkGroupUserEntity.class)
                    )})
    })
    public R createWorkGroupUser(@RequestHeader("dcm_headers") TenantIsolation tenantIsolation,
                                 @RequestBody @Validated WorkGroupUserEditDto dto) {
        return R.result(workGroupUserService.create(dto, tenantIsolation));
    }


    @DeleteMapping("/{id}")
    @Operation(summary = "根据id删除班组成员")
    public R deleteWorkGroupUser(@RequestHeader("dcm_headers") TenantIsolation tenantIsolation,
                                 @PathVariable Long id) {
        return R.result(workGroupUserService.deleteById(id, tenantIsolation));
    }

}
