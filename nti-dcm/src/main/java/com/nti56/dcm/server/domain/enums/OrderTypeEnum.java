package com.nti56.dcm.server.domain.enums;

import lombok.Getter;

public enum OrderTypeEnum {
    REPAIR(1, "repair", "维修工单"),
    MAINTENANCE(2, "maintenance", "保养工单"),
    INSPECT(3, "inspect", "巡检工单"),
    REPAIR_REPORT(4, "repairReport", "故障报修"),
    ;

    @Getter
    private Integer value;

    @Getter
    private String name;

    @Getter
    private String nameDesc;

    OrderTypeEnum(Integer value, String name, String nameDesc) {
        this.value = value;
        this.name = name;
        this.nameDesc = nameDesc;
    }

    public static OrderTypeEnum typeOfValue(Integer value){
        OrderTypeEnum[] values = OrderTypeEnum.values();
        for (OrderTypeEnum v : values) {
            if (v.value.equals(value)) {
                return v;
            }
        }
        return null;
    }

    public static OrderTypeEnum typeOfName(String name){
        OrderTypeEnum[] values = OrderTypeEnum.values();
        for (OrderTypeEnum v : values) {
            if (v.name.equals(name)) {
                return v;
            }
        }
        return null;
    }

    public static OrderTypeEnum typeOfNameDesc(String nameDesc){
        OrderTypeEnum[] values = OrderTypeEnum.values();
        for (OrderTypeEnum v : values) {
            if (v.nameDesc.equals(nameDesc)) {
                return v;
            }
        }
        return null;
    }
}
