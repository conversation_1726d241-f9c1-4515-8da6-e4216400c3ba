package com.nti56.dcm.server.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.nti56.nlink.common.dto.TenantIsolation;
import com.nti56.nlink.common.util.BeanUtilsIntensifier;
import com.nti56.nlink.common.util.PageParam;
import com.nti56.common.util.R;
import com.nti56.nlink.common.util.Result;
import com.nti56.nlink.common.util.ServiceCodeEnum;
import com.nti56.dcm.server.service.InspectPlanDeviceStandardService;
import com.nti56.dcm.server.entity.InspectPlanDeviceStandardEntity;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Optional;
import java.util.function.Function;

import lombok.extern.slf4j.Slf4j;


/**
 * 点巡检计划设备标准表 控制器
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2024-03-05 09:33:21
 * @since JDK 1.8
 */
@RestController
@RequestMapping("inspectPlanDeviceStandard")
@Slf4j
public class InspectPlanDeviceStandardController {

    @Autowired
    private InspectPlanDeviceStandardService service;

    /**
     * 获取分页
     * @param pageParam 分页参数
     */
    @GetMapping("/page")
    public R<Page<InspectPlanDeviceStandardEntity>> page(@RequestHeader("dcm_headers") TenantIsolation tenant, PageParam pageParam,InspectPlanDeviceStandardEntity entity){
        Page<InspectPlanDeviceStandardEntity> page = pageParam.toPage(InspectPlanDeviceStandardEntity.class);
        Result<Page<InspectPlanDeviceStandardEntity>> result = service.getPage(tenant.getTenantId(), entity,page);
        return R.result(result);
    }

    /**
     * 获取列表
     */
    @GetMapping("/list")
    public R<List<InspectPlanDeviceStandardEntity>> list(@RequestHeader("dcm_headers") TenantIsolation tenant, InspectPlanDeviceStandardEntity entity){
        Result<List<InspectPlanDeviceStandardEntity>> result = service.list(tenant.getTenantId(), entity);
        return R.result(result);
    }

    /**
     * 创建对象
     */
    @PostMapping("/create")
    public R<InspectPlanDeviceStandardEntity> create(@RequestHeader("dcm_headers") TenantIsolation tenant, @RequestBody InspectPlanDeviceStandardEntity entity){
        Result<InspectPlanDeviceStandardEntity> result = service.save(tenant.getTenantId(), entity);
        return R.result(result);
    }

    /**
     * 更新对象
     */
    @PutMapping("/update")
    public R<Void> update(@RequestHeader("dcm_headers") TenantIsolation tenant, @RequestBody InspectPlanDeviceStandardEntity entity){
        if (BeanUtilsIntensifier.checkBeanAndProperties(entity, InspectPlanDeviceStandardEntity::getId)) {
            return R.error(ServiceCodeEnum.CODE_PARAM_ERROR.getCode());
        }
        Result<Void> result = service.update(tenant.getTenantId(), entity);
        return R.result(result);
    }

    /**
     * 删除对象
     * @param entityId 对象id
     */
    @DeleteMapping("/{entityId}")
    public R<Void> delete(@RequestHeader("dcm_headers") TenantIsolation tenant, @PathVariable Long entityId){
        Result<Void> result = service.deleteById(tenant.getTenantId(), entityId);
        return R.result(result);
    }

    /**
     * 获取对象
     * @param entityId 对象id
     */
    @GetMapping("/{entityId}")
    public R<InspectPlanDeviceStandardEntity> get(@RequestHeader("dcm_headers") TenantIsolation tenant, @PathVariable Long entityId){
        Result<InspectPlanDeviceStandardEntity> result = service.getById(tenant.getTenantId(), entityId);
        return R.result(result);
    }
    
}
