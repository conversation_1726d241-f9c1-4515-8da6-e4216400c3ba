package com.nti56.dcm.server.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 消息通知配置表 实体类
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @Ddate 2024-06-24 14:01:32
 * @since JDK 1.8
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@TableName("message_notice_config")
public class MessageNoticeConfigEntity implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
    * id
    */
    private Long id;

    /**
    * 消息通知策略id
    */
    private Long strategyId;

    /**
     * 身份，1-供应商，2-客户
     */
    private Integer idType;

    /**
    * 租户id
    */
    @TableField(fill = FieldFill.INSERT)
    private Long tenantId;

    /**
    * 工单类型, 1-维修，2-保养，3-巡检，4-报表, 5-备件库存预警
    */
    private Integer orderType;

    /**
    * 配置类型, 1-报修，2-内部，3-外委, 4-内部工单流转，5-外委工单流转   1-日报，2-周报，3-月报
    */
    private Integer configType;

    /**
    * 渠道类型, 0-邮件，1-短信，2-钉钉机器人，3-钉钉卡片，4-企业微信机器人，99-站内信
    */
    private Integer channelType;

    /**
     * 通知类型（单选）, 1-设备故障，2-设备离线，3-低健康设备，4-备件库存预警，5-超时工单，6-收到新工单，7-设备超时未保养，8-设备维保临期，9-设备维保到期，10-设备质保临期，11-设备质保到期，12-设备计划报废临期，13-设备计划报废到期,14-设备在线
     */
    private Integer noticeType;

    /**
     * 报表范围（多选）, 1-客户新增及上云概况，2-设备异常概况，3-维保/质保到期概况，4-客户服务概况
     */
    private String reportRange;

    /**
    * 通知模板ID
    */
    private Long templateId;

    /**
    * 通知范围(人员)
    */
    private String personScope;

    /**
    * 通知范围(角色)
    */
    private String roleScope;

    /**
    * 是否启用, 0-禁用，1-启用
    */
    private Integer status;

    /**
    * 版本号
    */
    @Version
    private Integer version;

    /**
    * 删除
    */
    @TableLogic
    private Integer deleted;

    /**
    * 创建人ID
    */
    @TableField(fill = FieldFill.INSERT)
    private Long creatorId;

    /**
    * 创建人
    */
    @TableField(fill = FieldFill.INSERT)
    private String creator;

    /**
    * 创建时间
    */
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
    * 更新人ID
    */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Long updatorId;

    /**
    * 更新人
    */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private String updator;

    /**
    * 更新时间
    */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    /**
    * 工程ID
    */
    @TableField(fill = FieldFill.INSERT)
    private Long engineeringId;

    /**
    * 模块ID
    */
    @TableField(fill = FieldFill.INSERT)
    private Long moduleId;

    /**
    * 空间ID
    */
    @TableField(fill = FieldFill.INSERT)
    private Long spaceId;



}
