package com.nti56.dcm.server.controller;

import java.time.LocalDateTime;
import java.util.Date;
import java.util.List;
import java.util.Map;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;

import com.nti56.dcm.server.model.vo.CustomerDeviceStateCountVo;
import com.nti56.dcm.server.model.vo.DeviceBlockChartVo;
import com.nti56.dcm.server.model.vo.DeviceStateCountVo;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.nti56.common.util.R;
import com.nti56.dcm.server.model.vo.LocationDeviceStateCountVo;
import com.nti56.dcm.server.model.vo.PageWithStateCount;
import com.nti56.dcm.server.model.vo.PageWithStateCountGroupByCustomer;
import com.nti56.dcm.server.model.vo.PropertyValueWithTime;
import com.nti56.dcm.server.model.vo.StateValueWithTime;
import com.nti56.dcm.server.model.vo.TypeDeviceStateCountVo;
import com.nti56.dcm.server.domain.enums.DeviceCondEnum;
import com.nti56.dcm.server.domain.enums.DeviceOrderByEnum;
import com.nti56.dcm.server.domain.enums.DeviceStateEnum;
import com.nti56.dcm.server.model.result.ITResult;
import com.nti56.dcm.server.model.vo.DeviceStateVo;
import com.nti56.dcm.server.model.vo.DeviceVo;
import com.nti56.dcm.server.model.vo.FileVo;
import com.nti56.dcm.server.service.DeviceChartService;
import com.nti56.nlink.common.dto.TenantIsolation;
import com.nti56.nlink.common.util.Result;

import io.swagger.v3.oas.annotations.Parameter;
import lombok.extern.slf4j.Slf4j;

/**
 * 类说明: 设备心电图控制器
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2023-12-21 10:19:55
 * @since JDK 1.8
 */
@RestController
@RequestMapping("device-chart")
@Slf4j
public class DeviceChartController {

    @Autowired
    DeviceChartService deviceChartService;

    /**
     * 解析时间
     * 
     * @param begin 开始时间，格式：2023-12-26T07:00:00
     * @param end 结束时间，格式：2023-12-26T07:00:00
     * @return 1d-按天 1w-按周 1mo-按月
     */
     @GetMapping("/parse-every")
    public R<String> parseEvery(
        @RequestHeader("dcm_headers") TenantIsolation tenant, 
        @RequestParam("begin") String begin,
        @RequestParam("end") String end
    ){
        Result<String> everyResult = deviceChartService.checkAndParseEvery(begin, end);
        return R.result(everyResult);
    }

    /**
     * 分页过滤查询设备及其状态
     * 
     * @param cond 状态条件，null-全部，1-故障，2-离线
     * @param deviceName 设备名称
     * @param deviceSerialNumber 设备编号
     * @param customerName 客户名称
     * @param customerId 客户id
     * @param orderBy 排序字段，time-时间(默认），name-设备名
     * @param asc 是否正序，false-倒序(默认)， true-正序
     * @param size 分页大小，默认10
     * @param current 当前页，默认1
     */
    @GetMapping("/page-device-with-state")
    public R<PageWithStateCount> pageDeviceWithState(
        @RequestHeader("dcm_headers") TenantIsolation tenant, 
        @RequestParam(value = "cond", defaultValue = "") Integer cond,
        @RequestParam(value = "deviceName", defaultValue = "") String deviceName,
        @RequestParam(value = "deviceSerialNumber", defaultValue = "") String deviceSerialNumber,
        @RequestParam(value = "customerName", defaultValue = "") String customerName,
        @RequestParam(value = "customerId", defaultValue = "") Long customerId,
        @RequestParam(value = "orderBy", defaultValue = "time") String orderBy,
        @RequestParam(value = "asc", defaultValue = "false") Boolean asc,
        @RequestParam(value = "size", defaultValue = "10") @Max(500) Integer size,
        @RequestParam(value = "current", defaultValue = "1") @Min(1) Integer current,
        @RequestParam(value = "startConnectTime", defaultValue = "") @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") Date startConnectTime,
        @RequestParam(value = "endConnectTime", defaultValue = "") @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") Date endConnectTime
    ){
        log.info("page-device-with-state start");
        Result<PageWithStateCount> result = deviceChartService.pageDeviceWithState(
            tenant ,
            DeviceCondEnum.typeOfValue(cond),
            deviceName,
            deviceSerialNumber,
            customerName,
            customerId,
            DeviceOrderByEnum.typeOfName(orderBy), 
            asc,
            size, 
            current,
            startConnectTime,
            endConnectTime
        );
        log.info("page-device-with-state end");
        return R.result(result);
    }

    /**
     * 分页过滤查询设备及其状态，按客户分组
     * 
     * @param cond 状态条件，null-全部，1-故障，2-离线
     * @param deviceName 设备名称
     * @param deviceSerialNumber 设备编号
     * @param customerName 客户名称
     * @param customerId 客户id
     * @param orderBy 排序字段，time-时间(默认），name-设备名
     * @param asc 是否正序，false-倒序(默认)， true-正序
     * @param size 分页大小，默认10
     * @param current 当前页，默认1
     */
    @GetMapping("/page-device-with-state-group-by-customer")
    public R<PageWithStateCountGroupByCustomer> pageDeviceWithStateGroupByCustomer(
        @RequestHeader("dcm_headers") TenantIsolation tenant, 
        @RequestParam(value = "cond", defaultValue = "") Integer cond,
        @RequestParam(value = "deviceName", defaultValue = "") String deviceName,
        @RequestParam(value = "deviceSerialNumber", defaultValue = "") String deviceSerialNumber,
        @RequestParam(value = "customerName", defaultValue = "") String customerName,
        @RequestParam(value = "customerId", defaultValue = "") Long customerId,
        @RequestParam(value = "orderBy", defaultValue = "time") String orderBy,
        @RequestParam(value = "asc", defaultValue = "false") Boolean asc,
        @RequestParam(value = "size", defaultValue = "10") @Max(500) Integer size,
        @RequestParam(value = "current", defaultValue = "1") @Min(1) Integer current,
        @RequestParam(value = "startConnectTime", defaultValue = "") @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") Date startConnectTime,
        @RequestParam(value = "endConnectTime", defaultValue = "") @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") Date endConnectTime
    ){
        Result<PageWithStateCountGroupByCustomer> result = deviceChartService.pageDeviceWithStateGroupByCustomer(
            tenant ,
            DeviceCondEnum.typeOfValue(cond),
            deviceName,
            deviceSerialNumber,
            customerName,
            customerId,
            DeviceOrderByEnum.typeOfName(orderBy), 
            asc,
            size, 
            current,
            startConnectTime,
            endConnectTime
        );
        return R.result(result);
    }

    /**
     * 获取空间下所有设备的当前状态统计
     * 设备总数、在线设备、离线设备、故障设备
     * 
     * @param deviceLocationId 设备位置id
     * @param deviceName 设备名
     * @param deviceSerialNumber 设备编号
     * @param state 状态 1-任务, 2-空闲, 3-故障, 4-离线
     */
    @GetMapping("/device-state-count-of-location")
    public R<LocationDeviceStateCountVo> deviceStateCountOfLocation(
        @RequestHeader("dcm_headers") TenantIsolation tenant, 
        @RequestParam(value = "deviceLocationId", defaultValue = "") Long deviceLocationId, 
        @RequestParam(value = "deviceName", defaultValue = "") String deviceName,
        @RequestParam(value = "deviceSerialNumber", defaultValue = "") String deviceSerialNumber,
        @RequestParam(value = "state", defaultValue = "") Integer state
    ){
        Result<LocationDeviceStateCountVo> result = deviceChartService.deviceStateCountOfLocation(
            tenant,
            deviceLocationId,
            deviceName,
            deviceSerialNumber,
            DeviceStateEnum.typeOfValue(state)
        );
        return R.result(result);
    }

    /**
     * 获取设备类型下所有设备的当前状态统计
     * 设备总数、在线设备、离线设备、故障设备
     * 
     * @param deviceTypeId 设备类型id
     * @param deviceName 设备名
     * @param deviceSerialNumber 设备编号
     * @param state 状态 1-任务, 2-空闲, 3-故障, 4-离线
     */
    @GetMapping("/device-state-count-of-type")
    public R<TypeDeviceStateCountVo> deviceStateCountOfType(
        @RequestHeader("dcm_headers") TenantIsolation tenant, 
        @RequestParam(value = "deviceTypeId", defaultValue = "") Long deviceTypeId, 
        @RequestParam(value = "deviceName", defaultValue = "") String deviceName,
        @RequestParam(value = "deviceSerialNumber", defaultValue = "") String deviceSerialNumber,
        @RequestParam(value = "state", defaultValue = "") Integer state
    ){
        Result<TypeDeviceStateCountVo> result = deviceChartService.deviceStateCountOfType(
            tenant ,
            deviceTypeId,
            deviceName,
            deviceSerialNumber,
            DeviceStateEnum.typeOfValue(state)
        );
        return R.result(result);
    }

    /**
     * 获取客户所有设备的当前状态统计
     * 设备总数、在线设备、离线设备、故障设备
     * 
     * @param deviceName 设备名
     * @param deviceSerialNumber 设备编号
     * @param state 状态
     * @param customerId 客户id
     */
    @GetMapping("/device-state-count-of-customer")
    public R<CustomerDeviceStateCountVo> deviceStateCountOfCustomer(
        @RequestHeader("dcm_headers") TenantIsolation tenant, 
        @RequestParam(value = "deviceName", defaultValue = "") String deviceName,
        @RequestParam(value = "deviceSerialNumber", defaultValue = "") String deviceSerialNumber,
        @RequestParam(value = "state", defaultValue = "") Integer state,
        @RequestParam(value = "customerId", defaultValue = "") Long customerId
    ){
        Result<CustomerDeviceStateCountVo> result = deviceChartService.deviceStateCountOfCustomer(
            tenant.getTenantId() , 
            tenant.getIdType() ,
            deviceName,
            deviceSerialNumber,
            DeviceStateEnum.typeOfValue(state),
            customerId
        );
        return R.result(result);
    }

    /**
     * 获取所有设备的当前状态统计
     * 
     */
    @GetMapping("/device-state-count-of-all")
    public R<DeviceStateCountVo> deviceStateCountOfAll(
        @RequestHeader("dcm_headers") TenantIsolation tenant
    ){
        Result<DeviceStateCountVo> result = deviceChartService.deviceStateCountOfAll(tenant.getTenantId(),tenant.getIdType());
        return R.result(result);
    }

    /**
     * 获取某个设备的多个属性值
     * 
     */
    @GetMapping("/properties-of-device/{deviceId}")
    public R<Map<String, PropertyValueWithTime>> propertiesOfDevice(
        @RequestHeader("dcm_headers") TenantIsolation tenant, 
        @PathVariable("deviceId") Long deviceId,
        @RequestParam("properties") String[] properties
    ){
        Result<Map<String, PropertyValueWithTime>> result = deviceChartService.propertiesOfDevice(tenant.getTenantId() , deviceId, properties);
        return R.result(result);
    }

    /**
     * 获取ot设备id->云管设备名映射
     * 
     */
    @GetMapping("/ot-device-id-dcm-name-map")
    public R<Map<Long, String>> otDeviceIdDcmNameMap(
        @RequestHeader("dcm_headers") TenantIsolation tenant
    ){
        Result<Map<Long, String>> result = deviceChartService.otDeviceIdDcmNameMap(tenant);
        return R.result(result);
    }
    /**
     * 查询供应商设备总数
     *
     */
    @GetMapping("/querySupplierDeviceCount")
    public R<Integer> querySupplierDeviceCount(
        @RequestHeader("dcm_headers") TenantIsolation tenant
    ){
        Result<Integer> result = deviceChartService.querySupplierDeviceCount(
            tenant
        );
        return R.result(result);
    }

    /**
     * 查询累计在线时长
     * 
     * @param deviceId 设备id
     * @param begin 开始时间，格式：2023-12-26T07:00:00
     * @param end 结束时间，格式：2023-12-26T07:00:00
     */
    @GetMapping("/queryOnLineTimeSumOfDevice/{deviceId}")
    public R<Object> queryOnLineTimeSumOfDevice(
        @RequestHeader("dcm_headers") TenantIsolation tenant, 
        @PathVariable("deviceId") Long deviceId,
        @RequestParam("begin") String begin,
        @RequestParam("end") String end
    ){
        ITResult<Object> result = deviceChartService.queryOnLineTimeSumOfDevice(tenant.getTenantId() , deviceId, begin, end);
        return R.result(result);
    }

    /**
     * 查询累计空闲时长
     * 
     * @param deviceId 设备id
     * @param begin 开始时间，格式：2023-12-26T07:00:00
     * @param end 结束时间，格式：2023-12-26T07:00:00
     */
    @GetMapping("/queryStandByTimeSumOfDevice/{deviceId}")
    public R<Object> queryStandByTimeSumOfDevice(
        @RequestHeader("dcm_headers") TenantIsolation tenant, 
        @PathVariable("deviceId") Long deviceId,
        @RequestParam("begin") String begin,
        @RequestParam("end") String end
    ){
        ITResult<Object> result = deviceChartService.queryStandByTimeSumOfDevice(tenant.getTenantId() , deviceId, begin, end);
        return R.result(result);
    }

    /**
     * 查询累计任务时长
     * 
     * @param deviceId 设备id
     * @param begin 开始时间，格式：2023-12-26T07:00:00
     * @param end 结束时间，格式：2023-12-26T07:00:00
     */
    @GetMapping("/queryTaskTimeSumOfDevice/{deviceId}")
    public R<Object> queryTaskTimeSumOfDevice(
        @RequestHeader("dcm_headers") TenantIsolation tenant, 
        @PathVariable("deviceId") Long deviceId,
        @RequestParam("begin") String begin,
        @RequestParam("end") String end
    ){
        ITResult<Object> result = deviceChartService.queryTaskTimeSumOfDevice(tenant.getTenantId() , deviceId, begin, end);
        return R.result(result);
    }

    /**
     * 查询累计故障时长
     * 
     * @param deviceId 设备id
     * @param begin 开始时间，格式：2023-12-26T07:00:00
     * @param end 结束时间，格式：2023-12-26T07:00:00
     */
    @GetMapping("/queryFaultTimeSumOfDevice/{deviceId}")
    public R<Object> queryFaultTimeSumOfDevice(
        @RequestHeader("dcm_headers") TenantIsolation tenant, 
        @PathVariable("deviceId") Long deviceId,
        @RequestParam("begin") String begin,
        @RequestParam("end") String end
    ){
        ITResult<Object> result = deviceChartService.queryFaultTimeSumOfDevice(tenant.getTenantId() , deviceId, begin, end);
        return R.result(result);
    }


    /**
     * 查询平均故障时长
     * 
     * @param deviceId 设备id
     * @param begin 开始时间，格式：2023-12-26T07:00:00
     * @param end 结束时间，格式：2023-12-26T07:00:00
     */
    @GetMapping("/queryFaultTimeAvgOfDevice/{deviceId}")
    public R<Object> queryFaultTimeAvgOfDevice(
        @RequestHeader("dcm_headers") TenantIsolation tenant, 
        @PathVariable("deviceId") Long deviceId,
        @RequestParam("begin") String begin,
        @RequestParam("end") String end
    ){
        ITResult<Object> result = deviceChartService.queryFaultTimeAvgOfDevice(tenant.getTenantId() , deviceId, begin, end);
        return R.result(result);
    }

    /**
     * 查询故障率
     * 
     * @param deviceId 设备id
     * @param begin 开始时间，格式：2023-12-26T07:00:00
     * @param end 结束时间，格式：2023-12-26T07:00:00
     */
    @GetMapping("/queryFaultRateOfDevice/{deviceId}")
    public R<Object> queryFaultRateOfDevice(
        @RequestHeader("dcm_headers") TenantIsolation tenant, 
        @PathVariable("deviceId") Long deviceId,
        @RequestParam("begin") String begin,
        @RequestParam("end") String end
    ){
        ITResult<Object> result = deviceChartService.queryFaultRateOfDevice(tenant.getTenantId() , deviceId, begin, end);
        return R.result(result);
    }

    /**
     * 查询利用率
     * 
     * @param deviceId 设备id
     * @param begin 开始时间，格式：2023-12-26T07:00:00
     * @param end 结束时间，格式：2023-12-26T07:00:00
     */
    @GetMapping("/queryUseRateOfDevice/{deviceId}")
    public R<Object> queryUseRateOfDevice(
        @RequestHeader("dcm_headers") TenantIsolation tenant, 
        @PathVariable("deviceId") Long deviceId,
        @RequestParam("begin") String begin,
        @RequestParam("end") String end
    ){
        ITResult<Object> result = deviceChartService.queryUseRateOfDevice(tenant.getTenantId() , deviceId, begin, end);
        return R.result(result);
    }


    /**
     * 查询分组故障次数
     * 
     * @param deviceId 设备id
     * @param begin 开始时间，格式：2023-12-26T07:00:00
     * @param end 结束时间，格式：2023-12-26T07:00:00
     * @param every 分组窗口，可选，不传的时候根据开始结束自动计算，按小时：1h，按日：1d，按周：1w，按月：1mo
     */
    @GetMapping("/queryFaultCountByWindowOfDevice/{deviceId}")
    public R<Object> queryFaultCountByWindowOfDevice(
        @RequestHeader("dcm_headers") TenantIsolation tenant, 
        @PathVariable("deviceId") Long deviceId,
        @RequestParam("begin") String begin,
        @RequestParam("end") String end,
        @RequestParam(value = "every", defaultValue = "") String every
    ){
        ITResult<Object> result = deviceChartService.queryFaultCountByWindowOfDevice(tenant.getTenantId() , deviceId, begin, end, every);
        return R.result(result);
    }

    /**
     * 查询所有设备分组故障次数
     * 
     * @param begin 开始时间，格式：2023-12-26T07:00:00
     * @param end 结束时间，格式：2023-12-26T07:00:00
     * @param every 分组窗口，可选，不传的时候根据开始结束自动计算，按小时：1h，按日：1d，按周：1w，按月：1mo
     */
    @GetMapping("/queryFaultCountByWindowOfAllDevice")
    public R<Object> queryFaultCountByWindowOfAllDevice(
        @RequestHeader("dcm_headers") TenantIsolation tenant, 
        @RequestParam("begin") String begin,
        @RequestParam("end") String end,
        @RequestParam(value = "every", defaultValue = "") String every
    ){
        ITResult<Object> result = deviceChartService.queryFaultCountByWindowOfAllDevice(tenant.getTenantId() , begin, end, every);
        return R.result(result);
    }

    /**
     * 查询分组利用率
     * 
     * @param deviceId 设备id
     * @param begin 开始时间，格式：2023-12-26T07:00:00
     * @param end 结束时间，格式：2023-12-26T07:00:00
     * @param every 分组窗口，可选，不传的时候根据开始结束自动计算，按小时：1h，按日：1d，按周：1w，按月：1mo
     */
    @GetMapping("/queryUseRateByWindowOfDevice/{deviceId}")
    public R<Object> queryUseRateByWindowOfDevice(
        @RequestHeader("dcm_headers") TenantIsolation tenant, 
        @PathVariable("deviceId") Long deviceId,
        @RequestParam("begin") String begin,
        @RequestParam("end") String end,
        @RequestParam(value = "every", defaultValue = "") String every
    ){
        ITResult<Object> result = deviceChartService.queryUseRateByWindowOfDevice(tenant.getTenantId() , deviceId, begin, end, every);
        return R.result(result);
    }

    /**
     * 查询同类设备分组利用率
     * 
     * @param deviceId 设备id
     * @param begin 开始时间，格式：2023-12-26T07:00:00
     * @param end 结束时间，格式：2023-12-26T07:00:00
     * @param every 分组窗口，可选，不传的时候根据开始结束自动计算，按小时：1h，按日：1d，按周：1w，按月：1mo
     */
    @GetMapping("/queryDevicesUseRateByWindowOfDevice/{deviceId}")
    public R<Object> queryDevicesUseRateByWindowOfDevice(
        @RequestHeader("dcm_headers") TenantIsolation tenant, 
        @PathVariable("deviceId") Long deviceId,
        @RequestParam("begin") String begin,
        @RequestParam("end") String end,
        @RequestParam(value = "every", defaultValue = "") String every
    ){
        ITResult<Object> result = deviceChartService.queryDevicesUseRateByWindowOfDevice(tenant , deviceId, begin, end, every);
        return R.result(result);
    }

    /**
     * 查询分组累计在线时长
     * 
     * @param deviceId 设备id
     * @param begin 开始时间，格式：2023-12-26T07:00:00
     * @param end 结束时间，格式：2023-12-26T07:00:00
     * @param every 分组窗口，可选，不传的时候根据开始结束自动计算，按小时：1h，按日：1d，按周：1w，按月：1mo
     */
    @GetMapping("/queryOnLineTimeByWindowOfDevice/{deviceId}")
    public R<Object> queryOnLineTimeByWindowOfDevice(
        @RequestHeader("dcm_headers") TenantIsolation tenant, 
        @PathVariable("deviceId") Long deviceId,
        @RequestParam("begin") String begin,
        @RequestParam("end") String end,
        @RequestParam(value = "every", defaultValue = "") String every
    ){
        ITResult<Object> result = deviceChartService.queryOnLineTimeByWindowOfDevice(tenant.getTenantId() , deviceId, begin, end, every);
        return R.result(result);
    }

    /**
     * 查询同类设备分组累计在线时长
     * 
     * @param deviceId 设备id
     * @param begin 开始时间，格式：2023-12-26T07:00:00
     * @param end 结束时间，格式：2023-12-26T07:00:00
     * @param every 分组窗口，可选，不传的时候根据开始结束自动计算，按小时：1h，按日：1d，按周：1w，按月：1mo
     */
    @GetMapping("/queryDevicesOnLineTimeByWindowOfDevice/{deviceId}")
    public R<Object> queryDevicesOnLineTimeByWindowOfDevice(
        @RequestHeader("dcm_headers") TenantIsolation tenant, 
        @PathVariable("deviceId") Long deviceId,
        @RequestParam("begin") String begin,
        @RequestParam("end") String end,
        @RequestParam(value = "every", defaultValue = "") String every
    ){
        ITResult<Object> result = deviceChartService.queryDevicesOnLineTimeByWindowOfDevice(tenant , deviceId, begin, end, every);
        return R.result(result);
    }
    
    /**
     * 查询分组累计任务时长
     * 
     * @param deviceId 设备id
     * @param begin 开始时间，格式：2023-12-26T07:00:00
     * @param end 结束时间，格式：2023-12-26T07:00:00
     * @param every 分组窗口，可选，不传的时候根据开始结束自动计算，按小时：1h，按日：1d，按周：1w，按月：1mo
     */
    @GetMapping("/queryTaskTimeByWindowOfDevice/{deviceId}")
    public R<Object> queryTaskTimeByWindowOfDevice(
        @RequestHeader("dcm_headers") TenantIsolation tenant, 
        @PathVariable("deviceId") Long deviceId,
        @RequestParam("begin") String begin,
        @RequestParam("end") String end,
        @RequestParam(value = "every", defaultValue = "") String every
    ){
        ITResult<Object> result = deviceChartService.queryTaskTimeByWindowOfDevice(tenant.getTenantId() , deviceId, begin, end, every);
        return R.result(result);
    }
    
    /**
     * 查询同类设备分组累计任务时长
     * 
     * @param deviceId 设备id
     * @param begin 开始时间，格式：2023-12-26T07:00:00
     * @param end 结束时间，格式：2023-12-26T07:00:00
     * @param every 分组窗口，可选，不传的时候根据开始结束自动计算，按小时：1h，按日：1d，按周：1w，按月：1mo
     */
    @GetMapping("/queryDevicesTaskTimeByWindowOfDevice/{deviceId}")
    public R<Object> queryDevicesTaskTimeByWindowOfDevice(
        @RequestHeader("dcm_headers") TenantIsolation tenant, 
        @PathVariable("deviceId") Long deviceId,
        @RequestParam("begin") String begin,
        @RequestParam("end") String end,
        @RequestParam(value = "every", defaultValue = "") String every
    ){
        ITResult<Object> result = deviceChartService.queryDevicesTaskTimeByWindowOfDevice(tenant , deviceId, begin, end, every);
        return R.result(result);
    }
    
    

    /**
     * 查询分组累计空闲时长
     * 
     * @param deviceId 设备id
     * @param begin 开始时间，格式：2023-12-26T07:00:00
     * @param end 结束时间，格式：2023-12-26T07:00:00
     * @param every 分组窗口，可选，不传的时候根据开始结束自动计算，按小时：1h，按日：1d，按周：1w，按月：1mo
     */
    @GetMapping("/queryStandByTimeByWindowOfDevice/{deviceId}")
    public R<Object> queryStandByTimeByWindowOfDevice(
        @RequestHeader("dcm_headers") TenantIsolation tenant, 
        @PathVariable("deviceId") Long deviceId,
        @RequestParam("begin") String begin,
        @RequestParam("end") String end,
        @RequestParam(value = "every", defaultValue = "") String every
    ){
        ITResult<Object> result = deviceChartService.queryStandByTimeByWindowOfDevice(tenant.getTenantId() , deviceId, begin, end, every);
        return R.result(result);
    }


    /**
     * 查询同类设备分组累计空闲时长
     * 
     * @param deviceId 设备id
     * @param begin 开始时间，格式：2023-12-26T07:00:00
     * @param end 结束时间，格式：2023-12-26T07:00:00
     * @param every 分组窗口，可选，不传的时候根据开始结束自动计算，按小时：1h，按日：1d，按周：1w，按月：1mo
     */
    @GetMapping("/queryDevicesStandByTimeByWindowOfDevice/{deviceId}")
    public R<Object> queryDevicesStandByTimeByWindowOfDevice(
        @RequestHeader("dcm_headers") TenantIsolation tenant, 
        @PathVariable("deviceId") Long deviceId,
        @RequestParam("begin") String begin,
        @RequestParam("end") String end,
        @RequestParam(value = "every", defaultValue = "") String every
    ){
        ITResult<Object> result = deviceChartService.queryDevicesStandByTimeByWindowOfDevice(tenant, deviceId, begin, end, every);
        return R.result(result);
    }

    /**
     * 查询分组累计故障时长
     * 
     * @param deviceId 设备id
     * @param begin 开始时间，格式：2023-12-26T07:00:00
     * @param end 结束时间，格式：2023-12-26T07:00:00
     * @param every 分组窗口，可选，不传的时候根据开始结束自动计算，按小时：1h，按日：1d，按周：1w，按月：1mo
     */
    @GetMapping("/queryFaultTimeByWindowOfDevice/{deviceId}")
    public R<Object> queryFaultTimeByWindowOfDevice(
        @RequestHeader("dcm_headers") TenantIsolation tenant, 
        @PathVariable("deviceId") Long deviceId,
        @RequestParam("begin") String begin,
        @RequestParam("end") String end,
        @RequestParam(value = "every", defaultValue = "") String every
    ){
        ITResult<Object> result = deviceChartService.queryFaultTimeByWindowOfDevice(tenant.getTenantId() , deviceId, begin, end, every);
        return R.result(result);
    }


    /**
     * 查询同类设备分组累计故障时长
     * 
     * @param deviceId 设备id
     * @param begin 开始时间，格式：2023-12-26T07:00:00
     * @param end 结束时间，格式：2023-12-26T07:00:00
     * @param every 分组窗口，可选，不传的时候根据开始结束自动计算，按小时：1h，按日：1d，按周：1w，按月：1mo
     */
    @GetMapping("/queryDevicesFaultTimeByWindowOfDevice/{deviceId}")
    public R<Object> queryDevicesFaultTimeByWindowOfDevice(
        @RequestHeader("dcm_headers") TenantIsolation tenant, 
        @PathVariable("deviceId") Long deviceId,
        @RequestParam("begin") String begin,
        @RequestParam("end") String end,
        @RequestParam(value = "every", defaultValue = "") String every
    ){
        ITResult<Object> result = deviceChartService.queryDevicesFaultTimeByWindowOfDevice(tenant , deviceId, begin, end, every);
        return R.result(result);
    }

    /**
     * 查询所有设备分组累计故障时长
     * 
     * @param begin 开始时间，格式：2023-12-26T07:00:00
     * @param end 结束时间，格式：2023-12-26T07:00:00
     * @param every 分组窗口，可选，不传的时候根据开始结束自动计算，按小时：1h，按日：1d，按周：1w，按月：1mo
     */
    @GetMapping("/queryFaultTimeByWindowOfAllDevice")
    public R<Object> queryFaultTimeByWindowOfAllDevice(
        @RequestHeader("dcm_headers") TenantIsolation tenant, 
        @RequestParam("begin") String begin,
        @RequestParam("end") String end,
        @RequestParam(value = "every", defaultValue = "") String every
    ){
        ITResult<Object> result = deviceChartService.queryFaultTimeByWindowOfAllDevice(tenant.getTenantId() , begin, end, every);
        return R.result(result);
    }

    /**
     * 查询设备状态日志
     * 
     * @param deviceId 设备id
     * @param begin 开始时间，格式：2023-12-26T07:00:00
     * @param size 分页大小，默认10
     * @param current 当前页，默认1
     */
    @GetMapping("/page/state-of-device")
    public R<Page<DeviceStateVo>> pageStateOfDevice(
        @RequestHeader("dcm_headers") TenantIsolation tenant, 
        @RequestParam("deviceId") Long deviceId,
        @RequestParam("begin") String begin,
        @RequestParam("end") String end,
        @RequestParam(value = "size", defaultValue = "10") @Max(500) Integer size,
        @RequestParam(value = "current", defaultValue = "1") @Min(1) Integer current
    ){
        Result<Page<DeviceStateVo>> result = deviceChartService.pageStateOfDevice(tenant.getTenantId(), deviceId, begin, end, size, current);
        return R.result(result);
    }

    /**
     * 查询所有设备空闲率
     * 
     * @param begin 开始时间，格式：2023-12-26T07:00:00
     * @param end 结束时间，格式：2023-12-26T07:00:00
     */
    @GetMapping("/queryStandByRateOfAllDevice")
    public R<Object> queryStandByRateOfAllDevice(
        @RequestHeader("dcm_headers") TenantIsolation tenant, 
        @RequestParam("begin") String begin,
        @RequestParam("end") String end
    ){
        ITResult<Object> result = deviceChartService.queryStandByRateOfAllDevice(tenant.getTenantId() , begin, end);
        return R.result(result);
    }

    /**
     * 查询所有设备利用率
     * 
     * @param begin 开始时间，格式：2023-12-26T07:00:00
     * @param end 结束时间，格式：2023-12-26T07:00:00
     */
    @GetMapping("/queryUseRateOfAllDevice")
    public R<Object> queryUseRateOfAllDevice(
        @RequestHeader("dcm_headers") TenantIsolation tenant, 
        @RequestParam("begin") String begin,
        @RequestParam("end") String end
    ){
        ITResult<Object> result = deviceChartService.queryUseRateOfAllDevice(tenant , begin, end);
        return R.result(result);
    }

    /**
     * 查询所有设备故障率
     * 
     * @param begin 开始时间，格式：2023-12-26T07:00:00
     * @param end 结束时间，格式：2023-12-26T07:00:00
     */
    @GetMapping("/queryFaultRateOfAllDevice")
    public R<Object> queryFaultRateOfAllDevice(
        @RequestHeader("dcm_headers") TenantIsolation tenant, 
        @RequestParam("begin") String begin,
        @RequestParam("end") String end
    ){
        ITResult<Object> result = deviceChartService.queryFaultRateOfAllDevice(tenant.getTenantId() , begin, end);
        return R.result(result);
    }

    /**
     * 查询设备分组累计任务时长
     * 
     * @param begin 开始时间，格式：2023-12-26T07:00:00
     * @param end 结束时间，格式：2023-12-26T07:00:00
     * @param every 分组窗口，可选，不传的时候根据开始结束自动计算，按小时：1h，按日：1d，按周：1w，按月：1mo
     */
    @GetMapping("/queryDevicesTaskTimeByWindowOfDeviceType")
    public R<Object> queryDevicesTaskTimeByWindowOfDeviceType(
        @RequestHeader("dcm_headers") TenantIsolation tenant, 
        @RequestParam("deviceTypeId") Long deviceTypeId,
        @RequestParam("begin") String begin,
        @RequestParam("end") String end,
        @RequestParam(value = "every", defaultValue = "") String every
    ){
        ITResult<Object> result = deviceChartService.queryDevicesTaskTimeByWindowOfDeviceType(tenant, deviceTypeId, begin, end, every);
        return R.result(result);
    }
    
    /**
     * 查询所有设备累计故障时长
     * 
     * @param begin 开始时间，格式：2023-12-26T07:00:00
     * @param end 结束时间，格式：2023-12-26T07:00:00
     */
    @GetMapping("/queryFaultTimeSumOfAllDevice")
    public R<Object> queryFaultTimeSumOfAllDevice(
        @RequestHeader("dcm_headers") TenantIsolation tenant, 
        @RequestParam("begin") String begin,
        @RequestParam("end") String end
    ){
        ITResult<Object> result = deviceChartService.queryFaultTimeSumOfAllDevice(tenant.getTenantId() , begin, end);
        return R.result(result);
    }

    /**
     * 查询所有设备累计任务时长
     * 
     * @param begin 开始时间，格式：2023-12-26T07:00:00
     * @param end 结束时间，格式：2023-12-26T07:00:00
     */
    @GetMapping("/queryTaskTimeSumOfAllDevice")
    public R<Object> queryTaskTimeSumOfAllDevice(
        @RequestHeader("dcm_headers") TenantIsolation tenant, 
        @RequestParam("begin") String begin,
        @RequestParam("end") String end
    ){
        ITResult<Object> result = deviceChartService.queryTaskTimeSumOfAllDevice(tenant.getTenantId() , begin, end);
        return R.result(result);
    }

    /**
     * 查询所有设备累计在线时长
     * 
     * @param begin 开始时间，格式：2023-12-26T07:00:00
     * @param end 结束时间，格式：2023-12-26T07:00:00
     */
    @GetMapping("/queryOnLineTimeSumOfAllDevice")
    public R<Object> queryOnLineTimeSumOfAllDevice(
        @RequestHeader("dcm_headers") TenantIsolation tenant, 
        @RequestParam("begin") String begin,
        @RequestParam("end") String end
    ){
        ITResult<Object> result = deviceChartService.queryOnLineTimeSumOfAllDevice(tenant.getTenantId() , begin, end);
        return R.result(result);
    }

    /**
     * 查询所有设备故障次数
     * 
     * @param begin 开始时间，格式：2023-12-26T07:00:00
     * @param end 结束时间，格式：2023-12-26T07:00:00
     */
    @GetMapping("/queryFaultCountOfAllDevice")
    public R<Object> queryFaultCountOfAllDevice(
        @RequestHeader("dcm_headers") TenantIsolation tenant, 
        @RequestParam("begin") String begin,
        @RequestParam("end") String end
    ){
        ITResult<Object> result = deviceChartService.queryFaultCountOfAllDevice(tenant.getTenantId() , begin, end);
        return R.result(result);
    }

    /**
     * 查询所有故障设备数量
     * 
     * @param begin 开始时间，格式：2023-12-26T07:00:00
     * @param end 结束时间，格式：2023-12-26T07:00:00
     */
    @GetMapping("/queryFaultDevicesNumberOfAllDevice")
    public R<Object> queryFaultDevicesNumberOfAllDevice(
        @RequestHeader("dcm_headers") TenantIsolation tenant, 
        @RequestParam("begin") String begin,
        @RequestParam("end") String end
    ){
        ITResult<Object> result = deviceChartService.queryFaultDevicesNumberOfAllDevice(tenant.getTenantId() , begin, end);
        return R.result(result);
    }

    /**
     * 查询所有设备累计故障时长TopN
     * 
     * @param begin 开始时间，格式：2023-12-26T07:00:00
     * @param end 结束时间，格式：2023-12-26T07:00:00
     */
    @GetMapping("/queryDevicesFaultTimeSumTopnOfAllDevice")
    public R<Object> queryDevicesFaultTimeSumTopnOfAllDevice(
        @RequestHeader("dcm_headers") TenantIsolation tenant, 
        @RequestParam("begin") String begin,
        @RequestParam("end") String end,
        @RequestParam(value = "n", defaultValue = "10") Integer n
    ){
        ITResult<Object> result = deviceChartService.queryDevicesFaultTimeSumTopnOfAllDevice(tenant , begin, end, n);
        return R.result(result);
    }

    /**
     * 查询所有设备累计故障次数TopN
     * 
     * @param begin 开始时间，格式：2023-12-26T07:00:00
     * @param end 结束时间，格式：2023-12-26T07:00:00
     */
    @GetMapping("/queryDevicesFaultCountTopnOfAllDevice")
    public R<Object> queryDevicesFaultCountTopnOfAllDevice(
        @RequestHeader("dcm_headers") TenantIsolation tenant, 
        @RequestParam("begin") String begin,
        @RequestParam("end") String end,
        @RequestParam(value = "n", defaultValue = "10") Integer n
    ){
        ITResult<Object> result = deviceChartService.queryDevicesFaultCountTopnOfAllDevice(tenant, begin, end, n);
        return R.result(result);
    }


    /**
     * 查询所有设备累计故障时长TopN
     *
     */
    @GetMapping("/queryDeviceDistribution")
    public R<DeviceBlockChartVo> queryDeviceDistribution(
            @RequestHeader("dcm_headers") TenantIsolation tenant
    ){
        Result<DeviceBlockChartVo> result = deviceChartService.queryDeviceDistribution(tenant);
        return R.result(result);
    }

    /**
     * 查询健康度
     * 
     * @param deviceId 设备id
     * @param begin 开始时间，格式：2023-12-26T07:00:00
     * @param end 结束时间，格式：2023-12-26T07:00:00
     */
    @GetMapping("/queryHealthDegreeOfDevice/{deviceId}")
    public R<Object> queryHealthDegreeOfDevice(
        @RequestHeader("dcm_headers") TenantIsolation tenant, 
        @PathVariable("deviceId") Long deviceId,
        @RequestParam("begin") String begin,
        @RequestParam("end") String end
    ){
        ITResult<Object> result = deviceChartService.queryHealthDegreeOfDevice(tenant.getTenantId() , deviceId, begin, end);
        return R.result(result);
    }

    /**
     * 查询分组健康度
     * 
     * @param deviceId 设备id
     * @param begin 开始时间，格式：2023-12-26T07:00:00
     * @param end 结束时间，格式：2023-12-26T07:00:00
     * @param every 分组窗口，可选，不传的时候根据开始结束自动计算，按小时：1h，按日：1d，按周：1w，按月：1mo
     */
    @GetMapping("/queryHealthDegreeByWindowOfDevice/{deviceId}")
    public R<Object> queryHealthDegreeByWindowOfDevice(
        @RequestHeader("dcm_headers") TenantIsolation tenant, 
        @PathVariable("deviceId") Long deviceId,
        @RequestParam("begin") String begin,
        @RequestParam("end") String end,
        @RequestParam(value = "every", defaultValue = "") String every
    ){
        ITResult<Object> result = deviceChartService.queryHealthDegreeByWindowOfDevice(tenant.getTenantId() , deviceId, begin, end, every);
        return R.result(result);
    }
}
