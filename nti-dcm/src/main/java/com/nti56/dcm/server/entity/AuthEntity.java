package com.nti56.dcm.server.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import lombok.Data;

/**
 * <p>
 * 权限表 实体类
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @Ddate 2024-02-23 15:13:54
 * @since JDK 1.8
 */
@Data
@TableName("auth")
public class AuthEntity extends BaseEntity{
        /**
        * 身份类型, 1-供应商，2-客户
        */
        private Integer idType;

        /**
        * 权限名
        */
        private String authName;

        /**
        * 权限描述
        */
        private String authDesc;

        /**
        * 权限类型, 1-系统固定权限，2-菜单权限，3-按钮权限
        */
        private Integer authType;

        /**
        * 所属菜单
        */
        private String menuName;

}
