package com.nti56.dcm.server.domain.enums;

import lombok.Getter;

public enum DingDingCardUrlEnum {
    MONITOR_DETAIL(1, "%s/pagesB/monitor/detail?id=%s&idType=%s", "跳转设备监控详情"),
    EQUIPMENT_DETAIL(2, "%s/pagesA/equipment/detail?id=%s&idType=%s", "跳转设备台账详情"),
    REPAIR_ORDER(3, "%s/pagesD/repair/order?orderNo=%s&isOut=%s&type=waitHandle&idType=%s", "跳转维修工单"),
    MAINTAIN_ORDER(4, "%s/pagesD/maintain/order?orderNo=%s&isOut=%s&type=waitHandle&idType=%s", "跳转保养工单"),
    INSPECT_ORDER(5, "%s/pagesD/spotInspection/order?orderNo=%s&isOut=%s&type=waitHandle&idType=%s", "跳转巡检工单"),
    REPAIR_REPORT(6, "%s/pagesD/faultRepair/order?orderNo=%s&type=waitHandle&idType=%s", "跳转报修工单"),
    EQUIPMENT(7, "%s/pagesTabar/equipment/index?customerId=%s&customerName=%s&idType=%s", "跳转设备台账"),
    EQUIPMENT_MONITOR(7, "%s/pagesTabar/monitor/index?customerId=%s&customerName=%s&idType=%s", "跳转设备心电图"),
    ;

    @Getter
    private Integer value;

    @Getter
    private String name;

    @Getter
    private String nameDesc;

    DingDingCardUrlEnum(Integer value, String name, String nameDesc) {
        this.value = value;
        this.name = name;
        this.nameDesc = nameDesc;
    }

    public static DingDingCardUrlEnum typeOfValue(Integer value){
        DingDingCardUrlEnum[] values = DingDingCardUrlEnum.values();
        for (DingDingCardUrlEnum v : values) {
            if (v.value.equals(value)) {
                return v;
            }
        }
        return null;
    }

    public static DingDingCardUrlEnum typeOfName(String name){
        DingDingCardUrlEnum[] values = DingDingCardUrlEnum.values();
        for (DingDingCardUrlEnum v : values) {
            if (v.name.equals(name)) {
                return v;
            }
        }
        return null;
    }

    public static DingDingCardUrlEnum typeOfNameDesc(String nameDesc){
        DingDingCardUrlEnum[] values = DingDingCardUrlEnum.values();
        for (DingDingCardUrlEnum v : values) {
            if (v.nameDesc.equals(nameDesc)) {
                return v;
            }
        }
        return null;
    }
}
