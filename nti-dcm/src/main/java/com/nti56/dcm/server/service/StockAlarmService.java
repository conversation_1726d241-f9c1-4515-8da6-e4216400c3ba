package com.nti56.dcm.server.service;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.nti56.dcm.server.domain.enums.AlarmTypeEnum;
import com.nti56.dcm.server.domain.enums.IdTypeEnum;
import com.nti56.dcm.server.domain.enums.YesNoEnum;
import com.nti56.dcm.server.entity.AlarmRecordEntity;
import com.nti56.dcm.server.entity.TenantInfoEntity;
import com.nti56.dcm.server.mapper.MessageNoticeStrategyMapper;
import com.nti56.dcm.server.mapper.TenantInfoMapper;
import com.nti56.dcm.server.model.dto.*;
import com.nti56.dcm.server.model.vo.MessageStrategyConfigVo;
import com.nti56.nlink.common.util.Result;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.util.UriComponentsBuilder;

import java.time.Duration;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

@Service
@Slf4j
public class StockAlarmService {

    @Value("${wms.get-safe-stock-url}")
    private String getSafeStockUrl;

    @Value("${wms.url}")
    private String baseUrl;

    @Autowired
    @Qualifier("restTemplate")
    private RestTemplate restHttpTemplate;

    @Autowired
    private AlarmRecordService alarmRecordService;

    @Autowired
    private AsyncSendNoticeService asyncSendNoticeService;

    @Autowired
    private TenantInfoMapper tenantInfoMapper;

    @Autowired
    private MessageNoticeStrategyMapper messageNoticeStrategyMapper;

    public void stockAlarm() {
        // 获取所有租户
        List<TenantInfoEntity> tenantList = tenantInfoMapper.listAllTenant();
        if (CollectionUtil.isEmpty(tenantList)) {
            return;
        }

        // 获取所有租户库存预警配置
        List<MessageStrategyConfigVo> allConfigList = messageNoticeStrategyMapper.queryOrderTimeoutStrategyConfig(null, 5, null, null);
        Map<Long, List<MessageStrategyConfigVo>> configMap = allConfigList.stream().collect(Collectors.groupingBy(MessageStrategyConfigVo::getTenantId));
        if (CollectionUtil.isEmpty(configMap)) {
            return;
        }

        for (TenantInfoEntity tenant : tenantList) {
            Long tenantId = tenant.getTenantId();
            List<MessageStrategyConfigVo> configList = configMap.get(tenantId);
            if (CollectionUtil.isEmpty(configList)) {
                continue;
            }

            List<SafeStockDto> allStockList = getSafeStock(tenantId);

            // 默认仓库数据
            List<SafeStockDto> defaultStockList = allStockList.stream().filter(stock -> stock.getWarehouseName().equals("默认仓库")).collect(Collectors.toList());
            if (CollectionUtil.isEmpty(defaultStockList)) {
                return;
            }

            // 库存预警
            defaultStockList.forEach(stock -> {
                LocalDateTime now = LocalDateTime.now();
                String spareAlarmType = "";
                if (stock.getBaseQty().compareTo(stock.getMaxQty()) > 0) {
                    spareAlarmType = "高于上限";
                } else if (stock.getBaseQty().compareTo(stock.getMinQty()) < 0) {
                    spareAlarmType = "低于下限";
                }
                stock.setSpareAlarmType(spareAlarmType);
                stock.setSpareAlarmTime(now);

                // 保存库存预警记录
                boolean saveFlag = stockAlarmRecord(stock, tenantId, now, spareAlarmType);

                if (!saveFlag) {
                    return;
                }

                configList.forEach(config -> {
                    asyncSendNoticeService.sendStockAlarmNotice(config, stock);
                });
            });
        }

    }

    private boolean stockAlarmRecord(SafeStockDto stockDto, Long tenantId, LocalDateTime now, String spareAlarmType) {
        Integer alarmType = AlarmTypeEnum.SPARES_STOCK.getValue();
        AlarmRecordEntity existAlarmRecord = alarmRecordService.getOne(Wrappers.<AlarmRecordEntity>lambdaQuery()
                .eq(AlarmRecordEntity::getAlarmType, alarmType)
                .eq(AlarmRecordEntity::getSpareNo, stockDto.getItemCode())
                .eq(AlarmRecordEntity::getTenantId, tenantId)
                .eq(AlarmRecordEntity::getIdType, IdTypeEnum.CUSTOMER.getValue())
                .eq(AlarmRecordEntity::getIsFinish, YesNoEnum.NO.getValue())
                .last("limit 1"));
        // 如果存在并且spareAlarmType、BaseQty相同，则不保存； 如果存在并且spareAlarmType、BaseQty不同，则将existAlarmRecord的isFinish设置为YES
        if (existAlarmRecord != null) {
            if (existAlarmRecord.getSpareAlarmType().equals(spareAlarmType) && existAlarmRecord.getSpareQty().equals(stockDto.getBaseQty())) {
                return false;
            } else {
                existAlarmRecord.setIsFinish(YesNoEnum.YES.getValue());
                LocalDateTime alarmEndTime = LocalDateTime.now();
                existAlarmRecord.setAlarmEndTime(alarmEndTime);
                String alarmDuration = getAlarmTimeStr(existAlarmRecord.getAlarmTime(), alarmEndTime);
                existAlarmRecord.setAlarmDuration(alarmDuration);
                alarmRecordService.updateById(existAlarmRecord);
            }
        }

        AlarmRecordEntity alarmRecord = new AlarmRecordEntity();
        alarmRecord.setAlarmType(alarmType);
        alarmRecord.setAlarmTime(now);
        alarmRecord.setSpareName(stockDto.getItemName());
        alarmRecord.setSpareNo(stockDto.getItemCode());
        alarmRecord.setSpareAlarmType(spareAlarmType);
        alarmRecord.setSpareQty(stockDto.getBaseQty());
        alarmRecord.setTenantId(tenantId);
        alarmRecord.setIdType(IdTypeEnum.CUSTOMER.getValue());
        alarmRecord.setCreatorId(1L);
        alarmRecord.setCreator("定时任务");
        Result saveAlarmResult = alarmRecordService.saveAlarmRecord(alarmRecord);
        if (!saveAlarmResult.getSignal()) {
            log.error("保存备件库存预警告警记录失败：{} {}", stockDto.getItemCode(), saveAlarmResult.getMessage());
            return false;
        }
        return true;
    }

    private String getAlarmTimeStr(LocalDateTime alarmTime, LocalDateTime alarmEndTime) {
        Duration duration = Duration.between(alarmTime, alarmEndTime);
        long hours = duration.toHours();
        long minutes = duration.toMinutes() % 60;
        long secs = duration.getSeconds() % 60;
        return String.format("%d时%d分%d秒", hours, minutes, secs);
    }

    private List<SafeStockDto> getSafeStock(Long tenantId) {
        List<SafeStockDto> stockList = new ArrayList<>();
        try {
            HttpHeaders headers = new HttpHeaders();
            MediaType type = MediaType.parseMediaType("application/json; charset=UTF-8");
            headers.setContentType(type);
            headers.add("Accept", MediaType.APPLICATION_JSON.toString());
            headers.add("logintype", "saas");

            // 构造请求体参数
            HashMap<String, Object> pageParams = new HashMap<>();
            pageParams.put("page", 1);
            pageParams.put("rows", 10000);
            HashMap<String, Object> queryParams = new HashMap<>();
            queryParams.put("clientId", tenantId);

            HashMap<String, Object> params = new HashMap<>();
            params.put("pageQuery", pageParams);
            params.put("params", queryParams);
            HttpEntity<HashMap<String, Object>> httpEntity = new HttpEntity<>(params, headers);
            String pathUrl = baseUrl + getSafeStockUrl;
            UriComponentsBuilder builder = UriComponentsBuilder.fromHttpUrl(pathUrl);
            log.info("获取WMS库存数据：{}", JSON.toJSONString(params));
            String result = restHttpTemplate.exchange(builder.build().toString(), HttpMethod.POST, httpEntity, String.class).getBody();
            log.info("获取WMS库存数据返回：{}", result);

            Map res = JSONObject.parseObject(result, Map.class);
            if (res.get("rows") != null) {
                stockList = JSON.parseArray(JSON.toJSONString(res.get("rows")), SafeStockDto.class);
            }
        } catch (Exception e) {
            log.error("获取WMS库存数据异常：{}", e.getMessage());
        }
        return stockList;
    }

}
