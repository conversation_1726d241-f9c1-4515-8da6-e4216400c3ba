package com.nti56.dcm.server.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import java.io.Serializable;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 客户表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-16
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("pmo_customer")
public class PmoCustomerEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 上级id
     */
    private Long pid;

    /**
     * 租户id
     */
    private Long clientId;

    /**
     * 标准规范编码
     */
    private String ntiId;

    /**
     * 客户编号
     */
    private String customerNo;

    /**
     * 客户名称
     */
    private String customerName;

    /**
     * 客户简称
     */
    private String customerNickname;

    /**
     * 客户简拼
     */
    private String customerBriefSpelling;

    /**
     * 合作关系
     */
    private String relation;

    /**
     * 客户类型
     */
    private String customerType;

    /**
     * 客户级别
     */
    private String customerLevel;

    /**
     * 客户分类：1-企业；2-个人；3-国外
     */
    private String customerClass;

    /**
     * 客户行业
     */
    private String industry;

    /**
     * 电话号码
     */
    private String telephone;

    /**
     * 邮箱
     */
    private String mail;

    /**
     * 传真
     */
    private String fax;

    /**
     * 邮政编码
     */
    private String zipNo;

    /**
     * 官网
     */
    private String website;

    /**
     * 备注
     */
    private String remark;

    /**
     * 数据来源;关联枚举-data_source
     */
    private String dataSource;

    /**
     * 所属部门
     */
    private String cSection;

    /**
     * 所属部门ID
     */
    private String section;

    /**
     * 所属部门名称
     */
    private String sectionName;

    /**
     * 销售负责人名称
     */
    private String principal;

    /**
     * 销售负责人id
     */
    private String principalId;

    /**
     * 销售负责人号码
     */
    private String principalPhone;

    /**
     * 国家,如中国
     */
    private String country;

    /**
     * 区域,如华东
     */
    private String region;

    /**
     * 省份
     */
    private String province;

    /**
     * 市
     */
    private String city;

    /**
     * 客户联系人
     */
    private String customerContacts;

    /**
     * 客户简介
     */
    private String introduce;

    /**
     * 删除状态; 0:未删除，1：已删除
     */
    private Integer deleted;

    /**
     * 创建人
     */
    private String createBy;

    /**
     * 创建人姓名
     */
    private String createByName;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 修改人
     */
    private String modifyBy;

    /**
     * 创建人姓名
     */
    private String modifyByName;

    /**
     * 更新时间
     */
    private LocalDateTime modifyTime;

    /**
     * 版本号
     */
    private Long version;


}
