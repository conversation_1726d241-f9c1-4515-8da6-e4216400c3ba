package com.nti56.dcm.server.entity;

import com.baomidou.mybatisplus.annotation.TableName;

import lombok.Data;

/***
 * 类说明: 备件实体类
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2024-02-020 09:40:01
 * @since JDK 1.8
 */
@Data
@TableName(value ="device_type_monitor")
public class DeviceTypeMonitorEntity extends BaseEntity{

    /**
     * 所属设备类型id
     */
    Long deviceTypeId;

    /**
     * 监控类型，1-工况数据，2-任务数据，3-能耗数据
     */
    Integer monitorType;

    /**
     * 属性名
     */
    String propertyName;

    /**
     * 属性描述
     */
    String propertyDesc;

    /**
     * 属性计算公式
     */
    String propertyFormula;

    /**
     * 属性单位
     */
    String propertyUnit;

}
