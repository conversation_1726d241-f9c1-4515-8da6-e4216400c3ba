package com.nti56.dcm.server.domain.enums;

import lombok.Getter;

public enum AcceptEnum {
    NO(0, "NO", "验收通过"),
    YES(1, "YES", "验收不通过"),
    ;

    @Getter
    private Integer value;

    @Getter
    private String name;

    @Getter
    private String nameDesc;

    AcceptEnum(Integer value, String name, String nameDesc) {
        this.value = value;
        this.name = name;
        this.nameDesc = nameDesc;
    }

    public static AcceptEnum typeOfValue(Integer value){
        AcceptEnum[] values = AcceptEnum.values();
        for (AcceptEnum v : values) {
            if (v.value.equals(value)) {
                return v;
            }
        }
        return null;
    }

    public static AcceptEnum typeOfName(String name){
        AcceptEnum[] values = AcceptEnum.values();
        for (AcceptEnum v : values) {
            if (v.name.equals(name)) {
                return v;
            }
        }
        return null;
    }

    public static AcceptEnum typeOfNameDesc(String nameDesc){
        AcceptEnum[] values = AcceptEnum.values();
        for (AcceptEnum v : values) {
            if (v.nameDesc.equals(nameDesc)) {
                return v;
            }
        }
        return null;
    }
}
