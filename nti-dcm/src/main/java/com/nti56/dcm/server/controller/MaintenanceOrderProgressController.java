package com.nti56.dcm.server.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.nti56.dcm.server.entity.MaintenanceOrderProgressEntity;
import com.nti56.dcm.server.service.MaintenanceOrderProgressService;
import com.nti56.nlink.common.util.*;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.function.Function;


/**
 * <p>
 * 保养工单进度表 控制器
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @Ddate 2024-03-01 16:28:19
 * @since JDK 1.8
 */
@RestController
@RequestMapping("/")
@Tag(name = "保养工单进度表模块")
public class MaintenanceOrderProgressController {

    @Autowired
    MaintenanceOrderProgressService service;

    @GetMapping("maintenance_order_progress/page")
    @Operation(summary = "获取分页")
    public R page(PageParam pageParam,MaintenanceOrderProgressEntity entity){
        Page<MaintenanceOrderProgressEntity> page = pageParam.toPage(MaintenanceOrderProgressEntity.class);
        Result<Page<MaintenanceOrderProgressEntity>> result = service.getPage(entity,page);
        return R.result(result);
    }

    @GetMapping("maintenance_order_progress/list")
    @Operation(summary = "获取列表" )
    public R list(MaintenanceOrderProgressEntity entity){
        Result<List<MaintenanceOrderProgressEntity>> result = service.list(entity);
        return R.result(result);
    }

    @PutMapping("maintenance_order_progress")
    @Operation(summary = "更新")
    public R update( @RequestBody MaintenanceOrderProgressEntity entity){
        if (BeanUtilsIntensifier.checkBeanAndProperties(entity, MaintenanceOrderProgressEntity::getId)) {
            return R.error(ServiceCodeEnum.CODE_PARAM_ERROR);
        }
        Result<Void> result = service.update(entity);
        return R.result(result);
    }

    @DeleteMapping("maintenance_order_progress/{entityId}")
    @Operation(summary = "删除对象")
    public R delete(@PathVariable Long entityId){
        Result<Void> result = service.deleteById(entityId);
        return R.result(result);
        }

    @GetMapping("maintenance_order_progress/{entityId}")
    @Operation(summary = "获取对象")
    public R get(@PathVariable Long entityId){
        Result<MaintenanceOrderProgressEntity> result = service.getById(entityId);
        return R.result(result);
        }

    private R checkParamAndDoSomething(MaintenanceOrderProgressEntity entity, Function<MaintenanceOrderProgressEntity,Result> func){
        //TODO: do check params
        //if (BeanUtilsIntensifier.checkBeanAndProperties(entity, MaintenanceOrderProgressEntity::getName)) {
        //    return R.error(ServiceCodeEnum.CODE_PARAM_ERROR);
        //}
        Result result = func.apply(entity);
        return R.result(result);
    }
    
}
