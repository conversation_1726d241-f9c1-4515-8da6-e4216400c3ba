package com.nti56.dcm.server.controller;

import com.nti56.dcm.server.entity.SparesTypeEntity;
import com.nti56.dcm.server.model.vo.SparesTypeVo;
import com.nti56.dcm.server.service.SparesTypeService;
import com.nti56.nlink.common.dto.TenantIsolation;
import com.nti56.nlink.common.util.*;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <p>
 * 备件类型表 控制器
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @Ddate 2024-05-09 10:26:45
 * @since JDK 1.8
 */
@RestController
@RequestMapping("/sparesType")
@Tag(name = "备件类型表模块")
public class SparesTypeController {

    @Autowired
    private SparesTypeService sparesTypeService;

    @GetMapping("getTree")
    @Operation(summary = "获取备件类型树形数据")
    public R getTree(@RequestHeader("dcm_headers") TenantIsolation tenantIsolation,
                  SparesTypeEntity entity){
        Result<List<SparesTypeVo>> result = sparesTypeService.getTree(tenantIsolation, entity);
        return R.result(result);
    }

    @PostMapping("")
    @Operation(summary = "创建对象")
    public R create(@RequestHeader("dcm_headers") TenantIsolation tenantIsolation,
                    @RequestBody SparesTypeEntity entity){
        return R.result(sparesTypeService.save(tenantIsolation,entity));
    }

    @PutMapping("")
    @Operation(summary = "更新对象")
    public R update(@RequestHeader("dcm_headers") TenantIsolation tenantIsolation,
                    @RequestBody SparesTypeEntity entity){
        return R.result(sparesTypeService.update(tenantIsolation, entity));
    }

    @DeleteMapping("/{entityId}")
    @Operation(summary = "删除对象")
    public R delete(@RequestHeader("dcm_headers") TenantIsolation tenantIsolation, @PathVariable("entityId") Long entityId){
        return R.result(sparesTypeService.deleteById(tenantIsolation, entityId));
    }

    @GetMapping("/{entityId}")
    @Operation(summary = "获取对象")
    public R get(@RequestHeader("dcm_headers") TenantIsolation tenantIsolation, @PathVariable("entityId") Long entityId){
        return R.result(sparesTypeService.getById(tenantIsolation, entityId));
    }
    
}
