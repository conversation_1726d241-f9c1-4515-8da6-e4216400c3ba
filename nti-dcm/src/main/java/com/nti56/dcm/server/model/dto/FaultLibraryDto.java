package com.nti56.dcm.server.model.dto;

import lombok.Data;

import java.util.Set;

@Data
public class FaultLibraryDto {

    /**
     * id
     */
    private Long id;

    /**
     * 租户id
     */
    private Long tenantId;

    /**
     * 身份，1-供应商，2-客户
     */
    private Integer idType;

    /**
     * 设备类型id
     */
    private Long deviceTypeId;

    private Set<Long> deviceTypeIds;

    /**
     * 设备类型名称
     */
    private String deviceTypeName;

    /**
     * 故障代码
     */
    private String faultCode;

    /**
     * 故障事件名称
     */
    private String faultEvent;

    /**
     * 故障描述
     */
    private String faultDesc;

    /**
     * 解决方法
     */
    private String solveMethod;

    /**
     * 预防策略
     */
    private String preventStrategy;

    private Long customerId;

}
