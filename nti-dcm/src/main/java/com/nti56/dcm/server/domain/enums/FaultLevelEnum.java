package com.nti56.dcm.server.domain.enums;

import lombok.Getter;

public enum FaultLevelEnum {
    NORMAL(1, "normal", "一般"),
    WARN(2, "warn", "警告"),
    SERIOUS(3, "serious", "严重"),
    FATAL(4, "fatal", "致命"),
    ;

    @Getter
    private Integer value;

    @Getter
    private String name;

    @Getter
    private String nameDesc;

    FaultLevelEnum(Integer value, String name, String nameDesc) {
        this.value = value;
        this.name = name;
        this.nameDesc = nameDesc;
    }

    public static FaultLevelEnum typeOfValue(Integer value){
        FaultLevelEnum[] values = FaultLevelEnum.values();
        for (FaultLevelEnum v : values) {
            if (v.value.equals(value)) {
                return v;
            }
        }
        return null;
    }

    public static FaultLevelEnum typeOfName(String name){
        FaultLevelEnum[] values = FaultLevelEnum.values();
        for (FaultLevelEnum v : values) {
            if (v.name.equals(name)) {
                return v;
            }
        }
        return null;
    }

    public static FaultLevelEnum typeOfNameDesc(String nameDesc){
        FaultLevelEnum[] values = FaultLevelEnum.values();
        for (FaultLevelEnum v : values) {
            if (v.nameDesc.equals(nameDesc)) {
                return v;
            }
        }
        return null;
    }
}
