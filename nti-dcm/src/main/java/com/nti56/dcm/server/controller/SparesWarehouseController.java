package com.nti56.dcm.server.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.nti56.dcm.server.entity.SparesWarehouseEntity;
import com.nti56.dcm.server.service.SparesWarehouseService;
import com.nti56.nlink.common.dto.TenantIsolation;
import com.nti56.nlink.common.util.*;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <p>
 * 备件仓库表 控制器
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @Ddate 2024-05-09 10:26:45
 * @since JDK 1.8
 */
@RestController
@RequestMapping("/sparesWarehouse")
@Tag(name = "备件仓库表模块")
public class SparesWarehouseController {

    @Autowired
    private SparesWarehouseService sparesWarehouseService;

    @GetMapping("page")
    @Operation(summary = "获取分页")
    public R page(@RequestHeader("dcm_headers") TenantIsolation tenantIsolation,
                  PageParam pageParam, SparesWarehouseEntity entity){
        Page<SparesWarehouseEntity> page = pageParam.toPage(SparesWarehouseEntity.class);
        Result<Page<SparesWarehouseEntity>> result = sparesWarehouseService.getPage(tenantIsolation, entity, page);
        return R.result(result);
    }

    @GetMapping("list")
    @Operation(summary = "获取列表" )
    public R list(@RequestHeader("dcm_headers") TenantIsolation tenantIsolation, SparesWarehouseEntity entity){
        Result<List<SparesWarehouseEntity>> result = sparesWarehouseService.list(tenantIsolation, entity);
        return R.result(result);
    }

    @PostMapping("")
    @Operation(summary = "创建对象")
    public R create(@RequestHeader("dcm_headers") TenantIsolation tenantIsolation,
                    @RequestBody SparesWarehouseEntity entity){
        return R.result(sparesWarehouseService.save(tenantIsolation,entity));
    }

    @PutMapping("")
    @Operation(summary = "更新对象")
    public R update(@RequestHeader("dcm_headers") TenantIsolation tenantIsolation,
                    @RequestBody SparesWarehouseEntity entity){
        return R.result(sparesWarehouseService.update(tenantIsolation, entity));
    }

    @DeleteMapping("/{entityId}")
    @Operation(summary = "删除对象")
    public R delete(@RequestHeader("dcm_headers") TenantIsolation tenantIsolation, @PathVariable("entityId") Long entityId){
        return R.result(sparesWarehouseService.deleteById(tenantIsolation, entityId));
        }

    @GetMapping("/{entityId}")
    @Operation(summary = "获取对象")
    public R get(@RequestHeader("dcm_headers") TenantIsolation tenantIsolation, @PathVariable("entityId") Long entityId){
        return R.result(sparesWarehouseService.getById(tenantIsolation, entityId));
    }
    
}
