package com.nti56.dcm.server.controller.openApi;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.nti56.common.util.R;
import com.nti56.dcm.server.domain.enums.IdTypeEnum;
import com.nti56.dcm.server.entity.InspectOrderEntity;
import com.nti56.dcm.server.entity.InspectPlanEntity;
import com.nti56.dcm.server.model.dto.*;
import com.nti56.dcm.server.model.vo.InspectOrderPage;
import com.nti56.dcm.server.model.vo.InspectPlanVo;
import com.nti56.dcm.server.service.*;
import com.nti56.nlink.common.dto.TenantIsolation;
import com.nti56.nlink.common.util.PageParam;
import com.nti56.nlink.common.util.Result;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.validation.constraints.NotNull;

/**
 * 开放api，巡检
 */
@RestController
@RequestMapping("/openapi/inspect")
@Slf4j
public class OpenApiInspectController {
    
    @Autowired
    private InspectOrderService inspectOrderService;
    
    @Autowired
    private InspectPlanService inspectPlanService;

    /**
     * 供应商获取点巡检计划分页
     */
    @PostMapping("/pageInspectPlan")
    public R<Page<InspectPlanVo>> pageInspectPlan(@RequestBody InspectPlanParams params){
        TenantIsolation tenant = new TenantIsolation();
        tenant.setTenantId(params.getTenantId());
        tenant.setIdType(IdTypeEnum.SUPPLIER.getValue());

        Page<InspectPlanEntity> page = params.toPage(InspectPlanEntity.class);
        Result<Page<InspectPlanVo>> result = inspectPlanService.getPage(tenant.getTenantId(), tenant.getIdType(), params,page);
        return R.result(result);
    }

    /**
     * 供应商获取点巡检工单分页
     */
    @PostMapping("/pageInspectOrder")
    public R<InspectOrderPage> pageInspectOrder(@RequestBody InspectOrderParams params){
        TenantIsolation tenant = new TenantIsolation();
        tenant.setTenantId(params.getTenantId());
        tenant.setIdType(IdTypeEnum.SUPPLIER.getValue());

        Page<InspectOrderEntity> page = params.toPage(InspectOrderEntity.class);
        Result<InspectOrderPage> result = inspectOrderService.pageByParams(tenant.getTenantId(), tenant.getIdType(), params, page);
        return R.result(result);
    }

}
