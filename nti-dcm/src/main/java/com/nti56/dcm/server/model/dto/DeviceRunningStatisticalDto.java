package com.nti56.dcm.server.model.dto;

import com.nti56.nlink.common.util.PageParam;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;


@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class DeviceRunningStatisticalDto extends PageParam implements Serializable {


    private Long deviceId;
    private String deviceName;
    private String deviceNo;
    private String deviceAliasName;
    private Long deviceTypeId;
    private String deviceTypeName;

    private String onlineTime;
    private String taskSpend;
    private String freeTime;
    private String faultTime;
    private String faultCount;
    private double useRate;
    private double faultRate;

}