package com.nti56.dcm.server.schedule;

import com.nti56.dcm.server.service.StockAlarmService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

/**
 * 报表定时任务
 */
@Slf4j
@Component
public class StockSchedule {

    @Autowired
    private StockAlarmService stockAlarmService;

    /**
     * 12小时执行一次
     */
    @Scheduled(cron = "0 0 */12 * * ?")
    public void dailyReportTask() {
        log.info("开始执行wms库存监测任务");

        stockAlarmService.stockAlarm();

        log.info("结束执行wms库存监测任务");
    }

}
