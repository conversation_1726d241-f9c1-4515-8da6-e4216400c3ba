package com.nti56.dcm.server.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.nti56.dcm.server.entity.DeviceLocationEntity;
import com.nti56.dcm.server.entity.DeviceTypeEntity;
import com.nti56.dcm.server.entity.DeviceTypeGroupEntity;
import com.nti56.dcm.server.model.dto.ChangeStatusDto;
import com.nti56.dcm.server.model.dto.DeviceLocationDto;
import com.nti56.dcm.server.model.dto.DeviceLocationTreeDto;
import com.nti56.dcm.server.model.dto.DeviceTypeDto;
import com.nti56.dcm.server.service.DeviceLocationService;
import com.nti56.dcm.server.service.DeviceTypeGroupService;
import com.nti56.nlink.common.dto.TenantIsolation;
import com.nti56.nlink.common.util.PageParam;
import com.nti56.common.util.R;
import com.nti56.nlink.common.util.Result;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/deviceLocation")
@Tag(name = "设备存放位置")
public class DeviceLocationController {

    @Autowired
    private DeviceLocationService deviceLocationService;


    @GetMapping("/page")
    @Operation(summary = "存放空间分页")
    public R<Page<DeviceLocationDto>> locationPage(@RequestHeader("dcm_headers") TenantIsolation tenantIsolation, PageParam pageParam, DeviceLocationDto deviceLocationDto) {
        Page<DeviceLocationDto> page = pageParam.toPage(DeviceLocationDto.class);
        Result<Page<DeviceLocationDto>> result = deviceLocationService.getPage(deviceLocationDto, page, tenantIsolation);
        return R.result(result);
    }

    @GetMapping("/getNext")
    @Operation(summary = "子空间获取")
    public R<List<DeviceLocationDto>> getNext(@RequestHeader("dcm_headers") TenantIsolation tenantIsolation, @RequestParam("parentId") Long parentId) {
        Result<List<DeviceLocationDto>> result = deviceLocationService.getNextSpaces(parentId, tenantIsolation);
        return R.result(result);
    }


    /**
     * 新增设备存放位置
     *
     * @param tenantIsolation
     * @param deviceLocationDto
     * @return
     */
    @PostMapping("")
    public R<DeviceLocationEntity> create(@RequestHeader("dcm_headers") TenantIsolation tenantIsolation,
                    @RequestBody @Validated DeviceLocationDto deviceLocationDto) {
        return R.result(deviceLocationService.create(deviceLocationDto, tenantIsolation));
    }


    @PutMapping("/{id}")
    @Operation(summary = "修改设备存放位置")
    public R<Void> edit(@RequestHeader("dcm_headers") TenantIsolation tenantIsolation,
                  @RequestBody @Validated DeviceLocationDto deviceLocationDto) {
        return R.result(deviceLocationService.edit(deviceLocationDto, tenantIsolation));
    }

    @DeleteMapping("/{id}")
    @Operation(summary = "根据id删除设备存放位置")
    public R delete(@RequestHeader("dcm_headers") TenantIsolation tenantIsolation,
                    @PathVariable Long id) {
        return R.result(deviceLocationService.deleteById(id, tenantIsolation));
    }

    @GetMapping("/locationTree")
    @Operation(summary = "获取设备空间树树")
    public R<List<DeviceLocationTreeDto>> typeTree(@RequestHeader("dcm_headers") TenantIsolation tenantIsolation, DeviceLocationDto deviceLocationDto){
        return R.result(deviceLocationService.getTree(tenantIsolation,deviceLocationDto));
    }

    


    /*@GetMapping("/dict")
    @Operation(summary = "获取设备位置字典/下拉数据")
    public R typeDict(@RequestHeader("dcm_headers") TenantIsolation tenantIsolation) {
        return R.result(deviceLocationService.getDeviceLocationDict(tenantIsolation));
    }*/


    /*@PostMapping("/changeStatus")
    @Operation(summary = "批量更改启用、停用状态")
    public R batchChangeStatus(@RequestHeader("dcm_headers") TenantIsolation tenantIsolation,
                               @RequestBody ChangeStatusDto changeStatusDto) {
        return R.result(deviceLocationService.batchUpdateStatus(changeStatusDto, tenantIsolation));
    }*/

}
