package com.nti56.dcm.server.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 保养工单进度表 实体类
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @Ddate 2024-03-01 16:28:19
 * @since JDK 1.8
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@TableName("maintenance_order_progress")
public class MaintenanceOrderProgressEntity implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
    * ID
    */
    private Long id;

    /**
    * 保养工单ID
    */
    private Long maintenanceOrderId;

    /**
    * 工单进度 1-生成工单，2-派工 3-接收工单，4-执行保养 5-执行验收，6-发起，7-供应商派工，8-供应商接收工单，9-供应商执行检查，10-供应商验收，11-撤销
    */
    private Integer status;

    /**
    * 进度执行人id
    */
    private Long userId;

    /**
    * 进度执行人姓名
    */
    private String userName;

    /**
    * 进度时间
    */
    private LocalDateTime progressTime;

    /**
    * 保养开始时间
    */
    private LocalDateTime maintenanceBegin;

    /**
    * 保养结束时间
    */
    private LocalDateTime maintenanceEnd;

    /**
    * 保养耗时(单位：分钟)
    */
    private Integer maintenanceCostTime;

    /**
    * 验收结果，1-通过，0-不通过
    */
    private Integer acceptResult;

    /**
    * 验收说明
    */
    private String acceptDesc;

    /**
    * 创建人
    */
    @TableField(fill = FieldFill.INSERT)
    private String creator;

    /**
    * 创建时间
    */
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
    * 修改人
    */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private String updator;

    /**
    * 修改时间
    */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    /**
    * 租户ID
    */
    @TableField(fill = FieldFill.INSERT)
    private Long tenantId;

    /**
    * 工程ID
    */
    @TableField(fill = FieldFill.INSERT)
    private Long engineeringId;

    /**
    * 模块ID
    */
    @TableField(fill = FieldFill.INSERT)
    private Long moduleId;

    /**
    * 空间ID
    */
    @TableField(fill = FieldFill.INSERT)
    private Long spaceId;

    /**
    * 版本号
    */
    @Version
    private Integer version;

    /**
    * 删除
    */
    @TableLogic
    private Integer deleted;

    /**
     * 耗时，文本
     * @return
     */
    public String getMaintenanceCostTimeStr(){
        Integer t = getMaintenanceCostTime();
        if(t == null){
            return "";
        }
        if(t < 60){
            return t + "分";
        }else{
            Integer hour = t/60;
            Integer minute = t%60;
            String s = hour + "小时";
            if(minute >= 0){
                s += minute + "分";
            }
            return s;
        }
    }


}
