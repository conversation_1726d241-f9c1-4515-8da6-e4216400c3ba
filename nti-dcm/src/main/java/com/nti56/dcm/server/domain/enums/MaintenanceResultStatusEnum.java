package com.nti56.dcm.server.domain.enums;

import lombok.Getter;

public enum MaintenanceResultStatusEnum {
    MAINTENANCE(1, "MAINTENANCE", "已保养"),
    NOT_MAINTENANCE(0, "NOT_MAINTENANCE", "未保养"),
    ;

    @Getter
    private Integer value;

    @Getter
    private String name;

    @Getter
    private String nameDesc;

    MaintenanceResultStatusEnum(Integer value, String name, String nameDesc) {
        this.value = value;
        this.name = name;
        this.nameDesc = nameDesc;
    }

    public static MaintenanceResultStatusEnum typeOfValue(Integer value){
        MaintenanceResultStatusEnum[] values = MaintenanceResultStatusEnum.values();
        for (MaintenanceResultStatusEnum v : values) {
            if (v.value.equals(value)) {
                return v;
            }
        }
        return null;
    }

    public static MaintenanceResultStatusEnum typeOfName(String name){
        MaintenanceResultStatusEnum[] values = MaintenanceResultStatusEnum.values();
        for (MaintenanceResultStatusEnum v : values) {
            if (v.name.equals(name)) {
                return v;
            }
        }
        return null;
    }

    public static MaintenanceResultStatusEnum typeOfNameDesc(String nameDesc){
        MaintenanceResultStatusEnum[] values = MaintenanceResultStatusEnum.values();
        for (MaintenanceResultStatusEnum v : values) {
            if (v.nameDesc.equals(nameDesc)) {
                return v;
            }
        }
        return null;
    }
}
