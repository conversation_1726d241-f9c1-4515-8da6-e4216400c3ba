package com.nti56.dcm.server.controller;


import com.nti56.common.util.R;
import com.nti56.dcm.server.entity.DeviceAssetChangeRecordEntity;
import com.nti56.dcm.server.model.dto.DeviceAssetChangeRecordDto;
import com.nti56.dcm.server.service.IDeviceAssetChangeRecordService;
import com.nti56.nlink.common.dto.TenantIsolation;
import com.nti56.nlink.common.util.Result;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;

import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <p>
 * 设备资产变更记录表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-13
 */
@RestController
@RequestMapping("/deviceAssetChangeRecord")
@Tag(name = "设备资产变更记录")
public class DeviceAssetChangeRecordController {

    @Autowired
    private IDeviceAssetChangeRecordService deviceAssetChangeRecordService;


    @GetMapping("/listByDeviceId")
    @Operation(summary = "查询设备资产变更记录")
    public R getTree(@RequestHeader("dcm_headers") TenantIsolation tenantIsolation, DeviceAssetChangeRecordDto dto) {
        Result<List<DeviceAssetChangeRecordDto>> result = deviceAssetChangeRecordService.listByDeviceId(tenantIsolation, dto);
        return R.result(result);
    }


    @PostMapping("")
    @Operation(summary = "新增设备资产变更记录")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "返回值",
                    content = {@Content(mediaType = MediaType.APPLICATION_JSON_VALUE,
                            schema = @Schema(implementation = DeviceAssetChangeRecordEntity.class)
                    )})
    })
    public R createDeviceAssetChangeRecord(@RequestHeader("dcm_headers") TenantIsolation tenantIsolation,
                                         @RequestBody @Validated DeviceAssetChangeRecordDto dto) {
        return R.result(deviceAssetChangeRecordService.create(dto, tenantIsolation));
    }


    @PutMapping("/{id}")
    @Operation(summary = "修改设备资产变更记录")
    public R editDeviceAssetChangeRecord(@RequestHeader("dcm_headers") TenantIsolation tenantIsolation,
                                       @RequestBody @Validated DeviceAssetChangeRecordDto dto) {
        return R.result(deviceAssetChangeRecordService.edit(dto, tenantIsolation));
    }

    @DeleteMapping("/{id}")
    @Operation(summary = "根据id删除设备资产变更记录")
    public R deleteDeviceAssetChangeRecord(@RequestHeader("dcm_headers") TenantIsolation tenantIsolation,
                                         @PathVariable Long id) {
        return R.result(deviceAssetChangeRecordService.deleteById(id, tenantIsolation));
    }
}
