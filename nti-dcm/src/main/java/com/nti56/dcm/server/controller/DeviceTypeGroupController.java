package com.nti56.dcm.server.controller;

import com.nti56.common.util.R;
import com.nti56.dcm.server.entity.DeviceTypeGroupEntity;
import com.nti56.dcm.server.model.dto.DeviceTypeGroupChangeDto;
import com.nti56.dcm.server.model.dto.DeviceTypeGroupDto;
import com.nti56.dcm.server.model.dto.DeviceTypeGroupTreeDto;
import com.nti56.dcm.server.service.DeviceTypeGroupService;
import com.nti56.nlink.common.dto.TenantIsolation;
import com.nti56.nlink.common.util.Result;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@RestController
@RequestMapping("/deviceTypeGroup")
@Tag(name = "设备类型分组")
public class DeviceTypeGroupController {

    @Autowired
    private DeviceTypeGroupService deviceTypeGroupService;


    @GetMapping("/getGroupTree")
    @Operation(summary = "获取设备类型分组树形数据,只包含分组信息")
    public R getTree(@RequestHeader("dcm_headers") TenantIsolation tenantIsolation) {
        Result<List<DeviceTypeGroupTreeDto>> result = deviceTypeGroupService.getGroupTree( tenantIsolation);
        return R.result(result);
    }

    @GetMapping("/getTree")
    @Operation(summary = "获取设备类型分组树形数据,可根据传入分组id排除此分组")
    public R getTree(@RequestHeader("dcm_headers") TenantIsolation tenantIsolation, Integer type) {
        Result<List<DeviceTypeGroupTreeDto>> result = deviceTypeGroupService.getTypeGroupTree(type, tenantIsolation);
        return R.result(result);
    }


    @GetMapping("/getTreeById")
    @Operation(summary = "根据指定设备类型分组获取设备类型分组树形数据")
    public R getTreeById(@RequestHeader("dcm_headers") TenantIsolation tenantIsolation, Long id) {
        Result<List<DeviceTypeGroupTreeDto>> result = deviceTypeGroupService.getTypeGroupTreeById(tenantIsolation, id);
        return R.result(result);
    }

    @PostMapping("")
    @Operation(summary = "新增设备类型分组")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "返回值",
                    content = {@Content(mediaType = MediaType.APPLICATION_JSON_VALUE,
                            schema = @Schema(implementation = DeviceTypeGroupEntity.class)
                    )})
    })
    public R createDeviceType(@RequestHeader("dcm_headers") TenantIsolation tenantIsolation,
                              @RequestBody @Validated DeviceTypeGroupDto dto) {
        return R.result(deviceTypeGroupService.createDeviceTypeGroup(dto, tenantIsolation));
    }


    @PutMapping("/{id}")
    @Operation(summary = "修改设备类型分组")
    public R editChannel(@RequestHeader("dcm_headers") TenantIsolation tenantIsolation,
                         @RequestBody @Validated DeviceTypeGroupDto dto) {
        return R.result(deviceTypeGroupService.editDeviceTypeGroup(dto, tenantIsolation));
    }

    @DeleteMapping("/{id}")
    @Operation(summary = "根据id删除设备类型分组")
    public R deleteDeviceType(@RequestHeader("dcm_headers") TenantIsolation tenantIsolation,
                              @PathVariable Long id) {
        return R.result(deviceTypeGroupService.deleteDeviceTypeGroup(id, tenantIsolation));
    }

    @PostMapping("/changeGroup")
    @Operation(summary = "迁移设备类型分组")
    public R createDeviceType(@RequestHeader("dcm_headers") TenantIsolation tenantIsolation,
                              @RequestBody @Validated DeviceTypeGroupChangeDto dto) {
        return R.result(deviceTypeGroupService.changeGroup(dto, tenantIsolation));
    }

}
