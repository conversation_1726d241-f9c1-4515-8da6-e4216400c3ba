package com.nti56.dcm.server.domain.enums;

import lombok.Getter;

public enum RelationStatusEnum {
    WAIT_PROCESS(0, "WAIT_PROCESS", "待通过"),
    APPROVE(1, "APPROVE", "已通过"),
    REFUSE(2, "REFUS<PERSON>", "未通过"),
    DISSOLVE(3, "DISSOLVE", "已解除"),
    ;

    @Getter
    private Integer value;

    @Getter
    private String name;

    @Getter
    private String nameDesc;

    RelationStatusEnum(Integer value, String name, String nameDesc) {
        this.value = value;
        this.name = name;
        this.nameDesc = nameDesc;
    }

    public static RelationStatusEnum typeOfValue(Integer value){
        RelationStatusEnum[] values = RelationStatusEnum.values();
        for (RelationStatusEnum v : values) {
            if (v.value.equals(value)) {
                return v;
            }
        }
        return null;
    }

    public static RelationStatusEnum typeOfName(String name){
        RelationStatusEnum[] values = RelationStatusEnum.values();
        for (RelationStatusEnum v : values) {
            if (v.name.equals(name)) {
                return v;
            }
        }
        return null;
    }

    public static RelationStatusEnum typeOfNameDesc(String nameDesc){
        RelationStatusEnum[] values = RelationStatusEnum.values();
        for (RelationStatusEnum v : values) {
            if (v.nameDesc.equals(nameDesc)) {
                return v;
            }
        }
        return null;
    }
}
