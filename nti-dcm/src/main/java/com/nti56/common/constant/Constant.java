package com.nti56.common.constant;

public class Constant {


    public static final String APP_CODE_STR = "appCode";
    /**
     * 验证失败跳转执行controller
     */
    public static final String AUTH_FAILED_PATH = "/auth/failed";

    public static final String WEB_FILTER_ATTR_NAME = "filterchain";
    //认证失败属性名
    public static final String AUTH_ERROR_ATTR_NAME = "auth-error";

    public static final String TOKEN_HEADER = "Authorization";

    public static final String LOGIN_TOKEN = "Logintoken";

    public static final String CLIENT_ID = "Clientid";

    public static final String TOKEN_PREFIX = "Bearer ";

    public static final String TENANT_HEADER = "dcm_headers";

    public static final String APPCODE_HEADER = "dcm";

    public static final String EQUIP_ID = "Equip_Id";

    public static final String EQUIP_TYPE = "Equip_Type";

    public static final String REQUEST_SOURCE = "Request_Source";

    public static final String DEFAULT_ROLE_NAME_1 = "管理员";

    public static final String DEFAULT_ROLE_NAME_2 = "设备维保班长";

    public static final String DEFAULT_ROLE_NAME_3 = "供应商管理员";

    public static final String DEFAULT_ROLE_NAME_4 = "供应商设备维保班长";


    public static final Integer ROLE_BUSSINESS_TYPE = 1;

    public static int MENU_CATEGORY = 1; // 1:pc端菜单 2:移动端菜单


}
