package com.nti56.dcm.server.domain.enums;

import lombok.Getter;

public enum DeviceConnectEnum {
    DEVICE_MONITOR(1, "device_monitor", "使用设备监控的客户"),
    HAVE_DEVICE(2, "have_device", "仅使用台账的客户"),
    NO_DEVICE(3, "no_device", "未接入设备的客户"),
    ;

    @Getter
    private Integer value;

    @Getter
    private String name;

    @Getter
    private String nameDesc;

    DeviceConnectEnum(Integer value, String name, String nameDesc) {
        this.value = value;
        this.name = name;
        this.nameDesc = nameDesc;
    }

    public static DeviceConnectEnum typeOfValue(Integer value){
        DeviceConnectEnum[] values = DeviceConnectEnum.values();
        for (DeviceConnectEnum v : values) {
            if (v.value.equals(value)) {
                return v;
            }
        }
        return null;
    }

    public static DeviceConnectEnum typeOfName(String name){
        DeviceConnectEnum[] values = DeviceConnectEnum.values();
        for (DeviceConnectEnum v : values) {
            if (v.name.equals(name)) {
                return v;
            }
        }
        return null;
    }

    public static DeviceConnectEnum typeOfNameDesc(String nameDesc){
        DeviceConnectEnum[] values = DeviceConnectEnum.values();
        for (DeviceConnectEnum v : values) {
            if (v.nameDesc.equals(nameDesc)) {
                return v;
            }
        }
        return null;
    }
}
