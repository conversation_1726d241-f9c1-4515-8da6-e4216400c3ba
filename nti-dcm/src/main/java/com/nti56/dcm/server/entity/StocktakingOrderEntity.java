package com.nti56.dcm.server.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import lombok.Data;

/**
 * <p>
 * 盘点单表 实体类
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @Ddate 2024-05-08 13:49:40
 * @since JDK 1.8
 */
@Data
@TableName("stocktaking_order")
public class StocktakingOrderEntity extends BaseEntity{
        /**
        * 盘点单编码
        */
        private String orderNumber;

        /**
        * 盘点的备件仓库
        */
        private Long sparesWarehouseId;

        /**
        * 全部区域，1-全部，0-部分
        */
        private Integer allArea;

        /**
        * 全部货位，1-全部，0-部分
        */
        private Integer allLocation;

        /**
        * 全部备件类型，1-全部，0-部分
        */
        private Integer allSparesType;

        /**
        * 全部备件，1-全部，0-部分
        */
        private Integer allSpares;

        /**
        * 盘点区域，多个区域id用逗号分隔
        */
        private String sparesAreaIds;

        /**
        * 盘点类型，多个类型id用逗号分隔
        */
        private String sparesTypeIds;

        /**
        * 盘点单状态，1-未开始，2-执行中，3-已完成
        */
        private Integer status;



}
