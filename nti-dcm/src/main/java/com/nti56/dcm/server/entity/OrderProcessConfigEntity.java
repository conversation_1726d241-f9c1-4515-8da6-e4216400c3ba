package com.nti56.dcm.server.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.handlers.FastjsonTypeHandler;
import com.nti56.dcm.server.domain.enums.AcceptanceModeEnum;
import com.nti56.dcm.server.model.dto.ResponsibleDto;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <p>
 * 工单流程配置表 实体类
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @Ddate 2024-06-17 09:27:30
 * @since JDK 1.8
 */
@Data
@TableName(value = "order_process_config",autoResultMap = true)
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class OrderProcessConfigEntity extends BaseEntity{

        /**
        * 所属身份,1-供应商，2-客户
        */
        private Integer idType;

        /**
        * 工单类型，1-维修，2-保养，3-巡检
        */
        private Integer orderType;

        /**
         * 是否外委，1-是，2-否
         */
        private Boolean isOutsourcing;
        /**
         * 是否数据型，1-是，2-否
         */
        private Boolean isDataRecord;
        /**
         * 是否自动派工，1-是，0-否
         */
        private Boolean isAutoDispatch;
        /**
         * 派工责任人
         */
        @TableField(typeHandler = FastjsonTypeHandler.class)
        private ResponsibleDto[] dispatchResponsible;

        /**
         * 是否允许转派，1-是，0-否
         */
        private Boolean allowForward;
        /**
         * 验收模式，0-默认不验收直接通过，1-指定人验收，2-发起人验收 {@link AcceptanceModeEnum}
         */
        private Integer acceptanceMode;

        /**
         * 验收人
         */
        @TableField(typeHandler = FastjsonTypeHandler.class)
        private ResponsibleDto[] acceptor;

        /**
         * 流程配置
         */
        private String processConfig;

        /**
         * 工作流ID
         */
        private String flowId;








}
