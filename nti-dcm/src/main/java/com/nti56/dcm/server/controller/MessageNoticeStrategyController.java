package com.nti56.dcm.server.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.nti56.dcm.server.entity.MessageNoticeStrategyEntity;
import com.nti56.dcm.server.model.dto.MessageNoticeStrategyDeviceOverrideDto;
import com.nti56.dcm.server.model.dto.MessageNoticeStrategyEditDto;
import com.nti56.dcm.server.model.dto.MessageNoticeStrategyQueryDto;
import com.nti56.dcm.server.model.vo.MessageNoticeStrategyVo;
import com.nti56.dcm.server.service.DeviceService;
import com.nti56.dcm.server.service.IMessageNoticeStrategyDeviceService;
import com.nti56.dcm.server.service.MessageNoticeStrategyService;
import com.nti56.nlink.common.dto.TenantIsolation;
import com.nti56.nlink.common.util.PageParam;
import com.nti56.nlink.common.util.R;
import com.nti56.nlink.common.util.Result;
import io.swagger.annotations.ApiOperation;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;


/**
 * <p>
 * 消息通知策略表 控制器
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @Ddate 2024-06-24 14:01:33
 * @since JDK 1.8
 */
@RestController
@RequestMapping("messageNoticeStrategy")
@Tag(name = "消息通知策略表模块")
public class MessageNoticeStrategyController {

    @Autowired
    private MessageNoticeStrategyService service;

    @Autowired
    private IMessageNoticeStrategyDeviceService strategyDeviceService;

    @Autowired
    private DeviceService deviceService;

    @GetMapping("page")
    @Operation(summary = "获取分页")
    public R page(@RequestHeader("dcm_headers") TenantIsolation tenantIsolation, PageParam pageParam, MessageNoticeStrategyQueryDto dto) {
        Page<MessageNoticeStrategyQueryDto> page = pageParam.toPage(MessageNoticeStrategyQueryDto.class);
        Result<Page<MessageNoticeStrategyVo>> result = service.getPage(tenantIsolation, dto, page);
        return R.result(result);
    }


    @PostMapping("create")
    @ApiOperation("创建对象")
    public R create(@RequestHeader("dcm_headers") TenantIsolation tenantIsolation, @RequestBody @Validated MessageNoticeStrategyEditDto dto) {
        return R.result(service.save(tenantIsolation, dto));
    }

    @PutMapping("update")
    @ApiOperation("更新")
    public R update(@RequestHeader("dcm_headers") TenantIsolation tenantIsolation, @RequestBody @Validated MessageNoticeStrategyEditDto dto) {
        return R.result(service.update(tenantIsolation, dto));
    }

    @DeleteMapping("/{entityId}")
    @ApiOperation("删除")
    public R delete(@PathVariable Long entityId, @RequestHeader("dcm_headers") TenantIsolation tenantIsolation) {
        Result<Void> result = service.deleteById(entityId, tenantIsolation);
        return R.result(result);
    }

    @PutMapping("changeEnableStatus")
    @ApiOperation("更新启用状态,传入id和status")
    public R update(@RequestHeader("dcm_headers") TenantIsolation tenantIsolation, @RequestBody MessageNoticeStrategyEntity entity) {
        return R.result(service.changeEnableStatus(entity, tenantIsolation));
    }

    @PostMapping("/overrideDevice")
    @ApiOperation("覆盖设备")
    public R overrideDevice(@RequestHeader("dcm_headers") TenantIsolation tenantIsolation, @RequestBody MessageNoticeStrategyDeviceOverrideDto dto) {
        return R.result(strategyDeviceService.overrideDevice(tenantIsolation, dto));
    }

    @GetMapping("monitor")
    public R monitor() {
        service.deviceExpireMonitor();
        //service.handleDeviceExpireRecord();
        // 记录保存
        deviceService.deviceExpireMonitor();
        return R.result(Result.ok());
    }

}
