package com.nti56.dcm.server.model.dto;

import com.nti56.nlink.common.util.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDateTime;


@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class DeviceRunningQueryDto extends PageParam implements Serializable {


    /**
     * 查询范围——开始时间
     */
    @Schema(description = "查询范围——开始时间")
    private LocalDateTime startDate;
    /**
     * 查询范围——结束时间
     */
    @Schema(description = "查询范围——结束时间")
    private LocalDateTime endDate;
    /**
     * 班组id
     */
    @Schema(description = "班组id")
    private Long workGroupId;

    /**
     * 人员名称
     */
    @Schema(description = "人员名称")
    private String userName;

}