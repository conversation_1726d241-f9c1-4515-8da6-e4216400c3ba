package com.nti56.dcm.server.domain;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.bean.copier.CopyOptions;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import com.nti56.dcm.server.entity.WorkGroupEntity;
import com.nti56.dcm.server.entity.WorkGroupUserEntity;
import com.nti56.dcm.server.model.dto.WorkGroupDto;
import com.nti56.nlink.common.base.FieldValue;
import com.nti56.nlink.common.base.UniqueConstraint;
import com.nti56.nlink.common.fetcher.CommonFetcher;
import com.nti56.nlink.common.util.Result;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

@Slf4j
public class WorkGroup {

    @Getter
    private WorkGroupEntity entity;


    private static final UniqueConstraint groupUniqueConstraint = new UniqueConstraint("group_name","id_type");

    public static Result<WorkGroupDto> getAllInfos(Long groupId, CommonFetcher commonFetcher) {

        if (Objects.isNull(groupId)) {
            return Result.error("班组ID为空！");
        }
        WorkGroupEntity workGroupEntity = commonFetcher.get(groupId, WorkGroupEntity.class);
        if (Objects.isNull(workGroupEntity)) {
            return Result.error("班组不存在！");
        }
        WorkGroupDto workGroupDto = BeanUtil.copyProperties(workGroupEntity, WorkGroupDto.class, "params");
        List<WorkGroupUserEntity> workGroupUserEntities = commonFetcher.list("work_group_id", groupId, WorkGroupUserEntity.class);
        workGroupDto.setWorkGroupUserList(workGroupUserEntities);
        return Result.ok(workGroupDto);
    }


    public static Result<WorkGroup> checkCreate(WorkGroupDto dto, CommonFetcher commonFetcher) {
        if (StrUtil.isBlank(dto.getGroupName())) {
            return Result.error("班组名称不能为空！");
        }
        // 校验是否存在重名
        WorkGroupEntity repeatGroup = commonFetcher.get(groupUniqueConstraint.buildUnique(new FieldValue(dto.getGroupName()),new FieldValue(dto.getIdType())), WorkGroupEntity.class);
        if (ObjUtil.isNotNull(repeatGroup)) {
            return Result.error("班组名称已存在：" + dto.getGroupName());
        }
        WorkGroupEntity entity = new WorkGroupEntity();
        BeanUtils.copyProperties(dto, entity);
        entity.setGroupPurpose(JSONObject.toJSONString(dto.getGroupPurpose()));
        WorkGroup workGroup = new WorkGroup();
        workGroup.entity = entity;
        return Result.ok(workGroup);
    }


    public static Result<WorkGroup> checkUpdate(WorkGroupDto dto, CommonFetcher commonFetcher) {

        if (Objects.isNull(dto.getId())) {
            return Result.error("班组ID不能为空！");
        }

        WorkGroupEntity oldEntity = commonFetcher.get(dto.getId(), WorkGroupEntity.class);
        if (Objects.isNull(oldEntity)) {
            return Result.error("不存在的班组！");
        }
        // 校验是否存在重名
        WorkGroupEntity repeatGroup = commonFetcher.get(groupUniqueConstraint.buildUnique(new FieldValue(dto.getGroupName()),new FieldValue(dto.getIdType())), WorkGroupEntity.class);
        if (ObjUtil.isNotNull(repeatGroup) &&  !repeatGroup.getId().equals(oldEntity.getId())) {
            return Result.error("班组名称已存在：" + dto.getGroupName());
        }
        WorkGroup workGroup = new WorkGroup();
        BeanUtil.copyProperties(dto, oldEntity, CopyOptions.create().setIgnoreNullValue(false).setIgnoreProperties("tenantId"));
        oldEntity.setGroupPurpose(JSONObject.toJSONString(dto.getGroupPurpose()));
        workGroup.entity = oldEntity;
        return Result.ok(workGroup);
    }


    public static Result<Void> checkDelete(Long id, CommonFetcher commonFetcher) {

        WorkGroupEntity existGroup = commonFetcher.get(id, WorkGroupEntity.class);
        if (Objects.isNull(existGroup)) {
            return Result.error("班组不存在，无法删除！");
        }
        //检查是否存在班组成员
        List<WorkGroupUserEntity> workGroupUserEntityList = commonFetcher.list("work_group_id", id, WorkGroupUserEntity.class);
        if (CollectionUtil.isNotEmpty(workGroupUserEntityList)) {
            return Result.error("班组下存在用户，无法删除");
        }
        return Result.ok();
    }


}
