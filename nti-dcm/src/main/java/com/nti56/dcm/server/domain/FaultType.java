package com.nti56.dcm.server.domain;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.google.common.collect.HashMultiset;
import com.google.common.collect.Multiset;
import com.nti56.dcm.server.domain.enums.ConnectOtEnum;
import com.nti56.dcm.server.domain.enums.DeviceStateEnum;
import com.nti56.dcm.server.entity.DeviceEntity;
import com.nti56.dcm.server.entity.FaultTypeEntity;
import com.nti56.dcm.server.entity.FaultTypeEntity;
import com.nti56.dcm.server.model.dto.FaultTypeDto;
import com.nti56.dcm.server.model.vo.DeviceVo;
import com.nti56.nlink.common.base.FieldValue;
import com.nti56.nlink.common.base.UniqueConstraint;
import com.nti56.nlink.common.fetcher.CommonFetcher;
import com.nti56.nlink.common.fetcher.Preloader;
import com.nti56.nlink.common.util.Result;
import lombok.Data;
import lombok.Getter;
import org.springframework.beans.BeanUtils;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

public class FaultType {

    @Getter
    private FaultTypeEntity entity;

    @Getter
    private Boolean isRoot;

    @Getter
    private Long id;

    @Getter
    private String name;


    private static final UniqueConstraint faultTypeUniqueConstraint = new UniqueConstraint("type_name","id_type");

    public static Result<FaultType> checkCreate(FaultTypeDto dto, CommonFetcher commonFetcher) {
        if (StrUtil.isBlank(dto.getTypeName())) {
            return Result.error("故障类型名称不能为空");
        }

        // 校验同级别下是否存在设备故障类型重名
        Optional<FaultTypeEntity> repeat = commonFetcher.list("parent_id", dto.getParentId(), FaultTypeEntity.class)
                .stream()
                .filter(entity -> ObjectUtil.equals(entity.getIdType(), dto.getIdType())&& entity.getTypeName().equals(dto.getTypeName()))
                .findFirst();
        if (repeat.isPresent()){
            return Result.error("同级别下已存在故障类型：" + dto.getTypeName());
        }
        FaultTypeEntity entity = new FaultTypeEntity();
        BeanUtils.copyProperties(dto, entity);
        FaultType faultType = new FaultType();
        faultType.entity = entity;
        return Result.ok(faultType);
    }


    public static Result<FaultType> checkUpdate(FaultTypeDto dto, CommonFetcher commonFetcher) {

        if (Objects.isNull(dto.getId())) {
            return Result.error("故障类型ID不能为空！");
        }

        if (StrUtil.isBlank(dto.getTypeName())) {
            return Result.error("故障类型名称不能为空！");
        }
        FaultTypeEntity oldEntity = commonFetcher.get(dto.getId(), FaultTypeEntity.class);
        if (Objects.isNull(oldEntity)) {
            return Result.error("不存在的故障类型！");
        }

        // 校验修改后同级别下是否存在设备故障类型重名
        Optional<FaultTypeEntity> repeat = commonFetcher.list("parent_id", oldEntity.getParentId(), FaultTypeEntity.class)
                .stream()
                .filter(entity -> ObjectUtil.equals(entity.getIdType(), dto.getIdType())&& !entity.getId().equals(oldEntity.getId()) && entity.getTypeName().equals(dto.getTypeName()))
                .findFirst();
        if (repeat.isPresent()){
            return Result.error("同级别下已存在故障类型：" + dto.getTypeName());
        }

        oldEntity.setTypeName(dto.getTypeName());
        FaultType faultType = new FaultType();
        faultType.entity = oldEntity;
        return Result.ok(faultType);
    }

    public static Result<Void> checkDelete(Long id, CommonFetcher commonFetcher) {

        Result<FaultType> deviceLocationResult = checkInfo(id, commonFetcher);
        if (!deviceLocationResult.getSignal()) {
            return Result.error(deviceLocationResult.getMessage());
        }
        List<FaultTypeEntity> childrenList = commonFetcher.list("parent_id", id, FaultTypeEntity.class);
        if (CollectionUtil.isNotEmpty(childrenList)) {
            return Result.error("故障类型存在子类型，无法删除！");
        }

        return Result.ok();
    }

    public static Result<FaultType> checkInfo(Long id, CommonFetcher commonFetcher) {
        if (id == null) {
            // 
            FaultType rootDeviceLocation = new FaultType();
            rootDeviceLocation.isRoot = true;

            return Result.ok(rootDeviceLocation);
        }
        FaultTypeEntity existLocation = commonFetcher.get(id, FaultTypeEntity.class);
        if (Objects.isNull(existLocation)) {
            return Result.error("故障类型不存在，无法删除！");
        }
        FaultType faultType = new FaultType();
        faultType.isRoot = false;
        faultType.entity = existLocation;
        faultType.id = existLocation.getId();
        faultType.name = existLocation.getTypeName();
        return Result.ok(faultType);
    }


}
