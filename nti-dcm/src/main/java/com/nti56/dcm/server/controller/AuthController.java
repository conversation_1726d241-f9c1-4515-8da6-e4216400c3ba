package com.nti56.dcm.server.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.nti56.nlink.common.dto.TenantIsolation;
import com.nti56.nlink.common.util.BeanUtilsIntensifier;
import com.nti56.nlink.common.util.PageParam;
import com.nti56.common.util.R;
import com.nti56.nlink.common.util.Result;
import com.nti56.nlink.common.util.ServiceCodeEnum;
import com.nti56.dcm.server.service.AuthService;
import com.nti56.dcm.server.entity.AuthEntity;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Optional;
import java.util.function.Function;

import lombok.extern.slf4j.Slf4j;


/**
 * 权限表 控制器
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2024-02-26 16:54:01
 * @since JDK 1.8
 */
@RestController
@RequestMapping("/auth")
@Slf4j
public class AuthController {

    @Autowired
    private AuthService service;

    /**
     * 根据身份获取权限列表
     * @param idType 身份类型，1-供应商，2-客户
     */
    @GetMapping("/listByIdType/{idType}")
    public R<List<AuthEntity>> listByIdType(@RequestHeader("dcm_headers") TenantIsolation tenant, @PathVariable("idType") Integer idType){
        Result<List<AuthEntity>> result = service.listByIdType(idType);
        return R.result(result);
    }

    /**
     * 获取对象
     * @param entityId 对象id
     */
    @GetMapping("/{entityId}")
    public R<AuthEntity> get(@RequestHeader("dcm_headers") TenantIsolation tenant, @PathVariable Long entityId){
        Result<AuthEntity> result = service.getById(tenant.getTenantId(), entityId);
        return R.result(result);
    }

    // /**
    //  * 获取分页
    //  * @param pageParam 分页参数
    //  */
    // @GetMapping("/page")
    // public R<Page<AuthEntity>> page(@RequestHeader("dcm_headers") TenantIsolation tenant, PageParam pageParam,AuthEntity entity){
    //     Page<AuthEntity> page = pageParam.toPage(AuthEntity.class);
    //     Result<Page<AuthEntity>> result = service.getPage(tenant.getTenantId(), entity,page);
    //     return R.result(result);
    // }

    // /**
    //  * 获取列表
    //  */
    // @GetMapping("/list")
    // public R<List<AuthEntity>> list(@RequestHeader("dcm_headers") TenantIsolation tenant, AuthEntity entity){
    //     Result<List<AuthEntity>> result = service.list(tenant.getTenantId(), entity);
    //     return R.result(result);
    // }

    // /**
    //  * 创建对象
    //  */
    // @PostMapping("/create")
    // public R<AuthEntity> create(@RequestHeader("dcm_headers") TenantIsolation tenant, @RequestBody AuthEntity entity){
    //     Result<AuthEntity> result = service.save(tenant.getTenantId(), entity);
    //     return R.result(result);
    // }

    // /**
    //  * 更新对象
    //  */
    // @PutMapping("/update")
    // public R<Void> update(@RequestHeader("dcm_headers") TenantIsolation tenant, @RequestBody AuthEntity entity){
    //     if (BeanUtilsIntensifier.checkBeanAndProperties(entity, AuthEntity::getId)) {
    //         return R.error(ServiceCodeEnum.CODE_PARAM_ERROR.getCode());
    //     }
    //     Result<Void> result = service.update(tenant.getTenantId(), entity);
    //     return R.result(result);
    // }

    // /**
    //  * 删除对象
    //  * @param entityId 对象id
    //  */
    // @DeleteMapping("/{entityId}")
    // public R<Void> delete(@RequestHeader("dcm_headers") TenantIsolation tenant, @PathVariable Long entityId){
    //     Result<Void> result = service.deleteById(tenant.getTenantId(), entityId);
    //     return R.result(result);
    // }

    
}
