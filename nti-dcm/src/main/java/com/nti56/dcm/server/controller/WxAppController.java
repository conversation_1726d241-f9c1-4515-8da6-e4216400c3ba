package com.nti56.dcm.server.controller;

import cn.binarywang.wx.miniapp.bean.WxMaSubscribeMessage;
import com.nti56.dcm.server.service.WxService;
import com.nti56.nlink.common.dto.TenantIsolation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/wx")
@Tag(name = "微信小程序新接口")
public class WxAppController {


    @Autowired
    private WxService wxService;

    @PostMapping("getSession")
    public void getSession(@RequestHeader("dcm_headers") TenantIsolation tenantIsolation,String jsCode) {
        wxService.getSession(jsCode);

    }

    @PostMapping("sendMsg")
    public void sendMsg(@RequestHeader("dcm_headers") TenantIsolation tenantIsolation,@RequestBody WxMaSubscribeMessage message) {
        wxService.sendMsg(message);

    }
}
