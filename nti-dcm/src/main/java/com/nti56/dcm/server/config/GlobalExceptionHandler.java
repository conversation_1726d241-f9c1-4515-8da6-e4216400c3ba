package com.nti56.dcm.server.config;

import com.nti56.common.util.R;
import com.nti56.dcm.server.exception.BizException;
import com.nti56.nlink.common.util.ServiceCodeEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.ObjectError;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.validation.ConstraintViolation;
import javax.validation.ConstraintViolationException;
import java.util.stream.Collectors;

/**
 * 类说明: 全局异常处理<br/>
 *
 * <AUTHOR> <br/>
 * @version 1.0
 * @date 2021/8/24 11:11<br/>
 * @since JDK 1.8
 */
@Slf4j
@ControllerAdvice(basePackages = "com.nti56.dcm.server.controller")
public class GlobalExceptionHandler {

    @ExceptionHandler(MethodArgumentNotValidException.class)
    @ResponseBody
    public R exceptionHandler(MethodArgumentNotValidException e) {
        return R.error(e.getBindingResult().getAllErrors().stream()
                .map(ObjectError::getDefaultMessage)
                .reduce((o1, o2) -> o1 + "；" + o2)
                .orElse(null));
    }

    @ExceptionHandler(BizException.class)
    @ResponseBody
    public R bizExceptionHandler(BizException e) {
        return R.error(e.getCode(), e.getMessage());
    }

    @ExceptionHandler(ConstraintViolationException.class)
    @ResponseBody
    public R constraintViolationException(ConstraintViolationException exception) {
        String msg = exception.getConstraintViolations().stream().map(ConstraintViolation::getMessage).collect(Collectors.joining("；"));
        return R.error(ServiceCodeEnum.CODE_PARAM_ERROR.getCode(), msg);
    }


    @ExceptionHandler(Exception.class)
    @ResponseBody
    public R exceptionHandler(Exception exception) {
        log.error("system error", exception);
        String msg = exception.getMessage();
        return R.error(ServiceCodeEnum.CODE_PARAM_ERROR.getCode(), msg);
    }

}