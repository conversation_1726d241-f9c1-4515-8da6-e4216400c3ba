package com.nti56.dcm.server.domain.enums;

import lombok.Getter;

public enum NtiApkUrlEnum {
    MONITOR_DETAIL(1, "/dcm-mobile/pagesB/monitor/detail?id=%s&idType=%s", "跳转设备监控详情"),
    EQUIPMENT_DETAIL(2, "/dcm-mobile/pagesA/equipment/detail?id=%s&idType=%s", "跳转设备台账详情"),
    REPAIR_ORDER(3, "/dcm-mobile/pagesD/repair/order?orderNo=%s&isOut=%s&type=waitHandle&idType=%s", "跳转维修工单"),
    MAINTAIN_ORDER(4, "/dcm-mobile/pagesD/maintain/order?orderNo=%s&isOut=%s&type=waitHandle&idType=%s", "跳转保养工单"),
    INSPECT_ORDER(5, "/dcm-mobile/pagesD/spotInspection/order?orderNo=%s&isOut=%s&type=waitHandle&idType=%s", "跳转巡检工单"),
    REPAIR_REPORT(6, "/dcm-mobile/pagesD/faultRepair/order?orderNo=%s&type=waitHandle&idType=%s", "跳转报修工单"),
    EQUIPMENT(7, "/dcm-mobile/pagesTabar/equipment/index?customerId=%s&customerName=%s&idType=%s&maintenanceExpireState=%s&warrantyExpireState=%s&planScrapState=%s", "跳转设备台账"),
    EQUIPMENT_MONITOR(7, "/dcm-mobile/pagesTabar/monitor/index?customerId=%s&customerName=%s&idType=%s", "跳转设备心电图"),
    SPARES_STOCK(9, "/dcm-mobile/pagesG/inventory/index?itemCode=%s", "跳转备件库存"),
    ;

    @Getter
    private Integer value;

    @Getter
    private String name;

    @Getter
    private String nameDesc;

    NtiApkUrlEnum(Integer value, String name, String nameDesc) {
        this.value = value;
        this.name = name;
        this.nameDesc = nameDesc;
    }

    public static NtiApkUrlEnum typeOfValue(Integer value){
        NtiApkUrlEnum[] values = NtiApkUrlEnum.values();
        for (NtiApkUrlEnum v : values) {
            if (v.value.equals(value)) {
                return v;
            }
        }
        return null;
    }

    public static NtiApkUrlEnum typeOfName(String name){
        NtiApkUrlEnum[] values = NtiApkUrlEnum.values();
        for (NtiApkUrlEnum v : values) {
            if (v.name.equals(name)) {
                return v;
            }
        }
        return null;
    }

    public static NtiApkUrlEnum typeOfNameDesc(String nameDesc){
        NtiApkUrlEnum[] values = NtiApkUrlEnum.values();
        for (NtiApkUrlEnum v : values) {
            if (v.nameDesc.equals(nameDesc)) {
                return v;
            }
        }
        return null;
    }
}
