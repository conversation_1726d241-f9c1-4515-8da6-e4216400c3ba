package com.nti56.dcm.server.domain.enums;

import lombok.Getter;

/**
 * 工单操作按钮枚举
 */
public enum OperationEnum {
    DISPATCH(1, "dispatch", "分派"),
    RECEIVE(2, "receive", "接收"),
    EXECUTE(3, "execute", "执行"),
    ACCEPT(4, "accept", "验收"),
    REVOKE(5, "revoke", "撤销"),
    STOP(6, "stop", "结束"),
    FORWARD(7, "forward", "转交"),
    UNDO_DISPATCH(8, "undoDispatch", "撤回派工"),
    REJECT(9, "reject", "驳回"),
    UNDO_EXECUTE(10, "undoExecute", "撤回执行"),
    GENERATE_ORDER(11, "generateOrder", "生成工单"),
    CANCEL(12, "cancel", "关闭"),
    EDIT(13, "edit", "编辑"),
    REMOVE(14, "remove", "删除"),
    ;

    @Getter
    private Integer value;

    @Getter
    private String name;

    @Getter
    private String nameDesc;

    OperationEnum(Integer value, String name, String nameDesc) {
        this.value = value;
        this.name = name;
        this.nameDesc = nameDesc;
    }

    public static OperationEnum typeOfValue(Integer value){
        OperationEnum[] values = OperationEnum.values();
        for (OperationEnum v : values) {
            if (v.value.equals(value)) {
                return v;
            }
        }
        return null;
    }

    public static OperationEnum typeOfName(String name){
        OperationEnum[] values = OperationEnum.values();
        for (OperationEnum v : values) {
            if (v.name.equals(name)) {
                return v;
            }
        }
        return null;
    }

    public static OperationEnum typeOfNameDesc(String nameDesc){
        OperationEnum[] values = OperationEnum.values();
        for (OperationEnum v : values) {
            if (v.nameDesc.equals(nameDesc)) {
                return v;
            }
        }
        return null;
    }
}
