package com.nti56.dcm.server.feign;

import com.nti56.dcm.server.domain.request.ListDcmpEdgeGatewayRequest;
import com.nti56.dcm.server.domain.request.ListDeviceTemplateRequest;
import com.nti56.dcm.server.domain.request.ListDevicesRequest;
import com.nti56.dcm.server.domain.request.ListTagRequest;
import com.nti56.dcm.server.model.dto.DeviceSyncRespondBo;
import com.nti56.dcm.server.model.dto.OtChannelDto;
import com.nti56.dcm.server.model.dto.OtDeviceDto;
import com.nti56.dcm.server.model.dto.OtDeviceTemplateDto;
import com.nti56.dcm.server.model.dto.OtDeviceTemplatePropertyDto;
import com.nti56.dcm.server.model.dto.OtEdgeGatewayDto;
import com.nti56.dcm.server.model.dto.OtTagDto;
import com.nti56.dcm.server.model.result.ITResult;
import feign.Body;
import feign.Headers;
import feign.Param;
import feign.RequestLine;

import javax.validation.constraints.NotNull;
import java.util.List;
import java.util.Map;

public interface OtOpenController {

    @RequestLine("POST /device/template/list")
    @Headers({"Content-Type: application/json;charset=UTF-8", "ot_headers: {otHeaders}", "tenantId: {tenantId}"})
    ITResult<List<OtDeviceTemplateDto>> listTemplate(@Param("otHeaders") String otHeaders,
                                                     @Param("entity") ListDeviceTemplateRequest entity,@Param("tenantId") Long tenantId);


    @RequestLine("POST /device/list-by-name")
    @Headers({"Content-Type: application/json;charset=UTF-8", "ot_headers: {otHeaders}", "tenantId: {tenantId}"})
    ITResult<List<OtDeviceTemplateDto>> listDeviceByName(@Param("otHeaders") String otHeaders,
                                                         @Param("entity") ListDevicesRequest entity,@Param("tenantId") Long tenantId);

    @RequestLine("POST /device/template/valid")
    @Headers({"Content-Type: application/json;charset=UTF-8", "ot_headers: {otHeaders}", "tenantId: {tenantId}"})
    @Body("{body}")
    ITResult<OtChannelDto> validTemplate(@Param("otHeaders") String otHeaders,
                                         @Param("body") String body,@Param("tenantId") Long tenantId);

    @RequestLine("POST /device/template/createByTemplate")
    @Headers({"Content-Type: application/json;charset=UTF-8", "ot_headers: {otHeaders}", "tenantId: {tenantId}"})
    @Body("{body}")
    ITResult<Long> createByTemplate(@Param("otHeaders") String otHeaders,
                                    @Param("body") String body,@Param("tenantId") Long tenantId);


    @RequestLine("POST /device/template/getTemplateThingModel")
    @Headers({"Content-Type: application/json;charset=UTF-8", "ot_headers: {otHeaders}", "tenantId: {tenantId}"})
    @Body("{body}")
    ITResult<List<OtDeviceTemplatePropertyDto>> getTemplateThingModel(@Param("otHeaders") String otHeaders,
                                                                      @Param("body") String body, @Param("tenantId") Long tenantId);

    @RequestLine("POST /device/template/batchCopyTemplate")
    @Headers({"Content-Type: application/json;charset=UTF-8", "ot_headers: {otHeaders}", "tenantId: {tenantId}"})
    @Body("{body}")
    ITResult<Map<Long,Long>> batchCopyTemplate(@Param("otHeaders") String otHeaders,
                                          @Param("body") String body, @Param("tenantId") Long tenantId);


    @RequestLine("PUT /dcmp/edge-gateway/{id}/sync")
    @Headers({"Content-Type: application/json;charset=UTF-8", "ot_headers: {otHeaders}", "tenantId: {tenantId}"})
    ITResult<Void> edgeGatewaySync(@Param("otHeaders") String otHeaders,
                                   @Param("id") Long edgeGatewayId, @Param("tenantId") Long tenantId);

    @RequestLine("PUT /dcmp/device/batch/sync")
    @Headers({"Content-Type: application/json;charset=UTF-8", "ot_headers: {otHeaders}", "tenantId: {tenantId}"})
    @Body("{body}")
    ITResult<List<DeviceSyncRespondBo>> deviceBatchSync(@Param("otHeaders") String otHeaders,
                                                        @Param("body") String body,@Param("tenantId") Long tenantId);


    @RequestLine("POST /dcmp/device/list-basic")
    @Headers({"Content-Type: application/json;charset=UTF-8", "ot_headers: {otHeaders}", "tenantId: {tenantId}"})
    @Body("{body}")
    ITResult<List<OtDeviceDto>> listDevice(@Param("otHeaders") String otHeaders,
                                           @Param("body") String body,@Param("tenantId") Long tenantId);

    @RequestLine("POST /dcmp/edge-gateway/list")
    @Headers({"Content-Type: application/json;charset=UTF-8", "ot_headers: {otHeaders}", "tenantId: {tenantId}"})
    ITResult<List<OtEdgeGatewayDto>> listEdgeGateway(@Param("otHeaders") String otHeaders,
                                                     @Param("entity") ListDcmpEdgeGatewayRequest request,@Param("tenantId") Long tenantId);

    @RequestLine("POST /tags/listAll")
    @Headers({"Content-Type: application/json;charset=UTF-8", "ot_headers: {otHeaders}", "tenantId: {tenantId}"})
    ITResult<List<OtTagDto>> listTag(@Param("otHeaders") String otHeaders, @Param("tenantId") Long tenantId);


}
