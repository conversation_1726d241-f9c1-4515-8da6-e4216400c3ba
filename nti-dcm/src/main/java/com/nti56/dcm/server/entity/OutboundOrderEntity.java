package com.nti56.dcm.server.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.nti56.dcm.server.domain.enums.DeviceMaintenanceOperationTypeEnum;
import com.nti56.dcm.server.domain.enums.WarehouseOrderStatusEnum;
import com.nti56.dcm.server.domain.enums.WarehouseOrderTypeEnum;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 
 * @TableName outbound_order
 */
@TableName(value ="outbound_order")
@Data
@AllArgsConstructor
@NoArgsConstructor
public class OutboundOrderEntity implements Serializable {
    /**
     * 位置ID
     */
    @TableId
    private Long id;
    /**
     * 所属身份 1-供应商 2-客户
     */
    private Integer idType;
    /**
     * 出库单号
     */
    private String serialNumber;
    /**
     * 关联设备ID
     */
    private Long deviceId;
    /**
     * 出库类型
     * @see WarehouseOrderTypeEnum
     */
    private Long outboundType;
    /**
     * 仓库ID
     */
    private Long sparesWarehouseId;
    /**
     * 目标仓库ID（调拨）
     */
    private Long targetWarehouseId;
    /**
     * 状态，0-待执行，1-执行中，2-已完成
     * @see WarehouseOrderStatusEnum
     */
    private Integer status;

    /**
     * 应出数量
     */
    private BigDecimal shouldOutQuantity;
    /**
     * 已出数量
     */
    private BigDecimal alreadyOutQuantity;
    /**
     * 借出数量
     */
    private BigDecimal lendQuantity;


    /**
     * 所属设备工单ID
     */
    private Long belongOrderId;
    /**
     * 所属设备工单编号
     */
    private String belongSerialNumber;
    /**
     * 所属设备工单类型：1-巡检,2-维修,3-保养
     * @see DeviceMaintenanceOperationTypeEnum
     */
    private Integer belongOrderType;

    /**
     * 关联工单ID
     */
    private Long associatedOrderId;
    /**
     * 关联工单编号（领-还，调出-调入，盘）
     */
    private String associatedSerialNumber;


    /**
     * 创建人
     */
    @TableField(fill = FieldFill.INSERT)
    private String creator;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 修改人
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private String updator;

    /**
     * 修改时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    /**
     * 租户ID
     */
    @TableField(fill = FieldFill.INSERT)
    private Long tenantId;

    /**
     * 工程ID
     */
    @TableField(fill = FieldFill.INSERT)
    private Long engineeringId;

    /**
     * 模块ID
     */
    @TableField(fill = FieldFill.INSERT)
    private Long moduleId;

    /**
     * 空间ID
     */
    @TableField(fill = FieldFill.INSERT)
    private Long spaceId;

    /**
     * 版本号
     */
    @Version
    private Integer version;

    /**
     * 删除
     */
    @TableLogic
    private Integer deleted;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;

}