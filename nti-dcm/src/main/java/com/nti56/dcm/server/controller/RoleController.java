package com.nti56.dcm.server.controller;

import com.aliyun.tea.utils.StringUtils;
import com.nti56.common.util.R;
import com.nti56.dcm.server.entity.RoleEntity;
import com.nti56.dcm.server.service.RoleService;
import com.nti56.nlink.common.dto.TenantIsolation;
import com.nti56.nlink.common.util.BeanUtilsIntensifier;
import com.nti56.nlink.common.util.Result;
import com.nti56.nlink.common.util.ServiceCodeEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;


/**
 * 角色表 控制器
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2024-02-26 16:54:02
 * @since JDK 1.8
 */
@RestController
@RequestMapping("/role")
@Slf4j
public class RoleController {

    @Autowired
    private RoleService service;

    /**
     * 创建角色
     */
    @PostMapping("/create")
    public R<RoleEntity> create(@RequestHeader("dcm_headers") TenantIsolation tenant, @RequestBody RoleEntity entity){
        Integer idType = tenant.getIdType();
        entity.setIdType(idType);
        String roleName = entity.getRoleName();
        if(roleName == null || StringUtils.isEmpty(roleName)){
            return R.error("请输出角色名");
        }
        if(roleName.length() > 128){
            return R.error("角色名太长");
        }
        Result<RoleEntity> result = service.save(tenant.getTenantId(), tenant.getIdType(), entity);
        return R.result(result);
    }


    /**
     * 获取列表
     */
    @GetMapping("/list")
    public R<List<RoleEntity>> list(@RequestHeader("dcm_headers") TenantIsolation tenant){
        Integer idType = tenant.getIdType();
        Result<List<RoleEntity>> result = service.list(tenant.getTenantId(), idType);
        return R.result(result);
    }

    /**
     * 更新角色
     */
    @PutMapping("/update")
    public R<Void> update(@RequestHeader("dcm_headers") TenantIsolation tenant, @RequestBody RoleEntity entity){
        if (BeanUtilsIntensifier.checkBeanAndProperties(entity, RoleEntity::getId)) {
            return R.error(ServiceCodeEnum.CODE_PARAM_ERROR.getCode());
        }
        Result<Void> result = service.update(tenant.getTenantId(), tenant.getIdType(), entity);
        return R.result(result);
    }

    /**
     * 删除角色
     * @param entityId 对象id
     */
    @DeleteMapping("/{entityId}")
    public R<Void> delete(@RequestHeader("dcm_headers") TenantIsolation tenant, @PathVariable Long entityId){
        Result<Void> result = service.deleteById(tenant.getTenantId(), entityId);
        return R.result(result);
    }

    /**
     * 获取角色
     * @param entityId 对象id
     */
    @GetMapping("/{entityId}")
    public R<RoleEntity> get(@RequestHeader("dcm_headers") TenantIsolation tenant, @PathVariable Long entityId){
        Result<RoleEntity> result = service.getById(tenant.getTenantId(), entityId);
        return R.result(result);
    }
    
    // /**
    //  * 获取分页
    //  * @param pageParam 分页参数
    //  */
    // @GetMapping("/page")
    // public R<Page<RoleEntity>> page(@RequestHeader("dcm_headers") TenantIsolation tenant, PageParam pageParam,RoleEntity entity){
    //     Page<RoleEntity> page = pageParam.toPage(RoleEntity.class);
    //     Result<Page<RoleEntity>> result = service.getPage(tenant.getTenantId(), entity,page);
    //     return R.result(result);
    // }
    
}
