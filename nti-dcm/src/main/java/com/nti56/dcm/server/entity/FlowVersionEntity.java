package com.nti56.dcm.server.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.handlers.FastjsonTypeHandler;
import com.nti56.dcm.server.domain.enums.AcceptanceModeEnum;
import com.nti56.dcm.server.model.dto.ResponsibleDto;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <p>
 * 工单流程配置表 实体类
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @Ddate 2024-06-17 09:27:30
 * @since JDK 1.8
 */
@Data
@TableName(value = "flow_node_version",autoResultMap = true)
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class FlowVersionEntity {

        /**
         *
         */
        private Long id;

        /**
         * 流程配置
         */
        private String processConfig;

        /**
         * 工作流ID
         */
        private String flowId;

        /**
         * nodeId
         */
        private String nodeId;








}
