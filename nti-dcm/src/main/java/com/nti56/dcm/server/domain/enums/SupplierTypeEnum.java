package com.nti56.dcm.server.domain.enums;

import lombok.Getter;

public enum SupplierTypeEnum {
    TENANT(1, "TENANT", "租户"),
    SELF_DEFINING(2, "SELF_DEFINING", "自定义"),
    ;

    @Getter
    private Integer value;

    @Getter
    private String name;

    @Getter
    private String nameDesc;

    SupplierTypeEnum(Integer value, String name, String nameDesc) {
        this.value = value;
        this.name = name;
        this.nameDesc = nameDesc;
    }

    public static SupplierTypeEnum typeOfValue(Integer value){
        SupplierTypeEnum[] values = SupplierTypeEnum.values();
        for (SupplierTypeEnum v : values) {
            if (v.value.equals(value)) {
                return v;
            }
        }
        return null;
    }

    public static SupplierTypeEnum typeOfName(String name){
        SupplierTypeEnum[] values = SupplierTypeEnum.values();
        for (SupplierTypeEnum v : values) {
            if (v.name.equals(name)) {
                return v;
            }
        }
        return null;
    }

    public static SupplierTypeEnum typeOfNameDesc(String nameDesc){
        SupplierTypeEnum[] values = SupplierTypeEnum.values();
        for (SupplierTypeEnum v : values) {
            if (v.nameDesc.equals(nameDesc)) {
                return v;
            }
        }
        return null;
    }
}
