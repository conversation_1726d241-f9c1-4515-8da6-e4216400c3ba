package com.nti56.dcm.server.domain.enums;

import lombok.Getter;

public enum PlanTypeEnum {
    NO_PLAN(1, "noPlan", "无计划"),
    PLAN_CONNECTING(2, "planConnecting", "计划接入中"),
    FINISH_PLAN(3, "finishPlan", "接入完成"),
    ;

    @Getter
    private Integer value;

    @Getter
    private String name;

    @Getter
    private String nameDesc;

    PlanTypeEnum(Integer value, String name, String nameDesc) {
        this.value = value;
        this.name = name;
        this.nameDesc = nameDesc;
    }

    public static PlanTypeEnum typeOfValue(Integer value){
        PlanTypeEnum[] values = PlanTypeEnum.values();
        for (PlanTypeEnum v : values) {
            if (v.value.equals(value)) {
                return v;
            }
        }
        return null;
    }

    public static PlanTypeEnum typeOfName(String name){
        PlanTypeEnum[] values = PlanTypeEnum.values();
        for (PlanTypeEnum v : values) {
            if (v.name.equals(name)) {
                return v;
            }
        }
        return null;
    }

    public static PlanTypeEnum typeOfNameDesc(String nameDesc){
        PlanTypeEnum[] values = PlanTypeEnum.values();
        for (PlanTypeEnum v : values) {
            if (v.nameDesc.equals(nameDesc)) {
                return v;
            }
        }
        return null;
    }
}
