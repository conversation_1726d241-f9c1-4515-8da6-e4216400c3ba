package com.nti56.dcm.server.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDateTime;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;

/**
 * <p>
 * 故障库表 实体类
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @Ddate 2025-06-04 14:42:27
 * @since JDK 1.8
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@TableName("fault_library")
public class FaultLibraryEntity implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
    * id
    */
    private Long id;

    /**
    * 租户id
    */
    @TableField(fill = FieldFill.INSERT)
    private Long tenantId;

    /**
    * 身份，1-供应商，2-客户
    */
    private Integer idType;

    /**
    * 设备类型id
    */
    @NotNull(message = "设备类型ID不能为空")
    private Long deviceTypeId;

    /**
    * 设备类型名称
    */
    private String deviceTypeName;

    /**
    * 故障代码
    */
    private String faultCode;

    /**
     * 故障事件名称
     */
    private String faultEvent;

    /**
    * 故障描述
    */
    @NotBlank(message = "故障描述不能为空")
    private String faultDesc;

    /**
    * 解决方法
    */
    @Size(max = 1024, message = "解决方法长度不能超过1024个字符")
    private String solveMethod;

    /**
    * 预防策略
    */
    @Size(max = 1024, message = "预防策略长度不能超过1024个字符")
    private String preventStrategy;

    /**
    * 创建人
    */
    @TableField(fill = FieldFill.INSERT)
    private String creator;

    /**
    * 创建时间
    */
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
    * 修改人
    */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private String updator;

    /**
    * 修改时间
    */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    /**
    * 工程ID
    */
    @TableField(fill = FieldFill.INSERT)
    private Long engineeringId;

    /**
    * 模块ID
    */
    @TableField(fill = FieldFill.INSERT)
    private Long moduleId;

    /**
    * 空间ID
    */
    @TableField(fill = FieldFill.INSERT)
    private Long spaceId;

    /**
    * 版本号
    */
    @Version
    private Integer version;

    /**
    * 删除
    */
    @TableLogic
    private Integer deleted;



}
