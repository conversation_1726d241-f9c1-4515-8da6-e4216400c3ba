package com.nti56.dcm.server.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * <p>
 * 项目表 实体类
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @Ddate 2024-12-09 09:53:42
 * @since JDK 1.8
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@TableName("pmo_project")
public class PmoProjectEntity implements Serializable {
    private static final long serialVersionUID = 1L;
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
    * 上级id
    */
    private Long pid;

    /**
    * 租户id
    */
    @TableField(fill = FieldFill.INSERT)
    private Long clientId;

    /**
    * 项目编码
    */
    private String projectCode;

    /**
    * 项目名称
    */
    private String projectName;

    /**
    * 客户id
    */
    private Long customerId;

    /**
    * 客户名称
    */
    private String customerName;

    /**
    * 终端客户id
    */
    private Long terminalCustomerId;

    /**
    * 终端客户名称
    */
    private String terminalCustomerName;

    /**
    * 立项年份
    */
    private Integer approvalYear;

    /**
    * 立项日期
    */
    private LocalDate approvalDate;

    /**
    * 项目简称
    */
    private String projectSimpleName;

    /**
    * 项目类别;数据字典：project_type(01:纯软项目,02:设备项目,03:集成项目,04:咨询项目,05:研发项目,99:其他项目)
    */
    private String projectType;

    /**
    * 项目阶段;字典project_phase（1-实施阶段；2-销售阶段；3-质保阶段;4-售后阶段；5-项目关闭;6-质保到期;7-研发中; ）
    */
    private String projectPhase;

    /**
    * 所属部门-实施部门
    */
    private String section;

    /**
    * 实施部门名称
    */
    private String sectionName;

    /**
    * 软件实施部门
    */
    private String softwareSection;

    /**
    * 软件实施部门名称
    */
    private String softwareSectionName;

    /**
    * 销售部门
    */
    private String saleSection;

    /**
    * 销售部门名称
    */
    private String saleSectionName;

    /**
    * 售后部门
    */
    private String afterSalesSection;

    /**
    * 售后部门名称
    */
    private String afterSalesSectionName;

    /**
    * 数据归属部门
    */
    private String dataSection;

    /**
    * 数据归属部门
    */
    private String dataSectionName;

    /**
    * 金蝶项目编码
    */
    private String easProjectCode;

    /**
    * 金蝶项目名称
    */
    private String easProjectName;

    /**
    * 销售负责人
    */
    private String principal;

    /**
    * 销售负责人id
    */
    private Long principalId;

    /**
    * 实施负责人
    */
    private String implementationManager;

    /**
    * 售后负责人ID
    */
    private String afterSalesPersonIds;

    /**
    * 售后负责人名称
    */
    private String afterSalesPersonNames;

    /**
    * 项目经理
    */
    private String projectManagers;

    /**
    * 项目经理ids
    */
    private String projectManagersIds;

    /**
    * 项目预算
    */
    private String fldmondey;

    /**
    * 采购预算
    */
    private String purchaseBudget;

    /**
    * 钉钉流程实例id
    */
    private String dingInstanceId;

    /**
    * 项目分组
    */
    private String projectGroup;

    /**
    * 类型;1:企业 2:私有
    */
    private Integer type;

    /**
    * 项目地点
    */
    private String projectLocation;

    /**
    * 状态;1:正常  2:延期  3:异常  4:完成
    */
    private Integer status;

    /**
    * 项目归属主体
    */
    private String projectOwnership;

    /**
    * 项目归属主体编码
    */
    private String projectOwnershipCode;

    /**
    * 项目区分 0内部 1外部
    */
    private String projectSift;

    /**
    * 项目预计总工时(人天)
    */
    private String planHours;

    /**
    * 项目实际总工时(人天)
    */
    private String realHours;

    /**
    * 项目概述
    */
    private String description;

    /**
    * 数据来源关联枚举data_source;   0:pc  1:外部接口  2:移动端
    */
    private String dataSource;

    /**
    * 排序
    */
    private Integer sort;

    /**
    * 是否机密：1-是；0-否
    */
    private Integer isConfidential;

    /**
    * 项目预期金额（元）
    */
    private BigDecimal preAmount;

    /**
    * 预估项目获取方式：1-公开招标；2-第一来源谈判；3-竞争性谈判；4-客户管理平台直接投标；5-线下直接签约；6-其他
    */
    private String preAcquisitionMethod;

    /**
    * 其他获取方式名称
    */
    private String preOtherAcquisitionMethodName;

    /**
    * 预计启动时间
    */
    private LocalDate expectedStartTime;

    /**
    * 工程启动时间
    */
    private LocalDate startTime;

    /**
    * 项目等级：A，B，C，D
    */
    private String projectLevel;

    /**
    * 质保开始时间
    */
    private LocalDate warrantyStartDate;

    /**
    * 质保到期时间
    */
    private LocalDate warrantyExpirationDate;

    /**
    * 质保时长（年）
    */
    private String warrantyDuration;

    /**
    * 移交售后时间
    */
    private LocalDate handoverAfterSalesDate;

    /**
    * 销售跟进情况    1跟进中、2合作达成（中标、谈判成交）、3合作未达成（未中标、谈判失败）、4放弃跟进
    */
    private String followUpStatus;

    /**
    * 甲方名称
    */
    private String firstParty;

    /**
    * 乙方名称
    */
    private String secondParty;

    /**
    * 项目范围-原交付内容
    */
    private String projectScope;

    /**
    * 项目关闭时间 (新增字段)
    */
    private LocalDateTime closeTime;

    /**
    * 删除状态; 0:未删除，1：已删除
    */
    @TableLogic
    private Integer deleted;

    /**
    * 系统入库时间
    */
    private LocalDateTime inTime;

    /**
    * 创建人
    */
    @TableField(fill = FieldFill.INSERT)
    private String createBy;

    /**
    * 创建人姓名
    */
    private String createByName;

    /**
    * 创建时间
    */
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
    * 修改人
    */
    private String modifyBy;

    /**
    * 创建人姓名
    */
    private String modifyByName;

    /**
    * 更新时间
    */
    private LocalDateTime modifyTime;

    /**
    * 版本号
    */
    @Version
    private Long version;



}
