package com.nti56.dcm.server.controller;


import com.nti56.dcm.server.domain.enums.OrderTypeEnum;
import com.nti56.dcm.server.model.dto.danswer.ChatMessageFeedbackDTO;
import com.nti56.dcm.server.model.dto.danswer.ChatMessageSendDTO;
import com.nti56.dcm.server.service.IDanswerUserCharSessionService;
import com.nti56.nlink.common.util.R;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.servlet.mvc.method.annotation.ResponseBodyEmitter;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;
import org.springframework.web.servlet.mvc.method.annotation.StreamingResponseBody;

import javax.servlet.http.HttpServletResponse;

/**
 * <p>
 * danswer用户会话表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-09-06
 */
@RestController
@RequestMapping("/danswer")
@Tag(name = "danswer交互")
public class DanswerController {
    @Autowired
    private IDanswerUserCharSessionService danswerUserCharSessionService;

    @GetMapping("list")
    @Operation(summary = "获取用户会话列表")
    public R list() {
        return R.result(danswerUserCharSessionService.listUserChat());
    }


    @GetMapping("getChatSession")
    @Operation(summary = "获取会话信息")
    public R getChatSession(Integer id) {
        return R.result(danswerUserCharSessionService.getChatSession(id));
    }

    @PostMapping("sendMessage")
    @Operation(summary = "发送消息")
    public StreamingResponseBody sendMessage(@RequestBody @Validated ChatMessageSendDTO sendDTO, HttpServletResponse response) {
        return danswerUserCharSessionService.sendMessage(sendDTO,response);
    }


    @PostMapping("sendMessageNew")
    @Operation(summary = "发送消息")
    public SseEmitter sendMessageNew(@RequestBody @Validated ChatMessageSendDTO sendDTO, HttpServletResponse response) {
        return danswerUserCharSessionService.sendMessageNew(sendDTO,response);
    }

    @GetMapping("sendFileToDanswer")
    @Operation(summary = "sendFileToDanswer")
    public void sendMessage(String processMessage, String fileName) {
        danswerUserCharSessionService.sendFileToDanswer(processMessage,fileName,OrderTypeEnum.REPAIR);
    }

    @PostMapping("create")
    @Operation(summary = "创建新的会话")
    public R create() {
        return R.result(danswerUserCharSessionService.creatNewChat());
    }

    @DeleteMapping("delete/{chatId}")
    @Operation(summary = "删除会话")
    public R delete(@PathVariable Integer chatId) {
        return R.result(danswerUserCharSessionService.deleteChat(chatId));
    }
    @PostMapping("feedback")
    @Operation(summary = "消息有效性反馈")
    public R feedback(@RequestBody @Validated ChatMessageFeedbackDTO feedbackDTO) {
        return R.result(danswerUserCharSessionService.feedbackMessage(feedbackDTO));
    }
}
