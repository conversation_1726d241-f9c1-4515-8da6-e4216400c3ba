package com.nti56.dcm.server.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Date;

import com.baomidou.mybatisplus.annotation.Version;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 * 
 * @TableName device
 */
@TableName(value ="device")
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class DeviceEntity implements Serializable {
    /**
     * 设备ID
     */
    @TableId
    private Long id;

    /**
     * 设备编号
     */
    private String serialNumber;

    /**
     * 设备名称
     */
    private String deviceName;

    /**
     * 规格
     */
    private String spec;

    /**
     * 设备类型
     */
    private Long deviceTypeId;
    /**
     * 使用设备模板id
     */
    private String useTemplateId;

    /**
     * 设备存放位置
     */
    private Long deviceLocationId;

    /**
     * 内部 设备存放位置
     */
    private Long innerLocationId;

    /**
     * 设备所属部门
     */
    private String deviceDept;

    /**
     * 当前状态
     */
    private Integer status;

    /**
     * 品牌
     */
    private String brand;

    /**
     * 负责人
     */
    private String leader;

    /**
     * 联系电话
     */
    private String mobile;

    /**
     * 启用日期
     */
    private Date enableDatetime;


    /**
     * 扩展信息
     */
    private String extendInfo;

    /**
     * 继承于设备类型的扩展信息
     */
    private String inheritExtendInfo;
    /**
     * 合同编号
     */
    private String contractCode;

    /**
     * 连接ot
     */
    private Integer connectOt;

    /**
     * 是否开启同步
     */
    private Integer sync;

    /**
     * 创建人
     */
    @TableField(fill = FieldFill.INSERT)
    private String creator;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 修改人
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private String updator;

    /**
     * 修改时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    /**
     * 租户ID
     */
    @TableField(fill = FieldFill.INSERT)
    private Long tenantId;

    /**
     * 工程ID
     */
    @TableField(fill = FieldFill.INSERT)
    private Long engineeringId;

    /**
     * 模块ID
     */
    @TableField(fill = FieldFill.INSERT)
    private Long moduleId;

    /**
     * 空间ID
     */
    @TableField(fill = FieldFill.INSERT)
    private Long spaceId;

    /**
     * 版本号
     */
    @Version
    private Integer version;

    /**
     * 删除
     */
    @TableLogic
    private Integer deleted;

    /**
     * 设备别名
     */
    private String deviceAliasName;

    /**
     * 供应商
     */
    private String producer;

    /**
     * 供应商Id
     */
    private Long supplierId;


    /**
     * 安装时间
     */
    private Date installationTime;

    /**
     * 验收时间
     */
    private Date acceptanceTime;

    /**
     * 设备采购时间
     */
    private Date buyTime;

    /**
     * 质保过期时间
     */
    private Date warrantyExpireTime;

    /**
     * 维保过期时间
     */
    private Date maintenanceExpireTime;

    /**
     * 预计报废时间
     */
    private Date planScrapTime;


    /**
     * 产品序列号
     */
    private String productSerialNumber;

    /**
     * 备注
     */
    private String remark;

    /**
     * 是否pmo台账  0否 1是
     */
    private Integer isPmo;
    /**
     * 链接OT时间
     */
    private Date connectTime;

    /**
     * 项目ID
     */
    private Long projectId;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;


}