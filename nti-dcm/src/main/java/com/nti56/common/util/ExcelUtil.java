package com.nti56.common.util;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONArray;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.support.ExcelTypeEnum;
import com.alibaba.excel.write.builder.ExcelWriterBuilder;
import com.alibaba.excel.write.metadata.style.WriteCellStyle;
import com.alibaba.excel.write.metadata.style.WriteFont;
import com.alibaba.excel.write.style.HorizontalCellStyleStrategy;
import com.csvreader.CsvReader;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.openxml4j.exceptions.InvalidFormatException;
import org.apache.poi.poifs.filesystem.POIFSFileSystem;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.DateUtil;
import org.apache.poi.ss.usermodel.IndexedColors;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.usermodel.XSSFRow;
import org.apache.poi.xssf.usermodel.XSSFSheet;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.util.StringUtils;

import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.nio.charset.Charset;
import java.text.DecimalFormat;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Slf4j
public class ExcelUtil {

    public static void exportByEasyExcel(Boolean isCsv, HttpServletResponse response, String fileName, List<List<String>> head, List dataList, List<String> hideColumns) {
        exportByEasyExcel(isCsv, response, fileName, head, dataList, hideColumns, new ArrayList<>());
    }

    public static void exportByEasyExcel(Boolean isCsv, HttpServletResponse response, String fileName, List<List<String>> head, List dataList, List<String> hideColumns, List<String> excludeColumns) {
        WriteCellStyle headWriteCellStyle = new WriteCellStyle();
        // 背景设置为红色
//        headWriteCellStyle.setFillForegroundColor((short) 60);
//        headWriteCellStyle.setFillPatternType(FillPatternType.SOLID_FOREGROUND);
        WriteFont headWriteFont = new WriteFont();
        headWriteFont.setFontName("Calibri");
        headWriteFont.setColor(IndexedColors.WHITE.getIndex());
        headWriteFont.setBold(true);//.setBoldweight(Font.BOLDWEIGHT_BOLD);
        headWriteFont.setFontHeightInPoints((short) 10);
        headWriteCellStyle.setWriteFont(headWriteFont);
        HorizontalCellStyleStrategy horizontalCellStyleStrategy =
                new HorizontalCellStyleStrategy(headWriteCellStyle, new ArrayList<>());
        ExcelWriterBuilder excelWriter = null;
        ExcelWriter build = null;
        try {
            response.setHeader("Access-Control-Allow-Origin", "*");
            response.setHeader("Access-Control-Expose-Headers", "Content-Disposition");
            response.setContentType("application/octet-stream; charset=utf-8");
            if (isCsv) {
                response.setContentType("text/csv;charset=utf-8");
            } else {
                response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
            }
            response.setCharacterEncoding("utf-8");
            fileName = URLEncoder.encode(fileName, "UTF-8").replaceAll("\\+", "%20");
            response.setHeader("Content-Disposition", "attachment; filename=" + fileName);
            excelWriter = EasyExcel.write(response.getOutputStream());
            if (isCsv) {
                excelWriter.excelType(ExcelTypeEnum.CSV).charset(Charset.forName("GBK"));
            }
            excelWriter
                    .sheet("Sheet1")
                    .head(head)
                    .registerConverter(new LocalDateTimeConverter())
                    .excludeColumnFieldNames(excludeColumns)
                    .registerWriteHandler(new ExcelCellWriteHandler(hideColumns))
                    .registerWriteHandler(horizontalCellStyleStrategy)
                    .doWrite(dataList);
            build = excelWriter.build();
        } catch (UnsupportedEncodingException e) {
            e.printStackTrace();
        } catch (IOException e) {
            e.printStackTrace();
        } finally {
            if (build != null) {
                build.finish();
            }
        }
    }

    public static Map<String, List<Object>> readXLSX(String fileName, File file, Integer readRowStart, Integer headIndex, String replaceHeadChar, Map<String, String> headNameMap, boolean isTemplate) throws InvalidFormatException, IOException {
        InputStream input = null;
        try {
            input = new FileInputStream(file);
            Workbook book;
            if (fileName.toLowerCase().endsWith(".xls")) {
                book = new HSSFWorkbook(new POIFSFileSystem(input));
            } else if (fileName.toLowerCase().endsWith(".xlsx")) {
                book = new XSSFWorkbook(input);
            } else {
                // csv 转xssf
                InputStream inputStream = csvStream2xlsxStream(input, fileName);
                book = new XSSFWorkbook(inputStream);
            }
            return putSheetToMap(book, readRowStart, headIndex, replaceHeadChar, headNameMap, isTemplate);
        } finally {
            if (null != input) {
                input.close();
            }
        }
    }

    /**
     * @param inputStream 输入流
     */
    public static InputStream csvStream2xlsxStream(InputStream inputStream, String fileName) {
        FileOutputStream fileOutputStream = null;
        try {
            fileName = fileName.replace(".csv", ".xlsx");
            XSSFWorkbook workBook = new XSSFWorkbook();
            XSSFSheet sheet = workBook.createSheet("Sheet1");
            int rowNum = -1;
            CsvReader csvReader = new CsvReader(inputStream, Charset.forName("GBK"));
            while (csvReader.readRecord()) {
                rowNum++;
                XSSFRow currentRow = sheet.createRow(rowNum);
                for (int i = 0; i < csvReader.getColumnCount(); i++) {
                    currentRow.createCell(i).setCellValue(csvReader.get(i));
                }
            }
            File file = new File("/" + fileName);
            fileOutputStream = new FileOutputStream(file);
            workBook.write(fileOutputStream);
            InputStream input = new FileInputStream(file);
            file.delete();
            return input;
        } catch (Exception e) {
            log.error("CsvToXlsxUtil exception :", e);
        } finally {
            try {
                if (ObjectUtil.isNotNull(fileOutputStream)) {
                    assert fileOutputStream != null;
                    fileOutputStream.close();
                }
            } catch (IOException e) {
                log.error("CsvToXlsxUtil close FileOutputStream exception :", e);
            }
        }
        return null;
    }

    private static Map<String, List<Object>> putSheetToMap(Workbook book, Integer readRowStart, Integer headIndex, String replaceHeadChar, Map<String, String> headNameMap, boolean isTemplate) throws IOException {
        Map<String, List<Object>> map = new HashMap<>();
        List<Sheet> sheets = getSheets(book);
        for (Sheet sheet : sheets) {
            map.put(sheet.getSheetName(), read(sheet, book, readRowStart, headIndex, replaceHeadChar, headNameMap, isTemplate));
        }
        return map;
    }

    private static List<Sheet> getSheets(Workbook book) {
        int numberOfSheets = book.getNumberOfSheets();
        List<Sheet> sheets = new ArrayList<>();
        for (int i = 0; i < numberOfSheets; i++) {
            sheets.add(book.getSheetAt(i));
        }
        return sheets;
    }

    public static List<Object> read(Sheet sheet, Workbook book, Integer readRowStart, Integer headIndex, String replaceHeadChar, Map<String, String> headNameMap, boolean isTemplate) throws IOException {
        List<Object> readResult = new ArrayList<>();

        // 首行下标、尾行下标
        int rowStart = sheet.getFirstRowNum();
        if (readRowStart != null) {
            rowStart = readRowStart - 1;
        }
        int rowEnd = sheet.getLastRowNum();
        if (rowEnd < rowStart) {
            rowStart = rowEnd;
        }
        // 如果首行与尾行相同，表明只有一行，直接返回空数组
        if (!isTemplate && rowStart == rowEnd) {
            return new JSONArray();
        }

        // 获取第一行JSON对象键
        Row firstRow = sheet.getRow(null == headIndex ? rowStart : headIndex);
        int cellStart = firstRow.getFirstCellNum();
        int cellEnd = firstRow.getLastCellNum();

        // 存放表头字段
        Map<Integer, String> keyMap = new HashMap<>();
        for (int j = cellStart; j < cellEnd; j++) {
            //正常取第一行表头
            if (null == replaceHeadChar || StringUtils.isEmpty(replaceHeadChar)) {
                keyMap.put(j, getValue(firstRow.getCell(j)));
            } else {
                keyMap.put(j, getValue(firstRow.getCell(j)).replace(replaceHeadChar, ""));
            }
        }

        // 获取表头信息字符串（用来与sheetName做匹配，判断使用的是否是标准模板）
        String tableHeadStr = keyMap.values().toString().trim();
        readResult.add(tableHeadStr);

        // 获取每行JSON对象的值
        List<Map<String, Object>> list = new ArrayList<>();
        //JSONArray array = new JSONArray();
        // 默认：此处的起始读取行数是从第二行开始，表头是第一行
        if (null == readRowStart) {
            readRowStart = rowStart + 1;
        }
        for (int i = readRowStart; i <= rowEnd; i++) {
            Row eachRow = sheet.getRow(i);
            Map<String, Object> obj = new HashMap<>();
            //JSONObject obj = new JSONObject();
            StringBuffer sb = new StringBuffer();
            for (int k = cellStart; k < cellEnd; k++) {
                if (eachRow != null) {
                    String val = getValue(eachRow.getCell(k));
                    sb.append(val.trim()); // 所有数据添加到里面，用于判断该行是否为空
                    if (null != headNameMap && headNameMap.containsKey(keyMap.get(k))) {
                        obj.put(headNameMap.get(keyMap.get(k)), val);
                    } else {
                        obj.put(keyMap.get(k), val);
                    }
                }
            }
            if (sb.toString().length() > 0) {
                list.add(obj);
            } else {
                list.add(null);
            }
        }
        //book.close();
        readResult.add(list);
        return readResult;
    }

    public static String getValue(Cell cell) {

        // 空白或空
        if (cell == null) {
            return "";
        }

        switch (cell.getCellType()) {
            case BOOLEAN:
                return String.valueOf(cell.getBooleanCellValue());
            case NUMERIC:
                // excel中的日期一样是数据格式的
                if (DateUtil.isCellDateFormatted(cell)) {
                    SimpleDateFormat sdf = new SimpleDateFormat("yyyy/MM/dd");
                    Date date = DateUtil.getJavaDate(cell.getNumericCellValue());
                    return sdf.format(date);
                }
                // 返回数值类型的值
                Object inputValue;// 单元格值
                Long longVal = Math.round(cell.getNumericCellValue());
                Double doubleVal = cell.getNumericCellValue();
                if (Double.parseDouble(longVal + ".0") == doubleVal) {   //判断是否含有小数位.0
                    inputValue = longVal;
                } else {
                    inputValue = doubleVal;
                }
                DecimalFormat df = new DecimalFormat("#.################");    //格式化为16位小数，按自己需求选择；
                return df.format(inputValue);
            case FORMULA:
                //判断cell是否为日期格式
                try {
                    if (DateUtil.isCellDateFormatted(cell)) {
                        //转换为日期格式YYYY-mm-dd
                        return cell.getDateCellValue().toString();
                    } else {
                        //数字
                        return String.valueOf(cell.getNumericCellValue());
                    }
                } catch (Exception e) {
                    log.error("ExcelUtil-getValue,{}", Arrays.toString(e.getStackTrace()));
                    //公式字符串型
                    return String.valueOf(cell.getRichStringCellValue());
                }
            case BLANK:
                return "";
            case ERROR:
                return String.valueOf(cell.getErrorCellValue());
            default:
                return String.valueOf(cell.getStringCellValue()).trim();
        }

    }


}
