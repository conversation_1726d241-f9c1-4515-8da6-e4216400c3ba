package com.nti56.dcm.server.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.nti56.common.util.R;
import com.nti56.dcm.server.domain.enums.ReportTypeEnum;
import com.nti56.dcm.server.entity.ReportRecordEntity;
import com.nti56.dcm.server.model.dto.ReportRecordDto;
import com.nti56.dcm.server.model.vo.ReportRecordDetailVo;
import com.nti56.dcm.server.model.vo.ReportRecordVo;
import com.nti56.dcm.server.service.IReportRecordService;
import com.nti56.nlink.common.dto.TenantIsolation;
import com.nti56.nlink.common.util.PageParam;
import com.nti56.nlink.common.util.Result;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.time.temporal.TemporalAdjusters;
import java.util.List;

/**
 * <p>
 * 报表记录表 控制器
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @Ddate 2025-04-07 16:06:08
 * @since JDK 1.8
 */
@RestController
@RequestMapping("reportRecord")
@Tag(name = "报表记录表模块")
public class ReportRecordController {

    @Autowired
    private IReportRecordService reportRecordService;

    private static final DateTimeFormatter yyyyMMdd = DateTimeFormatter.ofPattern("yyyy-MM-dd");

    private static final DateTimeFormatter yyyyMM = DateTimeFormatter.ofPattern("yyyy-MM");

    @GetMapping("page")
    @Operation(summary = "获取分页")
    public R page(@RequestHeader("dcm_headers") TenantIsolation tenantIsolation,
                   PageParam pageParam, ReportRecordDto dto) {
        Page<ReportRecordVo> page = pageParam.toPage(ReportRecordVo.class);
        Result<Page<ReportRecordVo>> result = reportRecordService.getPage(tenantIsolation, dto, page);
        return R.result(result);
    }

    @GetMapping("/{id}")
    @Operation(summary = "获取对象")
    public R getById(@RequestHeader("dcm_headers") TenantIsolation tenantIsolation, @PathVariable("id") Long id) {
        Result<ReportRecordDetailVo> result = reportRecordService.getById(id);
        return R.result(result);
    }

    @PostMapping("getCustomerList")
    @Operation(summary = "获取客户列表")
    public R getCustomerList(@RequestHeader("dcm_headers") TenantIsolation tenantIsolation,
                             @RequestBody List<Long> customerIds) {
        return R.result(Result.ok(reportRecordService.getCustomerList(tenantIsolation.getTenantId(), customerIds)));
    }

    @GetMapping("getNewCustomerCountByDate")
    public R getNewCustomerCountByDate(@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8") @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") LocalDateTime start,
                                       @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8") @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") LocalDateTime end,
                                       Integer reportType,
                                       String belongDate) {
        if (ReportTypeEnum.DAY.getValue().equals(reportType)) {
//            // 获取前一天的日期
//            LocalDate yesterday = LocalDate.now().minusDays(1);
//
//            // 获取前一天的开始时间和结束时间
//            start = yesterday.atStartOfDay();
//            // 获取前一天的结束时间
//            end = yesterday.atTime(LocalTime.of(23, 59, 59));

            belongDate = yyyyMMdd.format(start);
        } else if (ReportTypeEnum.WEEK.getValue().equals(reportType)) {
//            // 获取当前日期
//            LocalDate now = LocalDate.now();
//
//            // 获取上一周的第一天（周一）和最后一天（周日）
//            LocalDate weekStart = now.minusWeeks(1).with(TemporalAdjusters.previousOrSame(java.time.DayOfWeek.MONDAY));
//            LocalDate weekEnd = weekStart.plusDays(6);
//
//            // 获取上一周的开始时间和结束时间
//            start = weekStart.atStartOfDay();
//            end = weekEnd.atTime(LocalTime.of(23, 59, 59));

            belongDate = yyyyMMdd.format(start) + "至" + yyyyMMdd.format(end);
        } else if (ReportTypeEnum.MONTH.getValue().equals(reportType)) {
//            LocalDate now = LocalDate.now();
//
//            // 获取上一个月的第一天和最后一天
//            LocalDate monthStart = now.minusMonths(1).withDayOfMonth(1);
//            LocalDate monthEnd = monthStart.with(TemporalAdjusters.lastDayOfMonth());
//
//            // 获取上一个月的开始时间和结束时间
//            start = monthStart.atStartOfDay();
//            end = monthEnd.atTime(LocalTime.of(23, 59, 59));

            belongDate = yyyyMM.format(start);
        }
        //return R.result(Result.ok(customerRelationService.getNewCustomerCountByDate(start, end)));
        return R.result(Result.ok(reportRecordService.reportStatistics(reportType, belongDate, start, end)));
    }

    @GetMapping("useDcmReport")
    public void useDcmReport(@JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8") @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate start,
                                       @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8") @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate end,
                                       Integer reportType,
                                       String belongDate) {
        if (ReportTypeEnum.DAY.getValue().equals(reportType)) {
            belongDate = yyyyMMdd.format(start);
        } else if (ReportTypeEnum.WEEK.getValue().equals(reportType)) {
            belongDate = yyyyMMdd.format(start) + "至" + yyyyMMdd.format(end);
        } else if (ReportTypeEnum.MONTH.getValue().equals(reportType)) {
            belongDate = yyyyMM.format(start);
        }
        reportRecordService.useDcmReport(reportType, belongDate, start, end);
    }
    
}
