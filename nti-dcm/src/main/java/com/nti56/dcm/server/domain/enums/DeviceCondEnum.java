package com.nti56.dcm.server.domain.enums;

import lombok.Getter;

public enum DeviceCondEnum {
    FAULT(1, "fault", "故障"),
    OFF_LINE(2, "offLine", "离线"),
    STAND_BY(3, "standBy", "空闲"),
    TASK(4, "task", "运行"),
    ;

    @Getter
    private Integer value;

    @Getter
    private String name;

    @Getter
    private String nameDesc;

    DeviceCondEnum(Integer value, String name, String nameDesc) {
        this.value = value;
        this.name = name;
        this.nameDesc = nameDesc;
    }

    public static DeviceCondEnum typeOfValue(Integer value){
        DeviceCondEnum[] values = DeviceCondEnum.values();
        for (DeviceCondEnum v : values) {
            if (v.value.equals(value)) {
                return v;
            }
        }
        return null;
    }

    public static DeviceCondEnum typeOfName(String name){
        DeviceCondEnum[] values = DeviceCondEnum.values();
        for (DeviceCondEnum v : values) {
            if (v.name.equals(name)) {
                return v;
            }
        }
        return null;
    }

    public static DeviceCondEnum typeOfNameDesc(String nameDesc){
        DeviceCondEnum[] values = DeviceCondEnum.values();
        for (DeviceCondEnum v : values) {
            if (v.nameDesc.equals(nameDesc)) {
                return v;
            }
        }
        return null;
    }
}
