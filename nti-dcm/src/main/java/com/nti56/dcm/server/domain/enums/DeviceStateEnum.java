package com.nti56.dcm.server.domain.enums;

import lombok.Getter;

public enum DeviceStateEnum {
    TASK(1, "task", "任务"),
    STAND_BY(2, "standBy", "空闲"),
    FAULT(3, "fault", "故障"),
    OFF_LINE(4, "offLine", "离线"),
    ;

    @Getter
    private Integer value;

    @Getter
    private String name;

    @Getter
    private String nameDesc;

    DeviceStateEnum(Integer value, String name, String nameDesc) {
        this.value = value;
        this.name = name;
        this.nameDesc = nameDesc;
    }

    public static DeviceStateEnum typeOfValue(Integer value){
        DeviceStateEnum[] values = DeviceStateEnum.values();
        for (DeviceStateEnum v : values) {
            if (v.value.equals(value)) {
                return v;
            }
        }
        return null;
    }

    public static DeviceStateEnum typeOfName(String name){
        DeviceStateEnum[] values = DeviceStateEnum.values();
        for (DeviceStateEnum v : values) {
            if (v.name.equals(name)) {
                return v;
            }
        }
        return null;
    }

    public static DeviceStateEnum typeOfNameDesc(String nameDesc){
        DeviceStateEnum[] values = DeviceStateEnum.values();
        for (DeviceStateEnum v : values) {
            if (v.nameDesc.equals(nameDesc)) {
                return v;
            }
        }
        return null;
    }
}
