package com.nti56.dcm.server.domain.enums;

import lombok.Getter;

public enum MessageTypeEnum {
    DEVICE_ERROR(1, "设备故障", "<div><p>设备:%s<p/><p>类型:%s<p/><p>故障时间:%s<p/><p>故障代码:%s<p/><p>%s<p/><div/>","%s %s发生故障/n故障描述:%s/n故障代码:%s"),
    DEVICE_OFFLINE(2, "设备离线", "<div><p>设备:%s<p/><p>类型:%s<p/><p>离线时间:%s<p/><p>%s<p/><div/>","%s %s离线"),
    DEVICE_HEALTH_LOW(3, "设备健康度较低", "<div><p>设备:%s<p/><p>类型:%s<p/><p>告警时间:%s<p/><p>%s<p/><div/>","%s %s健康度较低"),
    SPARES_STOCK_WARN(4, "备件库存告警", "<div><p>告警类型:%s<p/><p>备件名称:%s<p/><p>备件编号:%s<p/><p>当前库存:%s<p/><p>告警时间:%s<p/><div/>","%s 备件库存%s/n备件名称:%s; 备件编号:%s; 当前库存:%s;"),
    ORDER_TIMEOUT(5, "工单超时", "<div><p>编号:%s<p/><p>类型:%s<p/><p>进度:%s<p/><p>是否超时:%s<p/><p>%s<p/><div/>","%s %s%s（编号:%s）超时/n进度%s"),
    RECEIVE_ORDER(6, "收到新工单", "<div><p>编号:%s<p/><p>类型:%s<p/><p>进度:%s<p/><p>是否超时:%s<p/><p>%s<p/><div/>","%s 您收到新待处理工单/n%s; 编号:%s; 类型:%s; 进度:%s;"),
    DEVICE_MAINTENANCE_TIMEOUT(7, "设备超时未保养", "<div><p>设备:%s<p/><p>类型:%s<p/><p>告警时间:%s<p/><div/>","%s %s超时未保养"),

    DEVICE_MAINTENANCE_ADVENT(8, "设备维保即将到期", "<div><p>临期设备数:%s台<p/><p>%s<p/><p>以上设备将在30天内到期，请及时处理<p/><div/>","%s %s当前共有%s台设备将在30天内维保到期，请及时处理"),
    DEVICE_MAINTENANCE_EXPIRE(9, "设备维保已到期", "<div><p>到期设备数:%s台<p/><p>%s<p/><p>以上设备已到期，请及时处理<p/><div/>","%s %s当前共有%s台设备已维保到期，请及时处理"),
    DEVICE_WARRANTY_ADVENT(10, "设备质保即将到期", "<div><p>临期设备数:%s台<p/><p>%s<p/><p>以上设备将在30天内到期，请及时处理<p/><div/>","%s %s当前共有%s台设备将在30天内质保到期，请及时处理"),
    DEVICE_WARRANTY_EXPIRE(11, "设备质保已到期", "<div><p>到期设备数:%s台<p/><p>%s<p/><p>以上设备已到期，请及时处理<p/><div/>","%s %s当前共有%s台设备已质保到期，请及时处理"),
    DEVICE_SCRAP_ADVENT(12, "临近设备预计报废", "<div><p>临期设备数:%s台<p/><p>%s<p/><p>以上设备将在30天内计划报废，请及时处理<p/><div/>","%s %s当前共有%s台设备将在30天内计划报废，请及时处理"),
    DEVICE_SCRAP_EXPIRE(13, "设备预计报废已到期", "<div><p>到期设备数:%s台<p/><p>%s<p/><p>以上设备已到计划报废日期，请及时处理<p/><div/>","%s %s当前共有%s台设备已到计划报废日期，请及时处理"),
    DEVICE_ONLINE(14, "设备在线", "<div><p>设备:%s<p/><p>类型:%s<p/><p>在线时间:%s<p/><div/>","%s %s在线"),
    RECEIVE_REPORT_ORDER(15, "收到报修信息", "<div><p>编号:%s<p/><p>报修设备:%s<p/><p>报修人:%s<p/><div/>","%s 您收到新待处理报修信息/n编号:%s; 报修设备:%s; 报修人:%s;"),
    CANCEL_REPORT_ORDER(16, "报修信息反馈", "<div><p>编号:%s<p/><p>报修处理:%s<p/><p>原因:%s<p/><div/>","%s 您的报修信息（编号:%s）被关闭。/n原因为:%s;"),
    GENERATE_REPORT_ORDER(17, "报修信息反馈", "<div><p>编号:%s<p/><p>报修处理:%s<p/><p>工单编号:%s<p/><div/>","%s 您的报修信息（编号:%s）已转为维修工单。/n维修工单编号:%s。"),

    DEVICE_FAULT(21, "设备告警", "<div><p>设备:%s<p/><p>类型:%s<p/><p>告警时间:%s<p/><p>%s<p/><div/>","%s %s发生告警/n告警描述:%s"),
    ;

    @Getter
    private Integer value;

    @Getter
    private String title;

    @Getter
    private String showContent;

    @Getter
    private String saveContent;

    public String buildShowContent(String... args ) {
        return  String.format(this.showContent, args);
    }
    public String buildSaveContent(String... args ) {
        return  String.format(this.saveContent, args);
    }

    MessageTypeEnum(Integer value, String title, String showContent, String saveContent) {
        this.value = value;
        this.title = title;
        this.showContent = showContent;
        this.saveContent = saveContent;
    }

    public static MessageTypeEnum typeOfValue(Integer value){
        MessageTypeEnum[] values = MessageTypeEnum.values();
        for (MessageTypeEnum v : values) {
            if (v.value.equals(value)) {
                return v;
            }
        }
        return null;
    }

    public static MessageTypeEnum typeOfTitle(String title){
        MessageTypeEnum[] values = MessageTypeEnum.values();
        for (MessageTypeEnum v : values) {
            if (v.title.equals(title)) {
                return v;
            }
        }
        return null;
    }
}
