package com.nti56.dcm.server.controller;


import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.nti56.common.util.R;
import com.nti56.dcm.server.entity.WorkGroupDeviceEntity;
import com.nti56.dcm.server.model.dto.WorkGroupDeviceDto;
import com.nti56.dcm.server.service.IWorkGroupDeviceService;
import com.nti56.nlink.common.dto.TenantIsolation;
import com.nti56.nlink.common.util.PageParam;
import com.nti56.nlink.common.util.Result;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <p>
 * 班组负责设备表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-18
 */
@RestController
@RequestMapping("/workGroupDevice")
@Tag(name = "班组负责设备")
public class WorkGroupDeviceController {

    @Autowired
    private IWorkGroupDeviceService workGroupUserService;

    @GetMapping("/page")
    @Operation(summary = "获取班组负责设备分页")
    public R<Page<WorkGroupDeviceDto>> page(@RequestHeader("dcm_headers") TenantIsolation tenantIsolation, PageParam pageParam, WorkGroupDeviceDto dto) {
        Page<WorkGroupDeviceDto> page = pageParam.toPage(WorkGroupDeviceDto.class);
        Result<Page<WorkGroupDeviceDto>> result = workGroupUserService.getPage(dto, page, tenantIsolation);
        return R.result(result);
    }

    @PostMapping("")
    @Operation(summary = "新增班组负责设备")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "返回值",
                    content = {@Content(mediaType = MediaType.APPLICATION_JSON_VALUE,
                            schema = @Schema(implementation = WorkGroupDeviceEntity.class)
                    )})
    })
    public R createWorkGroupDevice(@RequestHeader("dcm_headers") TenantIsolation tenantIsolation,
                                   @RequestBody @Validated WorkGroupDeviceDto dto) {
        return R.result(workGroupUserService.create(dto, tenantIsolation));
    }


    @DeleteMapping("/{id}")
    @Operation(summary = "根据id删除班组负责设备")
    public R deleteWorkGroupDevice(@RequestHeader("dcm_headers") TenantIsolation tenantIsolation,
                                   @PathVariable Long id) {
        return R.result(workGroupUserService.deleteById(id, tenantIsolation));
    }


}
