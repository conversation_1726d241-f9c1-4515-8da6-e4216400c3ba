package com.nti56.dcm.server.controller;


import com.nti56.common.util.R;
import com.nti56.dcm.server.entity.WorkGroupEntity;
import com.nti56.dcm.server.model.dto.WorkGroupDto;
import com.nti56.dcm.server.service.IWorkGroupService;
import com.nti56.nlink.common.dto.TenantIsolation;
import com.nti56.nlink.common.util.Result;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <p>
 * 班组表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-02-26
 */
@RestController
@RequestMapping("/workGroup")
@Tag(name = "班组")
public class WorkGroupController {

    @Autowired
    private IWorkGroupService workGroupService;

    @GetMapping("/list")
    @Operation(summary = "获取班组数据")
    public R getTree(@RequestHeader("dcm_headers") TenantIsolation tenantIsolation,String type) {
        Result<List<WorkGroupDto>> result = workGroupService.getAllWorkGroup(tenantIsolation ,type);
        return R.result(result);
    }

    @GetMapping("/getDispatchUser")
    @Operation(summary = "获取班组数据")
    public R getDispatchUser(@RequestHeader("dcm_headers") TenantIsolation tenantIsolation,Long deviceId) {
        return R.result(workGroupService.getDispatchUser(tenantIsolation ,deviceId));
    }

    @PostMapping("")
    @Operation(summary = "新增班组")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "返回值",
                    content = {@Content(mediaType = MediaType.APPLICATION_JSON_VALUE,
                            schema = @Schema(implementation = WorkGroupEntity.class)
                    )})
    })
    public R createWorkGroup(@RequestHeader("dcm_headers") TenantIsolation tenantIsolation,
                             @RequestBody @Validated WorkGroupDto dto) {
        return R.result(workGroupService.create(dto, tenantIsolation));
    }


    @PutMapping("/{id}")
    @Operation(summary = "修改班组")
    public R editWorkGroup(@RequestHeader("dcm_headers") TenantIsolation tenantIsolation,
                           @RequestBody @Validated WorkGroupDto dto) {
        return R.result(workGroupService.edit(dto, tenantIsolation));
    }

    @DeleteMapping("/{id}")
    @Operation(summary = "根据id删除班组")
    public R deleteWorkGroup(@RequestHeader("dcm_headers") TenantIsolation tenantIsolation,
                             @PathVariable Long id) {
        return R.result(workGroupService.deleteById(id, tenantIsolation));
    }

}
