package com.nti56.dcm.server.domain.enums;

import lombok.Getter;

// 记录类型，1-单选，2-多选,3-数值，4-文本
public enum StandardRecordTypeEnum {
    SINGLE_SELECT(1, "SINGLE_SELECT", "单选"),
    MULTI_SELECT(2, "MULTI_SELECT", "多选"),
    NUMBER(3, "NUMBER", "数值"),
    TEXT(4, "TEXT", "文本"),
    ;

    @Getter
    private Integer value;

    @Getter
    private String name;

    @Getter
    private String nameDesc;

    StandardRecordTypeEnum(Integer value, String name, String nameDesc) {
        this.value = value;
        this.name = name;
        this.nameDesc = nameDesc;
    }

    public static StandardRecordTypeEnum typeOfValue(Integer value){
        StandardRecordTypeEnum[] values = StandardRecordTypeEnum.values();
        for (StandardRecordTypeEnum v : values) {
            if (v.value.equals(value)) {
                return v;
            }
        }
        return null;
    }

    public static StandardRecordTypeEnum typeOfName(String name){
        StandardRecordTypeEnum[] values = StandardRecordTypeEnum.values();
        for (StandardRecordTypeEnum v : values) {
            if (v.name.equals(name)) {
                return v;
            }
        }
        return null;
    }

    public static StandardRecordTypeEnum typeOfNameDesc(String nameDesc){
        StandardRecordTypeEnum[] values = StandardRecordTypeEnum.values();
        for (StandardRecordTypeEnum v : values) {
            if (v.nameDesc.equals(nameDesc)) {
                return v;
            }
        }
        return null;
    }
}
