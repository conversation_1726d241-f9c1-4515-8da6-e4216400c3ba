package com.nti56.dcm.server.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 基础数据-数据字典表 实体类
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @Ddate 2024-12-09 09:53:42
 * @since JDK 1.8
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@TableName("pmo_t_dict")
public class PmoTDictEntity implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
    * 主键ID
    */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
    * 字典分类ID
    */
    private Long categoryId;

    /**
    * 字典名称
    */
    private String name;

    /**
    * 字典编码
    */
    private String dictCode;

    /**
    * 排序
    */
    private Integer sortNo;

    /**
    * 状态 1启用 0禁用
    */
    private Integer status;

    /**
    * 删除
    */
    @TableLogic
    private Integer deleted;

    /**
    * 版本号
    */
    @Version
    private Long version;

    /**
    * 创建者ID
    */
    @TableField(fill = FieldFill.INSERT)
    private Long createBy;

    /**
    * 创建者名称
    */
    private String createByName;

    /**
    * 创建时间
    */
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
    * 更新者ID
    */
    private Long modifyBy;

    /**
    * 更新者名称
    */
    private String modifyByName;

    /**
    * 最后更新时间
    */
    private LocalDateTime modifyTime;

    /**
    * 租户ID
    */
    @TableField(fill = FieldFill.INSERT)
    private Long clientId;



}
