package com.nti56.dcm.server.controller;


import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.nti56.common.util.R;
import com.nti56.dcm.server.model.dto.DeviceRepairReportDto;
import com.nti56.dcm.server.model.dto.DeviceRepairReportHandleDto;
import com.nti56.dcm.server.model.dto.DeviceRepairReportQueryDto;
import com.nti56.dcm.server.service.IDeviceRepairReportService;
import com.nti56.nlink.common.dto.TenantIsolation;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <p>
 * 设备报修表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-17
 */
@RestController
@RequestMapping("/deviceRepairReport")
@Tag(name = "设备报修")
@Slf4j
public class DeviceRepairReportController {

    @Autowired
    private IDeviceRepairReportService deviceRepairReportService;

    @PostMapping("")
    @Operation(summary = "新增设备报修")
    public R createDeviceRepair(@RequestHeader("dcm_headers") TenantIsolation tenantIsolation,
                                @RequestBody @Validated DeviceRepairReportDto dto) {
        return R.result(deviceRepairReportService.create(dto, tenantIsolation));
    }

    @PutMapping("/{id}")
    @Operation(summary = "更新设备报修")
    public R editDeviceRepair(@RequestHeader("dcm_headers") TenantIsolation tenantIsolation,
                              @RequestBody @Validated DeviceRepairReportDto dto) {
        return R.result(deviceRepairReportService.edit(dto, tenantIsolation));
    }

    @DeleteMapping("/{id}")
    @Operation(summary = "删除设备报修")
    public R deleteDeviceRepair(@RequestHeader("dcm_headers") TenantIsolation tenantIsolation,
                                @PathVariable Long id) {
        return R.result(deviceRepairReportService.delete(id, tenantIsolation));
    }


    @PostMapping("/page")
    @Operation(summary = "获取设备报修分页")
    public R<Page<DeviceRepairReportDto>> page(@RequestHeader("dcm_headers") TenantIsolation tenantIsolation, @RequestBody DeviceRepairReportQueryDto queryDto) {
        Page<DeviceRepairReportQueryDto> page = queryDto.toPage(DeviceRepairReportQueryDto.class);
        return R.result(deviceRepairReportService.page(queryDto, page, tenantIsolation));
    }

    @PostMapping("/generateOrder")
    @Operation(summary = "生成维修工单")
    public R generateOrder(@RequestHeader("dcm_headers") TenantIsolation tenantIsolation, @RequestBody DeviceRepairReportHandleDto dto) {
        return R.result(deviceRepairReportService.generateOrder(dto, tenantIsolation));
    }

    @PostMapping("/cancel")
    @Operation(summary = "关闭报修")
    public R cancel(@RequestHeader("dcm_headers") TenantIsolation tenantIsolation, @RequestBody DeviceRepairReportHandleDto dto) {
        return R.result(deviceRepairReportService.cancel(dto, tenantIsolation));
    }

}
