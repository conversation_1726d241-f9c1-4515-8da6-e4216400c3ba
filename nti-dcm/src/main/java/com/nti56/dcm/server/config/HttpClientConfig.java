package com.nti56.dcm.server.config;

import org.apache.http.conn.ssl.NoopHostnameVerifier;
import org.apache.http.conn.ssl.SSLConnectionSocketFactory;
import org.apache.http.conn.ssl.TrustSelfSignedStrategy;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.ssl.SSLContextBuilder;
import org.apache.http.ssl.TrustStrategy;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.client.HttpComponentsClientHttpRequestFactory;
import org.springframework.web.client.RestTemplate;

import javax.net.ssl.SSLContext;
import java.security.KeyManagementException;
import java.security.KeyStoreException;
import java.security.NoSuchAlgorithmException;

/**
 * 类说明: <br/>
 *
 * <AUTHOR> <br/>
 * @version 1.0
 * @date 2021/5/25 16:58<br/>
 * @since JDK 1.8
 */
@Configuration
public class HttpClientConfig {

    @Bean("restTemplate")
    public RestTemplate restTemplate() throws NoSuchAlgorithmException, KeyStoreException, KeyManagementException {
        return new RestTemplate(httpComponentsClientHttpRequestFactory());
    }

    public HttpComponentsClientHttpRequestFactory httpComponentsClientHttpRequestFactory() throws NoSuchAlgorithmException, KeyStoreException, KeyManagementException {
        SSLContext sslContext = createSSLContextWithTLS12();

        // 创建一个 HttpClient，使用自定义的 SSLContext 和 TrustStrategy
        CloseableHttpClient httpClient = createHttpClientWithCustomSSLContext(sslContext);

        // 创建 ClientHttpRequestFactory，使用自定义的 HttpClient
        return new HttpComponentsClientHttpRequestFactory(httpClient);
    }
    private static SSLContext createSSLContextWithTLS12() throws NoSuchAlgorithmException, KeyManagementException, KeyStoreException {
        SSLContext sslContext = SSLContext.getInstance("TLSv1.2");

        // 创建一个 TrustStrategy，接受所有证书
        TrustStrategy acceptingTrustStrategy = new TrustSelfSignedStrategy();

        // 创建 SSLContextBuilder 并设置 TrustManager 和 SSLProtocol
        SSLContextBuilder builder = new SSLContextBuilder()
                .loadTrustMaterial(acceptingTrustStrategy)
                .setProtocol("TLSv1.2");

        // 初始化 SSLContext
        sslContext = builder.build();

        return sslContext;
    }

    private static CloseableHttpClient createHttpClientWithCustomSSLContext(SSLContext sslContext) {
        // 创建一个 SSLConnectionSocketFactory，使用自定义的 SSLContext 和 HostnameVerifier
        SSLConnectionSocketFactory sslsf = new SSLConnectionSocketFactory(
                sslContext,
                NoopHostnameVerifier.INSTANCE
        );

        // 创建一个 HttpClient，使用自定义的 SSLConnectionSocketFactory
        return HttpClients.custom()
                .setSSLSocketFactory(sslsf)
                .build();
    }
}
