package com.nti56.dcm.server.controller;

import com.nti56.common.util.R;
import com.nti56.dcm.server.entity.OrderProcessRecordEntity;
import com.nti56.dcm.server.service.OrderProcessRecordService;
import com.nti56.nlink.common.dto.TenantIsolation;
import com.nti56.nlink.common.util.Result;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 工单流程记录 控制器
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2024-06-17 09:27:30
 * @since JDK 1.8
 */
@RestController
@RequestMapping("orderProcessRecord")
@Slf4j
public class OrderProcessRecordController {

    @Autowired
    private OrderProcessRecordService service;


     /**
      * 获取对象
      * @param orderType 工单类型：1-维修，2-保养，3-巡检
      * @param isOutsourcing 是否外委
      * @param isDataRecord 是否数据型
      */
     @GetMapping("{orderType}/{isOutsourcing}/{isDataRecord}")
     public R<OrderProcessRecordEntity> get(@RequestHeader("dcm_headers") TenantIsolation tenant, @PathVariable Integer orderType,@PathVariable Boolean isOutsourcing,@PathVariable Boolean isDataRecord){
         Result<OrderProcessRecordEntity> result = service.getById(tenant.getTenantId(),tenant.getIdType(), isOutsourcing, isDataRecord);
         return R.result(result);
     }

    /**
     * 获取流程操作步骤
     * @param processInstanceId 流程实例ID
     */
    @GetMapping("list/{processInstanceId}")
    public R<List<OrderProcessRecordEntity>> get(@RequestHeader("dcm_headers") TenantIsolation tenant, @PathVariable String processInstanceId){
        Result<List<OrderProcessRecordEntity>> result = service.getListByProcessInstanceId(tenant.getTenantId(),processInstanceId);
        return R.result(result);
    }
    
}
