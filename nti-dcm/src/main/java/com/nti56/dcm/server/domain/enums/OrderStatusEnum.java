package com.nti56.dcm.server.domain.enums;

import lombok.Getter;

public enum OrderStatusEnum {
    WAIT_DISPATCH(1, "WAIT_DISPATCH", "待分派"),
    WAIT_RECEIVE(2, "WAIT_RECEIVE", "待接单"),
    EXECUTING(3, "EXECUTING", "执行中"),
    WAIT_ACCEPT(4, "WAIT_ACCEPT", "待验收"),
    ACCEPTED(5, "ACCEPTED", "验收通过"),
    REJECT(6, "REJECT", "验收不通过"),
    REVOKE(7, "REVOKE", "已撤销"),
    ;

    @Getter
    private Integer value;

    @Getter
    private String name;

    @Getter
    private String nameDesc;

    OrderStatusEnum(Integer value, String name, String nameDesc) {
        this.value = value;
        this.name = name;
        this.nameDesc = nameDesc;
    }

    public static OrderStatusEnum typeOfValue(Integer value){
        OrderStatusEnum[] values = OrderStatusEnum.values();
        for (OrderStatusEnum v : values) {
            if (v.value.equals(value)) {
                return v;
            }
        }
        return null;
    }

    public static OrderStatusEnum typeOfName(String name){
        OrderStatusEnum[] values = OrderStatusEnum.values();
        for (OrderStatusEnum v : values) {
            if (v.name.equals(name)) {
                return v;
            }
        }
        return null;
    }

    public static OrderStatusEnum typeOfNameDesc(String nameDesc){
        OrderStatusEnum[] values = OrderStatusEnum.values();
        for (OrderStatusEnum v : values) {
            if (v.nameDesc.equals(nameDesc)) {
                return v;
            }
        }
        return null;
    }
}
