package com.nti56.dcm.server.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.nti56.nlink.common.dto.TenantIsolation;
import com.nti56.nlink.common.util.BeanUtilsIntensifier;
import com.nti56.nlink.common.util.PageParam;
import com.nti56.common.util.R;
import com.nti56.nlink.common.util.Result;
import com.nti56.nlink.common.util.ServiceCodeEnum;
import com.nti56.dcm.server.service.InspectResultStandardService;
import com.nti56.dcm.server.entity.InspectResultStandardEntity;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Optional;
import java.util.function.Function;

import lombok.extern.slf4j.Slf4j;


/**
 * 点巡检结果标准表 控制器
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2024-03-13 16:56:29
 * @since JDK 1.8
 */
// @RestController
// @RequestMapping("inspectResultStandard")
// @Slf4j
public class InspectResultStandardController {

    // @Autowired
    // private InspectResultStandardService service;

    // /**
    //  * 获取分页
    //  * @param pageParam 分页参数
    //  */
    // @GetMapping("/page")
    // public R<Page<InspectResultStandardEntity>> page(@RequestHeader("dcm_headers") TenantIsolation tenant, PageParam pageParam,InspectResultStandardEntity entity){
    //     Page<InspectResultStandardEntity> page = pageParam.toPage(InspectResultStandardEntity.class);
    //     Result<Page<InspectResultStandardEntity>> result = service.getPage(tenant.getTenantId(), entity,page);
    //     return R.result(result);
    // }

    // /**
    //  * 获取列表
    //  */
    // @GetMapping("/list")
    // public R<List<InspectResultStandardEntity>> list(@RequestHeader("dcm_headers") TenantIsolation tenant, InspectResultStandardEntity entity){
    //     Result<List<InspectResultStandardEntity>> result = service.list(tenant.getTenantId(), entity);
    //     return R.result(result);
    // }

    // /**
    //  * 创建对象
    //  */
    // @PostMapping("/create")
    // public R<InspectResultStandardEntity> create(@RequestHeader("dcm_headers") TenantIsolation tenant, @RequestBody InspectResultStandardEntity entity){
    //     Result<InspectResultStandardEntity> result = service.save(tenant.getTenantId(), entity);
    //     return R.result(result);
    // }

    // /**
    //  * 更新对象
    //  */
    // @PutMapping("/update")
    // public R<Void> update(@RequestHeader("dcm_headers") TenantIsolation tenant, @RequestBody InspectResultStandardEntity entity){
    //     if (BeanUtilsIntensifier.checkBeanAndProperties(entity, InspectResultStandardEntity::getId)) {
    //         return R.error(ServiceCodeEnum.CODE_PARAM_ERROR.getCode());
    //     }
    //     Result<Void> result = service.update(tenant.getTenantId(), entity);
    //     return R.result(result);
    // }

    // /**
    //  * 删除对象
    //  * @param entityId 对象id
    //  */
    // @DeleteMapping("/{entityId}")
    // public R<Void> delete(@RequestHeader("dcm_headers") TenantIsolation tenant, @PathVariable Long entityId){
    //     Result<Void> result = service.deleteById(tenant.getTenantId(), entityId);
    //     return R.result(result);
    // }

    // /**
    //  * 获取对象
    //  * @param entityId 对象id
    //  */
    // @GetMapping("/{entityId}")
    // public R<InspectResultStandardEntity> get(@RequestHeader("dcm_headers") TenantIsolation tenant, @PathVariable Long entityId){
    //     Result<InspectResultStandardEntity> result = service.getById(tenant.getTenantId(), entityId);
    //     return R.result(result);
    // }
    
}
