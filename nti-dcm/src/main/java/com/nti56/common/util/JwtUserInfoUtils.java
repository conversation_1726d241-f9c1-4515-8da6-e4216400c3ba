package com.nti56.common.util;


import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.nimbusds.jose.JWSObject;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;
import javax.servlet.http.HttpServletRequest;
import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import java.nio.charset.StandardCharsets;
import lombok.extern.slf4j.Slf4j;

/**
 * 封装获取用户的信息
 * <AUTHOR>
 */
@Slf4j
public class JwtUserInfoUtils {

  public static final String AUTHORIZATION = "LoginToken";

  public static final String TOKEN_PREFIX = "Bearer ";

  public static final String FROM = "from";

  public static final String it = "it";



  /**
   * 获取用户id
   **/
  public static Long getUserId() {
    JSONObject payload = JSON.parseObject(userJson());
    if(payload ==null){
      return null;
    }
    String userInfo = payload.getString("userInfo");
    JSONObject userInfoObject = JSON.parseObject(userInfo);
    return userInfoObject.getLong("id");
  }

  /**
   * 获取用户id
   **/
  public static Long getUserId(String auth) {
    JSONObject payload = JSON.parseObject(userJson(auth));
    if(payload ==null){
      return null;
    }

    String userInfo = payload.getString("userInfo");
    JSONObject userInfoObject = JSON.parseObject(userInfo);
    return userInfoObject.getLong("id");
  }

  /**
   * 获取真实名字
   */
  public static String getRealname() {
    JSONObject payload = JSON.parseObject(userJson());
    if(payload ==null){
      return null;
    }
    String userInfo = payload.getString("userInfo");
    JSONObject userInfoObject = JSON.parseObject(userInfo);
    return userInfoObject.getString("empName");
  }

  /**
   * 获取真实名字
   */
  public static String getRealname(String auth) {
    JSONObject payload = JSON.parseObject(userJson(auth));
    if(payload ==null){
      return null;
    }
    String userInfo = payload.getString("userInfo");
    JSONObject userInfoObject = JSON.parseObject(userInfo);
    return userInfoObject.getString("empName");
  }

  /**
   * 获取用户名账号
   */
  public static String getUserName() {
    JSONObject payload = JSON.parseObject(userJson());
    if(payload ==null){
      return null;
    }
    String userInfo = payload.getString("userInfo");
    JSONObject userInfoObject = JSON.parseObject(userInfo);
    return userInfoObject.getString("empName");
  }

  /**
   * 获取用户名账号
   */
  public static String getUserName(String auth) {
    JSONObject payload = JSON.parseObject(userJson(auth));
    if(payload ==null){
      return null;
    }
    String userInfo = payload.getString("userInfo");
    JSONObject userInfoObject = JSON.parseObject(userInfo);
    return userInfoObject.getString("empName");
  }


  /**
   * 获取token
   **/
  public static String getToken() {
    HttpServletRequest request = getRequest();
    String token = request.getHeader(AUTHORIZATION);
    if (StringUtils.isEmpty(token)) {
      return null;
    }
    try {
      token = URLDecoder.decode(token, StandardCharsets.UTF_8.name());
    } catch (UnsupportedEncodingException e) {
      log.error(e.getMessage(), e);
    }
    return token;
  }

  public static String getToken(String auth) {
    String token = getAuthorization(auth);
    if (StringUtils.isEmpty(token)) {
      return null;
    }
    try {
      token = URLDecoder.decode(token, StandardCharsets.UTF_8.name());
    } catch (UnsupportedEncodingException e) {
      log.error(e.getMessage(), e);
    }
    return token;
  }

  /**
   * 获取带Brear token
   */
  public static String getAuthorization() {
    HttpServletRequest request = getRequest();
    String token = request.getHeader(AUTHORIZATION);
    if (StringUtils.isEmpty(token)) {
      return null;
    }
    return token;
  }

  public static String getAuthorization(String auth) {
    HttpServletRequest request = getRequest();
    String token = request.getHeader(auth);
    if (StringUtils.isEmpty(token)) {
      return null;
    }
    return token;
  }

  /**
   * 判断token是否存在
   */
  public static boolean isExistToken(String auth) {
    String token = getAuthorization(auth);
    return !StringUtils.isEmpty(token);
  }

  /**
   * 判断token是否存在
   */
  public static boolean isExistToken() {
    HttpServletRequest request = getRequest();
    String token = request.getHeader(AUTHORIZATION);
    return !StringUtils.isEmpty(token);
  }

  /**
   * 获取request
   */
  public static HttpServletRequest getRequest() {
    return ((ServletRequestAttributes) (RequestContextHolder.currentRequestAttributes())).getRequest();
  }

  /**
   * 获取jwt用户载体
   * @return String
   */
  public static String userJson() {
    try {
      String token = getToken();
      if (StringUtils.isBlank(token)) {
        return null;
      }
      token = token.replace(TOKEN_PREFIX, "");
      JWSObject jwsObject = JWSObject.parse(token);
      return jwsObject.getPayload().toString();
    } catch (Exception e) {
      log.error(e.getMessage());
      return null;
    }
  }

  public static String userJson(String auth) {
    try {
      String token = getToken(auth);
      if (StringUtils.isBlank(token)) {
        return null;
      }
      JWSObject jwsObject = JWSObject.parse(token);
      return jwsObject.getPayload().toString();
    } catch (Exception e) {
      log.error(e.getMessage());
      return null;
    }
  }

}
