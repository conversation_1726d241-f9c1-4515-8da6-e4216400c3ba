package com.nti56.dcm.server.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 国家城市表 实体类
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @Ddate 2024-12-09 09:53:42
 * @since JDK 1.8
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@TableName("pmo_national_city")
public class PmoNationalCityEntity implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
    * 主键
    */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
    * 国内/国外   0:国内  1:国外
    */
    private Integer type;

    /**
    * 国家/城市名称
    */
    private String name;

    /**
    * 国家/城市编号
    */
    private String code;

    /**
    * 上级id
    */
    private Long pId;

    /**
    * 区域
    */
    private String region;

    /**
    * 删除状态; 0:未删除，1：已删除
    */
    @TableLogic
    private Integer deleted;

    /**
    * 创建人
    */
    @TableField(fill = FieldFill.INSERT)
    private String createBy;

    /**
    * 创建人姓名
    */
    private String createByName;

    /**
    * 创建时间
    */
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
    * 修改人
    */
    private String modifyBy;

    /**
    * 创建人姓名
    */
    private String modifyByName;

    /**
    * 更新时间
    */
    private LocalDateTime modifyTime;

    /**
    * 版本号
    */
    @Version
    private Long version;



}
