package com.nti56.dcm.server.controller;


import com.nti56.common.util.R;
import com.nti56.dcm.server.model.dto.WorkGroupScheduleArrangeDto;
import com.nti56.dcm.server.model.dto.WorkGroupScheduleLoopArrangeDto;
import com.nti56.dcm.server.model.dto.WorkGroupScheduleQueryDto;
import com.nti56.dcm.server.model.vo.WorkGroupScheduleVo;
import com.nti56.dcm.server.service.IWorkGroupScheduleService;
import com.nti56.nlink.common.dto.TenantIsolation;
import com.nti56.nlink.common.util.Result;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <p>
 * 班组排班 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-19
 */
@RestController
@RequestMapping("/workGroupSchedule")
@Tag(name = "班组排班")
public class WorkGroupScheduleController {

    @Autowired
    private IWorkGroupScheduleService workGroupScheduleService;

    @GetMapping("/listArrange")
    @Operation(summary = "查询班组排班信息")
    public R<List<WorkGroupScheduleVo>> listArrange(@RequestHeader("dcm_headers") TenantIsolation tenantIsolation, WorkGroupScheduleQueryDto dto) {
        Result<List<WorkGroupScheduleVo>> result = workGroupScheduleService.listArrange(dto, tenantIsolation);
        return R.result(result);
    }

    @PostMapping("loopArrange")
    @Operation(summary = "循环排班")
    public R loopArrange(@RequestHeader("dcm_headers") TenantIsolation tenantIsolation,
                         @RequestBody @Validated WorkGroupScheduleLoopArrangeDto dto) {
        return R.result(workGroupScheduleService.loopArrange(dto, tenantIsolation));
    }

    @PostMapping("/arrange")
    @Operation(summary = "单个排班设置")
    public R arrange(@RequestHeader("dcm_headers") TenantIsolation tenantIsolation,
                     @RequestBody @Validated WorkGroupScheduleArrangeDto dto) {
        return R.result(workGroupScheduleService.arrange(dto, tenantIsolation));
    }

    @PostMapping("/clearShift")
    @Operation(summary = "清空排班班次")
    public R clearShift(@RequestHeader("dcm_headers") TenantIsolation tenantIsolation,
                        @RequestBody @Validated WorkGroupScheduleArrangeDto dto) {
        return R.result(workGroupScheduleService.clearShift(dto, tenantIsolation));
    }
}
