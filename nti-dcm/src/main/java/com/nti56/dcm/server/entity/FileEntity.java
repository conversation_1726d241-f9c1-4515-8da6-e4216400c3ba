package com.nti56.dcm.server.entity;

import java.time.LocalDateTime;

import com.baomidou.mybatisplus.annotation.TableName;

import lombok.Data;

@Data
@TableName(value ="file")
public class FileEntity extends BaseEntity{
    
    /**
     * 文件类型，参考FileTypeEnum
     */
    private Integer fileType;
    /**
     * 文件名
     */
    private String fileName;
    /**
     * 文件描述
     */
    private String fileDesc;
    /**
     * 文件后缀
     */
    private String fileSuffix;
    /**
     * 文件路径
     */
    private String path;
    /**
     * 文件url
     */
    private String url;
    /**
     * 文件大小，单位B
     */
    private Integer fileSize;
    /**
     * 业务类型，参考FileBizTypeEnum
     */
    private Integer bizType;
    /**
     * 所属业务id
     */
    private Long bizId;
    /**
     * 上传时间
     */
    private LocalDateTime uploadTime;
    /**
     * 上传者
     */
    private Long uploader;

    /**
     * 身份，1-供应商，2-客户
     */
    private Integer idType;
}
