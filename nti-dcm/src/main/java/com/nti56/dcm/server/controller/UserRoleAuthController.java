package com.nti56.dcm.server.controller;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

import com.nti56.dcm.server.service.TenantIdentityService;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.nti56.common.util.JwtUserInfoUtils;
import com.nti56.common.util.R;
import com.nti56.dcm.server.domain.UserRoleAuth;
import com.nti56.dcm.server.entity.AuthEntity;
import com.nti56.dcm.server.entity.MenuEntity;
import com.nti56.dcm.server.entity.UserRoleEntity;
import com.nti56.dcm.server.model.vo.MenuVo;
import com.nti56.dcm.server.service.MenuService;
import com.nti56.dcm.server.service.UserCenterService;
import com.nti56.dcm.server.service.UserRoleAuthService;
import com.nti56.nlink.common.dto.TenantIsolation;
import com.nti56.nlink.common.util.Result;

import lombok.extern.slf4j.Slf4j;

/**
 * 用户角色权限 控制器
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2024-02-26 16:54:02
 * @since JDK 1.8
 */
@RestController
@RequestMapping("userRoleAuth")
@Slf4j
public class UserRoleAuthController {
    
    @Autowired
    private UserRoleAuthService userRoleAuthService;

    @Autowired
    private MenuService menuService;

    @Autowired
    private UserCenterService userCenterService;

    @Autowired
    private TenantIdentityService tenantIdentityService;



    /**
     * 获取当前用户是否是集成商
     */
    @GetMapping("/getCurrentUserIsAggregator")
    public R<Boolean> getCurrentUserIsAggregator(
        @RequestHeader("dcm_headers") TenantIsolation tenant
    ){
        Boolean currentUserIsAdministrator = userRoleAuthService.getCurrentUserIsAdministrator(tenant);
        return R.ok(currentUserIsAdministrator);
    }

    @GetMapping("/init")
    public void init(@RequestHeader("dcm_headers") TenantIsolation tenant){
        userRoleAuthService.initCustomerRole(tenant.getTenantId());
    }

}
