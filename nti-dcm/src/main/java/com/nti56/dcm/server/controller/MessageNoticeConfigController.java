package com.nti56.dcm.server.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.nti56.dcm.server.entity.MessageNoticeConfigEntity;
import com.nti56.dcm.server.entity.MessageNoticeStrategyEntity;
import com.nti56.dcm.server.model.dto.MessageNoticeConfigEditDto;
import com.nti56.dcm.server.model.dto.MessageNoticeConfigQueryDto;
import com.nti56.dcm.server.model.dto.MessageNoticeConfigSaveDto;
import com.nti56.dcm.server.service.MessageNoticeConfigService;
import com.nti56.nlink.common.dto.TenantIsolation;
import com.nti56.nlink.common.util.*;
import io.swagger.annotations.ApiOperation;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <p>
 * 消息通知配置表 控制器
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @Ddate 2024-06-24 14:01:32
 * @since JDK 1.8
 */
@RestController
@RequestMapping("messageNoticeConfig")
@Tag(name = "消息通知配置表模块")
public class MessageNoticeConfigController {

    @Autowired
    MessageNoticeConfigService service;


    @GetMapping("list")
    @ApiOperation(value = "获取列表")
    public R list(@RequestHeader("dcm_headers") TenantIsolation tenantIsolation, MessageNoticeConfigQueryDto dto) {
        Result<List<MessageNoticeConfigEntity>> result = service.list(tenantIsolation, dto);
        return R.result(result);
    }

    @PostMapping("create")
    @ApiOperation("创建对象")
    public R create(@RequestHeader("dcm_headers") TenantIsolation tenantIsolation, @RequestBody @Validated MessageNoticeConfigEditDto dto) {
        return R.result(service.save(tenantIsolation, dto));
    }

    @PutMapping("update")
    @ApiOperation("更新")
    public R update(@RequestHeader("dcm_headers") TenantIsolation tenantIsolation, @RequestBody @Validated MessageNoticeConfigEditDto dto) {
        return R.result(service.update(tenantIsolation, dto));
    }

    @PostMapping("saveBatch")
    @ApiOperation("批量保存")
    public R saveBatch(@RequestHeader("dcm_headers") TenantIsolation tenantIsolation, @RequestBody @Validated MessageNoticeConfigSaveDto dto) {
        return R.result(service.saveBatch(tenantIsolation, dto));
    }

    @DeleteMapping("/{entityId}")
    @ApiOperation("删除对象")
    public R delete(@RequestHeader("dcm_headers") TenantIsolation tenantIsolation, @PathVariable Long entityId) {
        return R.result(service.deleteById(entityId));
    }

    @PutMapping("changeEnableStatus")
    @ApiOperation("更新启用状态,传入id和status")
    public R update(@RequestHeader("dcm_headers") TenantIsolation tenantIsolation, @RequestBody MessageNoticeConfigEntity entity) {
        return R.result(service.changeEnableStatus(entity, tenantIsolation));
    }

    @GetMapping("initSupplierTimeoutConfig")
    @Operation(summary = "初始化存量租户工单流转站内信通知配置")
    public R initSupplierTimeoutConfig() {
        return R.result(service.initExistTenantMessageNoticeConfig());
    }

}
