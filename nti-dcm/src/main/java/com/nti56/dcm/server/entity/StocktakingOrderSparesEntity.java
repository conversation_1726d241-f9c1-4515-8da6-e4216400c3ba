package com.nti56.dcm.server.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import lombok.Data;

/**
 * <p>
 * 盘点货位表 实体类
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @Ddate 2024-05-08 13:49:40
 * @since JDK 1.8
 */
@Data
@TableName("stocktaking_order_spares")
public class StocktakingOrderSparesEntity extends BaseEntity{
        /**
        * 所属盘点单id
        */
        private Long stocktakingOrderId;

        /**
        * 盘点备件id
        */
        private Long sparesId;



}
