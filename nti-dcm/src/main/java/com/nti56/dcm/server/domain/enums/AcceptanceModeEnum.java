package com.nti56.dcm.server.domain.enums;

import lombok.Getter;
public enum AcceptanceModeEnum {
    INITIATOR(2, "发起人验收", "发起人验收"),
    DESIGNEE(1, "指定人员验收", "指定人员验收"),
    NOT_ACCEPTANCE(0, "无需验收直接通过", "无需验收直接通过"),
    ;

    @Getter
    private Integer value;

    @Getter
    private String name;

    @Getter
    private String nameDesc;

    AcceptanceModeEnum(Integer value, String name, String nameDesc) {
        this.value = value;
        this.name = name;
        this.nameDesc = nameDesc;
    }

    public static AcceptanceModeEnum typeOfValue(Integer value){
        AcceptanceModeEnum[] values = AcceptanceModeEnum.values();
        for (AcceptanceModeEnum v : values) {
            if (v.value.equals(value)) {
                return v;
            }
        }
        return null;
    }

    public static AcceptanceModeEnum typeOfName(String name){
        AcceptanceModeEnum[] values = AcceptanceModeEnum.values();
        for (AcceptanceModeEnum v : values) {
            if (v.name.equals(name)) {
                return v;
            }
        }
        return null;
    }

    public static AcceptanceModeEnum typeOfNameDesc(String nameDesc){
        AcceptanceModeEnum[] values = AcceptanceModeEnum.values();
        for (AcceptanceModeEnum v : values) {
            if (v.nameDesc.equals(nameDesc)) {
                return v;
            }
        }
        return null;
    }
}
