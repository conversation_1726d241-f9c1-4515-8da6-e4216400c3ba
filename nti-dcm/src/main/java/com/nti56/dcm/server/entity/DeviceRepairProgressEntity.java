package com.nti56.dcm.server.entity;

import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import java.io.Serializable;

import com.baomidou.mybatisplus.annotation.Version;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 设备维修流程表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-02-29
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("device_repair_progress")
public class DeviceRepairProgressEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * ID
     */
    private Long id;

    /**
     * 设备维修工单id
     */
    private Long deviceRepairId;

    /**
     * 流程类型 参考DeviceRepairProgressEnum
     */
    private Integer progressType;

    /**
     * 进度执行人id
     */
    private Long executeUserId;

    /**
     * 进度执行人名称
     */
    private String executeUserName;

    /**
     * 进度执行时间
     */
    private LocalDateTime executeTime;

    /**
     * 委外执行（0-内部，1-供应商）
     */
    private Integer outsourceExecute;

    /**
     * 接收工单人
     */
    private String receiveUser;

    /**
     * 故障类型
     */
    private Long faultType;

    /**
     * 故障原因
     */
    private String faultReason;

    /**
     * 处理过程
     */
    private String repairProcess;

    /**
     * 维修开始时间
     */
    private LocalDateTime repairStartTime;

    /**
     * 维修结束时间
     */
    private LocalDateTime repairEndTime;

    /**
     * 维修耗时（时分格式）
     */
    private String repairUseTime;


    /**
     * 验收状态 0 验收不通过 1 验收通过
     */
    private Integer acceptState;
    /**
     * 验收说明
     */
    private String acceptExplain;

    /**
     * 创建人
     */
    @TableField(fill = FieldFill.INSERT)
    private String creator;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 修改人
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private String updator;

    /**
     * 修改时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    /**
     * 租户ID
     */
    @TableField(fill = FieldFill.INSERT)
    private Long tenantId;

    /**
     * 工程ID
     */
    @TableField(fill = FieldFill.INSERT)
    private Long engineeringId;

    /**
     * 模块ID
     */
    @TableField(fill = FieldFill.INSERT)
    private Long moduleId;

    /**
     * 空间ID
     */
    @TableField(fill = FieldFill.INSERT)
    private Long spaceId;

    /**
     * 版本号
     */
    @Version
    private Integer version;

    /**
     * 删除
     */
    @TableLogic
    private Integer deleted;


}
