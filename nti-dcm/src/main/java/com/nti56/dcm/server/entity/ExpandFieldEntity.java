package com.nti56.dcm.server.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 拓展字段表 实体类
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @Ddate 2024-08-09 14:53:11
 * @since JDK 1.8
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@TableName("expand_field")
public class ExpandFieldEntity implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * id
     */
    private Long id;

    /**
     * 租户id
     */
    @TableField(fill = FieldFill.INSERT)
    private Long tenantId;

    /**
     * 身份，1-供应商，2-客户
     */
    @NotNull(message = "身份类型不能为空")
    private Integer idType;

    /**
     * 字段key名
     */
    private String field;

    /**
     * 字段显示名
     */
    @NotBlank(message = "字段显示名不能为空")
    private String label;

    /**
     * 是否必填, false-否，true-是
     */
    private Boolean required;

    /**
     * 字段类型, 1-文本，2-单选
     */
    @NotNull(message = "字段类型不能为空")
    private Integer type;

    /**
     * 单选选项值
     */
    private String optionValue;

    /**
     * 序号
     */
    private Integer sortNo;

    /**
     * 版本号
     */
    @Version
    private Integer version;

    /**
     * 删除
     */
    @TableLogic
    private Integer deleted;

    /**
     * 创建人ID
     */
    private Long creatorId;

    /**
     * 创建人
     */
    @TableField(fill = FieldFill.INSERT)
    private String creator;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 更新人ID
     */
    private Long updatorId;

    /**
     * 更新人
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private String updator;

    /**
     * 更新时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    /**
     * 工程ID
     */
    @TableField(fill = FieldFill.INSERT)
    private Long engineeringId;

    /**
     * 模块ID
     */
    @TableField(fill = FieldFill.INSERT)
    private Long moduleId;

    /**
     * 空间ID
     */
    @TableField(fill = FieldFill.INSERT)
    private Long spaceId;

    /**
     * 是否系统字段, 0-自定义，1-系统
     */
    private Integer systemField;

}
