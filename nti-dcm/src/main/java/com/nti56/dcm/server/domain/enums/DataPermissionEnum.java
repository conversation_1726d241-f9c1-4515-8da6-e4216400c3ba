package com.nti56.dcm.server.domain.enums;

/**
 * <AUTHOR>
 * @version v1.0
 * @date 2024-12-17 15:58:13
 */
public enum DataPermissionEnum {

    ROLE(1,"拥有角色权限"),
    DEPT(2,"拥有部门权限"),
    USER(3,"拥有个人权限"),
    ;

    private Integer code;
    private String desc;

    DataPermissionEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public Integer getCode() {
        return code;
    }

    public void setCode(Integer code) {
        this.code = code;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }
}
