package com.nti56.dcm.server.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

@Data
@Component
@ConfigurationProperties(prefix = "danswer")
public class DanswerConfig {
    /**
     * danswer服务url
     */
    private String baseUrl;

    private String username;

    private String password;
    /**
     * login method
     */
    private String login;

    /**
     * getUserChatSessions method
     */
    private String getUserChatSessions;

    /**
     * getChatSession method
     */
    private String getChatSession;

    /**
     * deleteChatSession method
     */
    private String deleteChatSession;


    /**
     * createChatMessageFeedback method
     */
    private String createChatMessageFeedback;

    /**
     * createChatSession method
     */
    private String createChatSession;

    /**
     * renameChatSession method
     */
    private String renameChatSession;

    /**
     * sendMessage method
     */
    private String sendMessage;

    /**
     * sendMessage method
     */
    private String flushConnector;

    /**
     * verifyToken method
     */
    private String verifyToken;

    private Sftp sftp;
    @Data
    public static class Sftp{
        private String destIp;
        private String username;
        private String password;
        private String destPathMaintenance;
        private String destPathRepair;
        private Integer repairConnectorId;
        private Integer maintenanceConnectorId;

    }
}
