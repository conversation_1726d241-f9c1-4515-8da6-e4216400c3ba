package com.nti56.dcm.server.domain.enums;

import lombok.Getter;

@Getter
public enum WarehouseOrderTypeEnum {
    COMMON(1L, "普通出入库", "所有自定义都是普通出入库"),
    RECEIVE_AND_RESTORED(2L, "领用出库", "归还入库"),
    ALLOCATE(3L, "调拨出库", "调拨入库"),
    STOCKTAKING(4L, "盘点出库", "盘点入库"),
    ;

    private Long value;

    private String name;

    private String nameDesc;

    WarehouseOrderTypeEnum(Long value, String name, String nameDesc) {
        this.value = value;
        this.name = name;
        this.nameDesc = nameDesc;
    }

    public static WarehouseOrderTypeEnum typeOfValue(Long value){
        WarehouseOrderTypeEnum[] values = WarehouseOrderTypeEnum.values();
        for (WarehouseOrderTypeEnum v : values) {
            if (v.value.equals(value)) {
                return v;
            }
        }
        return null;
    }

    public static WarehouseOrderTypeEnum typeOfName(String name){
        WarehouseOrderTypeEnum[] values = WarehouseOrderTypeEnum.values();
        for (WarehouseOrderTypeEnum v : values) {
            if (v.name.equals(name)) {
                return v;
            }
        }
        return null;
    }

    public static WarehouseOrderTypeEnum typeOfNameDesc(String nameDesc){
        WarehouseOrderTypeEnum[] values = WarehouseOrderTypeEnum.values();
        for (WarehouseOrderTypeEnum v : values) {
            if (v.nameDesc.equals(nameDesc)) {
                return v;
            }
        }
        return null;
    }
}
