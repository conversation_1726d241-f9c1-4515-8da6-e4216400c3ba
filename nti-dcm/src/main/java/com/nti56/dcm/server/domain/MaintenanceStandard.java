package com.nti56.dcm.server.domain;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.bean.copier.CopyOptions;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjUtil;
import cn.hutool.core.util.StrUtil;
import com.nti56.dcm.server.entity.MaintenanceStandardEntity;
import com.nti56.dcm.server.model.dto.MaintenanceStandardDto;
import com.nti56.nlink.common.base.FieldValue;
import com.nti56.nlink.common.base.UniqueConstraint;
import com.nti56.nlink.common.fetcher.CommonFetcher;
import com.nti56.nlink.common.util.Result;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

@Slf4j
public class MaintenanceStandard {

    public static final SerialNumber SERIALNUMBER = new SerialNumber("BYBZ");

    private static final UniqueConstraint uniqueConstraint = new UniqueConstraint("standard_name","id_type");

    @Getter
    private MaintenanceStandardEntity entity;

    public static Result<MaintenanceStandard> checkCreate(MaintenanceStandardDto dto, CommonFetcher commonFetcher) {
        if (StrUtil.isBlank(dto.getStandardName())) {
            return Result.error("保养标准名称不能为空！");
        }
        // 校验是否存在重名
        MaintenanceStandardEntity repeatStandard = commonFetcher.get(uniqueConstraint.buildUnique(new FieldValue(dto.getStandardName()),new FieldValue(dto.getIdType())), MaintenanceStandardEntity.class);
        if (ObjUtil.isNotNull(repeatStandard)) {
            return Result.error("保养标准名称已存在：" + dto.getStandardName());
        }
        MaintenanceStandardEntity entity = new MaintenanceStandardEntity();
        BeanUtils.copyProperties(dto, entity);
        MaintenanceStandard maintenanceStandard = new MaintenanceStandard();
        maintenanceStandard.entity = entity;
        return Result.ok(maintenanceStandard);
    }


    public static Result<MaintenanceStandard> checkUpdate(MaintenanceStandardDto dto, CommonFetcher commonFetcher) {
        if (Objects.isNull(dto.getId())) {
            return Result.error("保养标准ID不能为空！");
        }

        MaintenanceStandardEntity oldEntity = commonFetcher.get(dto.getId(), MaintenanceStandardEntity.class);
        if (Objects.isNull(oldEntity)) {
            return Result.error("不存在的保养标准！");
        }
        // 校验是否存在重名
        MaintenanceStandardEntity repeatStandard = commonFetcher.get(uniqueConstraint.buildUnique(new FieldValue(dto.getStandardName()),new FieldValue(dto.getIdType())), MaintenanceStandardEntity.class);
        if (ObjUtil.isNotNull(repeatStandard) &&  !repeatStandard.getId().equals(oldEntity.getId())) {
            return Result.error("保养标准名称已存在：" + dto.getStandardName());
        }
        MaintenanceStandard maintenanceStandard = new MaintenanceStandard();
        BeanUtil.copyProperties(dto, oldEntity, CopyOptions.create().setIgnoreNullValue(false).setIgnoreProperties("tenantId"));
        maintenanceStandard.entity = oldEntity;
        return Result.ok(maintenanceStandard);
    }


    public static Result<Void> checkDelete(Long id, CommonFetcher commonFetcher) {
        MaintenanceStandardEntity existStandard = commonFetcher.get(id, MaintenanceStandardEntity.class);
        if (Objects.isNull(existStandard)) {
            return Result.error("保养标准不存在，无法删除！");
        }
        return Result.ok();
    }


}
