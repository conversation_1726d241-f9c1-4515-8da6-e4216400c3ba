package com.nti56.dcm.server.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import lombok.Data;

/**
 * <p>
 * 点巡检计划表 实体类
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @Ddate 2024-02-23 15:13:54
 * @since JDK 1.8
 */
@Data
@TableName("inspect_plan")
public class InspectPlanEntity extends BaseEntity{
        /**
        * 计划编码
        */
        private String planNumber;

        /**
        * 计划名称
        */
        private String planName;

        /**
        * 计划说明
        */
        private String planDesc;

        /**
        * 检查类型，1-点检，2-巡检
        */
        private Integer inspectType;

        /**
        * 检查模式，1-单次检查，2-周期性检查，3-多次检查
        */
        private Integer inspectMode;

        /**
        * 单次计划检查时间
        */
        private LocalDateTime singleInspectTime;
        
        /**
        * 多次计划检查时间，年月日时分秒，逗号分隔
        */
        private String multiInspectTime;

        /**
        * 周期性检查开始时间
        */
        private LocalDate periodInspectBegin;

        /**
        * 周期性检查结束时间
        */
        private LocalDate periodInspectEnd;

        /**
        * 执行周期，1-每日，2-每周，3-每月
        */
        private Integer periodInspectType;

        /**
        * 周几/几号执行，1-7-周一到周日，1-31-1号到31号，多个用逗号隔开
        */
        private String periodInspectDay;

        /**
        * 执行时间
        */
        private LocalTime periodInspectTime;

        /**
        * 是否委外，1-是，0-否
        */
        private Integer outsource;

        /**
        * 委外供应商id
        */
        private Long supplierId;

        /**
        * 委外供应商名称
        */
        private String supplierName;

        /**
         * 是否停止，1-是，0-否，null-否
         */
        private Integer isStop;

        /**
        * 委外客户id
        */
        private Long customerId;

        /**
        * 委外客户名称
        */
        private String customerName;
        
        /**
         * 创建来源 1-供应商/集成商 2-客户
         */
        private Integer createSource;

        /**
         * 计划等级 1-紧急 2-重要 3-普通 4-其他
         */
        private Integer level;

        /**
         * 工单生成策略 1-提前生成 2-立即生成
         */
        private Integer orderCreateStrategy;

        /**
         * 提前生成数
         */
        private Integer createBeforeNumber;

        /**
         * 提前生成单位，1-天，2-小时
         */
        private Integer createBeforeUnit;
        
        /**
        * 最近一次工单生成时间
        */
        private LocalDateTime recentOrderCreateTime;
        
        /**
        * 下次工单生成时间
        */
        private LocalDateTime nextOrderCreateTime;

        
        /**
         * 状态，1-进行中，2-已结束
         */
        private Integer status;

        /**
        * 身份类型, 1-供应商，2-客户
        */
        private Integer idType;

        /**
        * 外委供应商类型：1-租户型，2 数据型
        */
        private Integer outsourceSupplierType;
        
        /**
        * 外委客户类型：1-租户型，2 数据型
        */
        private Integer outsourceCustomerType;
}
