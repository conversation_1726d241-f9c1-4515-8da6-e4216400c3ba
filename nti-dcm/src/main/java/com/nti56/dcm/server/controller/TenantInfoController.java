package com.nti56.dcm.server.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.nti56.nlink.common.dto.TenantIsolation;
import com.nti56.nlink.common.util.BeanUtilsIntensifier;
import com.nti56.nlink.common.util.PageParam;
import com.nti56.common.util.R;
import com.nti56.nlink.common.util.Result;
import com.nti56.nlink.common.util.ServiceCodeEnum;
import com.nti56.dcm.server.service.TenantInfoService;
import com.nti56.dcm.server.entity.TenantInfoEntity;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Optional;
import java.util.function.Function;

import lombok.extern.slf4j.Slf4j;


/**
 * 租户信息表 控制器
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2024-05-20 13:43:15
 * @since JDK 1.8
 */
// @RestController
// @RequestMapping("/tenant-info")
@Slf4j
public class TenantInfoController {

    // @Autowired
    // private TenantInfoService service;

    // /**
    //  * 获取分页
    //  * @param pageParam 分页参数
    //  */
    // @GetMapping("/page")
    // public R<Page<TenantInfoEntity>> page(@RequestHeader("dcm_headers") TenantIsolation tenant, PageParam pageParam,TenantInfoEntity entity){
    //     Page<TenantInfoEntity> page = pageParam.toPage(TenantInfoEntity.class);
    //     Result<Page<TenantInfoEntity>> result = service.getPage(tenant.getTenantId(), entity,page);
    //     return R.result(result);
    // }

    // /**
    //  * 获取列表
    //  */
    // @GetMapping("/list")
    // public R<List<TenantInfoEntity>> list(@RequestHeader("dcm_headers") TenantIsolation tenant, TenantInfoEntity entity){
    //     Result<List<TenantInfoEntity>> result = service.list(tenant.getTenantId(), entity);
    //     return R.result(result);
    // }

    // /**
    //  * 创建对象
    //  */
    // @PostMapping("/create")
    // public R<TenantInfoEntity> create(@RequestHeader("dcm_headers") TenantIsolation tenant, @RequestBody TenantInfoEntity entity){
    //     Result<TenantInfoEntity> result = service.save(tenant.getTenantId(), entity);
    //     return R.result(result);
    // }

    // /**
    //  * 更新对象
    //  */
    // @PutMapping("/update")
    // public R<Void> update(@RequestHeader("dcm_headers") TenantIsolation tenant, @RequestBody TenantInfoEntity entity){
    //     if (BeanUtilsIntensifier.checkBeanAndProperties(entity, TenantInfoEntity::getId)) {
    //         return R.error(ServiceCodeEnum.CODE_PARAM_ERROR.getCode());
    //     }
    //     Result<Void> result = service.update(tenant.getTenantId(), entity);
    //     return R.result(result);
    // }

    // /**
    //  * 删除对象
    //  * @param entityId 对象id
    //  */
    // @DeleteMapping("/{entityId}")
    // public R<Void> delete(@RequestHeader("dcm_headers") TenantIsolation tenant, @PathVariable Long entityId){
    //     Result<Void> result = service.deleteById(tenant.getTenantId(), entityId);
    //     return R.result(result);
    // }

    // /**
    //  * 获取对象
    //  * @param entityId 对象id
    //  */
    // @GetMapping("/{entityId}")
    // public R<TenantInfoEntity> get(@RequestHeader("dcm_headers") TenantIsolation tenant, @PathVariable Long entityId){
    //     Result<TenantInfoEntity> result = service.getById(tenant.getTenantId(), entityId);
    //     return R.result(result);
    // }
    
}
