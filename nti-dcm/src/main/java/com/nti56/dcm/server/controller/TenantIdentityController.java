package com.nti56.dcm.server.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.nti56.common.tenant.TenantIsolationThreadLocal;
import com.nti56.dcm.server.model.dto.BatchSetIdTypeDto;
import com.nti56.dcm.server.model.dto.SysClientDto;
import com.nti56.dcm.server.service.TenantIdentityService;
import com.nti56.dcm.server.service.UserCenterService;
import com.nti56.nlink.common.dto.TenantIsolation;
import com.nti56.nlink.common.util.PageParam;
import com.nti56.nlink.common.util.R;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Nullable;
import java.util.List;

/**
 * <p>
 * 租户身份表 控制器
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @Ddate 2024-04-10 09:49:58
 * @since JDK 1.8
 */
@RestController
@RequestMapping("/tenantIdentity")
@Tag(name = "租户身份表模块")
public class TenantIdentityController {

    @Autowired
    private TenantIdentityService service;

    @PostMapping("setting")
    @Operation(summary = "设置租户身份")
    public R setting(@RequestHeader("dcm_headers") TenantIsolation tenantIsolation, @RequestParam("idTypes") List<Integer> idTypes) {
        return R.result(service.setting(tenantIsolation, idTypes));
    }

    @GetMapping("listIdentutyByUser")
    @Operation(summary = "查询租户用户身份")
    public R listIdentutyByUser(@RequestHeader("dcm_headers") TenantIsolation tenantIsolation, @Nullable @RequestParam Integer init) {
        return R.result(service.listIdentutyByUser(tenantIsolation, init));
    }


    @PostMapping("initTenant")
    @Operation(summary = "初始化租户信息")
    public R initTenant() {
        return R.result(service.initTenant(TenantIsolationThreadLocal.getTenantId()));
    }

    @GetMapping("/page-all-tenant")
    @Operation(summary = "查询所有租户")
    public com.nti56.common.util.R<Page<SysClientDto>> pageAllTenant(
            String searchStr,
            PageParam pageParam,
            @RequestHeader("dcm_headers") TenantIsolation tenantIsolation
    ) {
        return com.nti56.common.util.R.result(service.pageAllTenant(tenantIsolation.getTenantId(), searchStr, pageParam));
    }

    @PostMapping("/batch-set-id-type")
    @Operation(summary = "设置租户身份")
    public R batchSetIdType(
            @RequestHeader("dcm_headers") TenantIsolation tenantIsolation,
            @RequestBody BatchSetIdTypeDto dto
    ) {
        List<Integer> idTypes = dto.getIdTypes();
        List<Long> tenantIds = dto.getTenantIds();
        return R.result(service.batchSetIdType(tenantIsolation, idTypes, tenantIds));
    }


    @Autowired
    UserCenterService userCenterService;

    @GetMapping("/test/{id}")
    @Operation(summary = "test")
    public R getDetail(@PathVariable("id") Long id) {
        return R.ok(userCenterService.getOrgNameByTenantId(id));
    }

}
