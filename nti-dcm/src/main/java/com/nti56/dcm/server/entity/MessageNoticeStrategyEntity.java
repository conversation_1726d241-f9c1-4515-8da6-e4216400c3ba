package com.nti56.dcm.server.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 消息通知策略表 实体类
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @Ddate 2024-06-24 14:01:33
 * @since JDK 1.8
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@TableName("message_notice_strategy")
public class MessageNoticeStrategyEntity implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
    * id
    */
    private Long id;

    /**
    * 租户id
    */
    @TableField(fill = FieldFill.INSERT)
    private Long tenantId;

    /**
    * 策略名称
    */
    private String strategyName;

    /**
    * 通知类型（多选）, 1-设备任务，2-设备空闲，3-设备故障，4-设备离线
    */
    private String noticeType;

    /**
    * 是否启用, 0-禁用，1-启用
    */
    private Integer status;
    /**
    * 覆盖全部设备,0-否,1-是
    */
    private Integer overrideAllDevice;

    /**
    * idType
    */
    private Integer idType;

    /**
    * 版本号
    */
    @Version
    private Integer version;

    /**
    * 删除
    */
    @TableLogic
    private Integer deleted;

    /**
    * 创建人ID
    */
    @TableField(fill = FieldFill.INSERT)
    private Long creatorId;

    /**
    * 创建人
    */
    @TableField(fill = FieldFill.INSERT)
    private String creator;

    /**
    * 创建时间
    */
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
    * 更新人ID
    */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Long updatorId;

    /**
    * 更新人
    */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private String updator;

    /**
    * 更新时间
    */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    /**
    * 工程ID
    */
    @TableField(fill = FieldFill.INSERT)
    private Long engineeringId;

    /**
    * 模块ID
    */
    @TableField(fill = FieldFill.INSERT)
    private Long moduleId;

    /**
    * 空间ID
    */
    @TableField(fill = FieldFill.INSERT)
    private Long spaceId;



}
