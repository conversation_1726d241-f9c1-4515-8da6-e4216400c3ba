package com.nti56.dcm.server.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.time.LocalDateTime;
import lombok.Data;

/**
 * <p>
 * 工单人员工作量表 实体类
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @Ddate 2024-06-17 09:27:30
 * @since JDK 1.8
 */
@Data
@TableName("workload")
public class WorkloadEntity extends BaseEntity{
        /**
        * 工单类型，1-维修，2-保养，3-巡检
        */
        private Integer orderType;

        /**
        * 所属工单id
        */
        private Long orderId;

        /**
        * 人员id
        */
        private Long userId;

        /**
        * 人员名称
        */
        private String userName;

        /**
        * 开始时间
        */
        private LocalDateTime beginTime;

        /**
        * 结束时间
        */
        private LocalDateTime endTime;

        /**
        * 耗时，分钟
        */
        private Integer costTime;

        /**
        * 实际耗时，分钟
        */
        private Integer realCostTime;



}
