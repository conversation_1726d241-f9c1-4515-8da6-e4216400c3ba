package com.nti56.dcm.server.controller;

import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 类说明: <br/>
 *
 * <AUTHOR> <br/>
 * @version 1.0
 * @date 2024/7/8 17:28<br/>
 * @since JDK 1.8
 */
@RestController
@RequestMapping("mqtt")
public class MqttController {

//    @Autowired
//    private MqttConfig mqttConfig;
//
//    @GetMapping("connect")
//    public String connect() {
//        mqttConfig.conenct();
//        return "连接成功";
//    }
}
