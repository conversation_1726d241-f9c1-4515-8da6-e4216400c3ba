package com.nti56.dcm.server.config;

import com.baomidou.mybatisplus.annotation.DbType;
import com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor;
import com.baomidou.mybatisplus.extension.plugins.handler.DataPermissionHandler;
import com.baomidou.mybatisplus.extension.plugins.inner.OptimisticLockerInnerInterceptor;
import com.baomidou.mybatisplus.extension.plugins.inner.PaginationInnerInterceptor;

// import com.nti56.basic.injector.CustomSqlInjector;
//import com.nti56.common.mybatis.MySqlInjector;
import com.nti56.dcm.server.interceptor.DataPermissionHandlerImpl;
import com.nti56.dcm.server.interceptor.DataPermissionInterceptorPlus;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;


/**
 * 类说明: 
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2023-11-08 10:56:35
 * @since JDK 1.8
 */
@Configuration
public class MyBatisConfig {

    @Bean
    public DataPermissionInterceptorPlus dataPermissionInterceptorPlus() {
        return new DataPermissionInterceptorPlus(new DataPermissionHandlerImpl());
    }
    @Bean
    public MybatisPlusInterceptor mybatisPlusInterceptor() {
        MybatisPlusInterceptor interceptor = new MybatisPlusInterceptor();
        //mybatis分页插件
        interceptor.addInnerInterceptor(dataPermissionInterceptorPlus());
        interceptor.addInnerInterceptor(new PaginationInnerInterceptor(DbType.MYSQL));
//         添加乐观锁拦截器 mybatis-plus @version 控制乐观锁,自行开启
        interceptor.addInnerInterceptor(new OptimisticLockerInnerInterceptor());
        return interceptor;
    }

//    @Bean
//    public CustomSqlInjector sqlInjector() {
//        return new CustomSqlInjector();
//    }


}
