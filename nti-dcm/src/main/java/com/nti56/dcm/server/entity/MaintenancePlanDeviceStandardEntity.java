package com.nti56.dcm.server.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 保养计划设备标准表 实体类
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @Ddate 2024-04-08 17:55:21
 * @since JDK 1.8
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@TableName("maintenance_plan_device_standard")
public class MaintenancePlanDeviceStandardEntity implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
    * id
    */
    private Long id;

    /**
    * 保养计划设备ID
    */
    private Long maintenancePlanDeviceId;

    /**
    * 保养标准ID
    */
    private Long maintenanceStandardId;

    /**
    * 创建人
    */
    @TableField(fill = FieldFill.INSERT)
    private String creator;

    /**
    * 创建时间
    */
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
    * 修改人
    */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private String updator;

    /**
    * 修改时间
    */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    /**
    * 租户ID
    */
    @TableField(fill = FieldFill.INSERT)
    private Long tenantId;

    /**
    * 工程ID
    */
    @TableField(fill = FieldFill.INSERT)
    private Long engineeringId;

    /**
    * 模块ID
    */
    @TableField(fill = FieldFill.INSERT)
    private Long moduleId;

    /**
    * 空间ID
    */
    @TableField(fill = FieldFill.INSERT)
    private Long spaceId;

    /**
    * 版本号
    */
    @Version
    private Integer version;

    /**
    * 删除
    */
    @TableLogic
    private Integer deleted;



}
