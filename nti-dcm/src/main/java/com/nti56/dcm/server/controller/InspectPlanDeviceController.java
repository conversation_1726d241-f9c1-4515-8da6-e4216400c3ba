package com.nti56.dcm.server.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.nti56.nlink.common.dto.TenantIsolation;
import com.nti56.nlink.common.util.BeanUtilsIntensifier;
import com.nti56.nlink.common.util.PageParam;
import com.nti56.common.util.R;
import com.nti56.nlink.common.util.Result;
import com.nti56.nlink.common.util.ServiceCodeEnum;
import com.nti56.dcm.server.service.InspectPlanDeviceService;
import com.nti56.dcm.server.entity.InspectPlanDeviceEntity;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Optional;
import java.util.function.Function;

import lombok.extern.slf4j.Slf4j;


/**
 * 点巡检计划设备表 控制器
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2024-02-26 16:54:02
 * @since JDK 1.8
 */
@RestController
@RequestMapping("inspectPlanDevice")
@Slf4j
public class InspectPlanDeviceController {

    @Autowired
    private InspectPlanDeviceService service;

    /**
     * 获取分页
     * @param pageParam 分页参数
     */
    @GetMapping("/page")
    public R<Page<InspectPlanDeviceEntity>> page(@RequestHeader("dcm_headers") TenantIsolation tenant, PageParam pageParam,InspectPlanDeviceEntity entity){
        Page<InspectPlanDeviceEntity> page = pageParam.toPage(InspectPlanDeviceEntity.class);
        Result<Page<InspectPlanDeviceEntity>> result = service.getPage(tenant.getTenantId(), entity,page);
        return R.result(result);
    }

    /**
     * 获取列表
     */
    @GetMapping("/list")
    public R<List<InspectPlanDeviceEntity>> list(@RequestHeader("dcm_headers") TenantIsolation tenant, InspectPlanDeviceEntity entity){
        Result<List<InspectPlanDeviceEntity>> result = service.list(tenant.getTenantId(), entity);
        return R.result(result);
    }

    /**
     * 创建对象
     */
    @PostMapping("/create")
    public R<InspectPlanDeviceEntity> create(@RequestHeader("dcm_headers") TenantIsolation tenant, @RequestBody InspectPlanDeviceEntity entity){
        Result<InspectPlanDeviceEntity> result = service.save(tenant.getTenantId(), entity);
        return R.result(result);
    }

    /**
     * 更新对象
     */
    @PutMapping("/update")
    public R<Void> update(@RequestHeader("dcm_headers") TenantIsolation tenant, @RequestBody InspectPlanDeviceEntity entity){
        if (BeanUtilsIntensifier.checkBeanAndProperties(entity, InspectPlanDeviceEntity::getId)) {
            return R.error(ServiceCodeEnum.CODE_PARAM_ERROR.getCode());
        }
        Result<Void> result = service.update(tenant.getTenantId(), entity);
        return R.result(result);
    }

    /**
     * 删除对象
     * @param entityId 对象id
     */
    @DeleteMapping("/{entityId}")
    public R<Void> delete(@RequestHeader("dcm_headers") TenantIsolation tenant, @PathVariable Long entityId){
        Result<Void> result = service.deleteById(tenant.getTenantId(), entityId);
        return R.result(result);
    }

    /**
     * 获取对象
     * @param entityId 对象id
     */
    @GetMapping("/{entityId}")
    public R<InspectPlanDeviceEntity> get(@RequestHeader("dcm_headers") TenantIsolation tenant, @PathVariable Long entityId){
        Result<InspectPlanDeviceEntity> result = service.getById(tenant.getTenantId(), entityId);
        return R.result(result);
    }
    
}
