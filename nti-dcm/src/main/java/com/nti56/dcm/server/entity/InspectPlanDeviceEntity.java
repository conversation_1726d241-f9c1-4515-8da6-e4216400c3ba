package com.nti56.dcm.server.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.util.List;

import lombok.Data;

/**
 * <p>
 * 点巡检计划设备表 实体类
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @Ddate 2024-02-23 15:13:54
 * @since JDK 1.8
 */
@Data
@TableName("inspect_plan_device")
public class InspectPlanDeviceEntity extends BaseEntity{
        
    /**
    * 点巡检计划id
    */
    private Long inspectPlanId;

    /**
    * 设备id
    */
    private Long deviceId;

}
