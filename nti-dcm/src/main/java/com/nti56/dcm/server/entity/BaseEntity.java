package com.nti56.dcm.server.entity;

import java.time.LocalDateTime;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/***
 * 类说明: 基础类
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2023-11-06 09:40:34
 * @since JDK 1.8
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class BaseEntity {
    /**
     *id
    */
    private Long id;
    /**
     *租户id
    */
    @TableField(fill = FieldFill.INSERT)
    private Long tenantId;
    /**
     *版本号
    */
    private Integer version;
    /**
     *删除
    */
    private Integer deleted;
    /**
     *创建人ID
    */
    @TableField(fill = FieldFill.INSERT)
    private Long creatorId;
    /**
     *创建人
    */
    @TableField(fill = FieldFill.INSERT)
    private String creator;
    /**
     *创建时间
    */
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;
    /**
     *更新人ID
    */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Long updatorId;
    /**
     *更新人
    */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private String updator;
    /**
     *更新时间
    */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;
    /**
     *工程ID
    */
    @TableField(fill = FieldFill.INSERT)
    private Long engineeringId;
    /**
     *模块ID
    */
    @TableField(fill = FieldFill.INSERT)
    private Long moduleId;
    /**
     *空间ID
    */
    @TableField(fill = FieldFill.INSERT)
    private Long spaceId;
}
