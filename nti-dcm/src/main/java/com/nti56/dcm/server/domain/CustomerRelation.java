package com.nti56.dcm.server.domain;

import com.nti56.dcm.server.model.dto.CustomerRelationDto;
import com.nti56.nlink.common.util.Result;
import org.apache.commons.lang3.StringUtils;

import java.util.Objects;

/**
 * 类说明: <br/>
 *
 * <AUTHOR> <br/>
 * @version 1.0
 * @date 2024/3/7 14:48<br/>
 * @since JDK 1.8
 */
public class CustomerRelation {

    public static Result<Void> checkEdit(CustomerRelationDto customerRelationDto) {
        if (Objects.isNull(customerRelationDto.getId())) {
            return Result.error("id不能为空");
        }
        if (StringUtils.isNotEmpty(customerRelationDto.getDirector())) {
            if (customerRelationDto.getDirector().length() > 32) {
                return Result.error("负责人长度不能超过32个字符");
            }
        }
        if (StringUtils.isNotEmpty(customerRelationDto.getTel())) {
            if (customerRelationDto.getTel().length() > 20) {
                return Result.error("联系电话长度不能超过20个字符");
            }
        }
        return Result.ok();
    }
}
