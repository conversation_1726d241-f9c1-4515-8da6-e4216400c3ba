package com.nti56.dcm.server.domain.enums;

import lombok.Getter;

public enum StandardTypeEnum {
    COMMON(1, "common", "通用"),
    SPECIAL(2, "SPECIAL", "专用"),
    ;

    @Getter
    private Integer value;

    @Getter
    private String name;

    @Getter
    private String nameDesc;

    StandardTypeEnum(Integer value, String name, String nameDesc) {
        this.value = value;
        this.name = name;
        this.nameDesc = nameDesc;
    }

    public static StandardTypeEnum typeOfValue(Integer value){
        StandardTypeEnum[] values = StandardTypeEnum.values();
        for (StandardTypeEnum v : values) {
            if (v.value.equals(value)) {
                return v;
            }
        }
        return null;
    }

    public static StandardTypeEnum typeOfName(String name){
        StandardTypeEnum[] values = StandardTypeEnum.values();
        for (StandardTypeEnum v : values) {
            if (v.name.equals(name)) {
                return v;
            }
        }
        return null;
    }

    public static StandardTypeEnum typeOfNameDesc(String nameDesc){
        StandardTypeEnum[] values = StandardTypeEnum.values();
        for (StandardTypeEnum v : values) {
            if (v.nameDesc.equals(nameDesc)) {
                return v;
            }
        }
        return null;
    }
}
