package com.nti56.dcm.server.model.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;


@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder

// 记录类
public class DeviceRunningOtResponseRecordValueDto {
        private String result;
        private String table;
        private String deviceId;
        private String onlineTime;
        private String taskSpend;
        private String freeTime;
        private String faultTime;
        private String faultCount;
        private double useRate;
        private double faultRate;

        // setter 和 getter 方法
        public String getResult() {
            return result;
        }

        public void setResult(String result) {
            this.result = result;
        }

        public String getTable() {
            return table;
        }

        public void setTable(String table) {
            this.table = table;
        }

        public String getDeviceId() {
            return deviceId;
        }

        public void setDeviceId(String deviceId) {
            this.deviceId = deviceId;
        }

        public String getOnlineTime() {
            return onlineTime;
        }

        public void setOnlineTime(String onlineTime) {
            this.onlineTime = onlineTime;
        }

        public String getTaskSpend() {
            return taskSpend;
        }

        public void setTaskSpend(String taskSpend) {
            this.taskSpend = taskSpend;
        }

        public String getFreeTime() {
            return freeTime;
        }

        public void setFreeTime(String freeTime) {
            this.freeTime = freeTime;
        }

        public String getFaultTime() {
            return faultTime;
        }

        public void setFaultTime(String faultTime) {
            this.faultTime = faultTime;
        }

        public String getFaultCount() {
            return faultCount;
        }

        public void setFaultCount(String faultCount) {
            this.faultCount = faultCount;
        }

        public double getUseRate() {
            return useRate;
        }

        public void setUseRate(double useRate) {
            this.useRate = useRate;
        }

        public double getFaultRate() {
            return faultRate;
        }

        public void setFaultRate(double faultRate) {
            this.faultRate = faultRate;
        }
    }