package com.nti56.dcm.server.controller;

import com.nti56.dcm.server.entity.TimeoutConfigEntity;
import com.nti56.dcm.server.service.TimeoutConfigService;
import com.nti56.nlink.common.dto.TenantIsolation;
import com.nti56.nlink.common.util.*;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;


/**
 * <p>
 * 超时配置表 控制器
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @Ddate 2024-06-24 17:21:42
 * @since JDK 1.8
 */
@RestController
@RequestMapping("timeoutConfig")
@Tag(name = "超时配置模块")
public class TimeoutConfigController {

    @Autowired
    private TimeoutConfigService service;

    @GetMapping("list")
    @Operation(summary = "获取列表" )
    public R list(@RequestHeader("dcm_headers") TenantIsolation tenantIsolation, TimeoutConfigEntity entity){
        Result<List<TimeoutConfigEntity>> result = service.list(tenantIsolation, entity);
        return R.result(result);
    }

    @PutMapping("")
    @Operation(summary = "更新")
    public R update(@RequestHeader("dcm_headers") TenantIsolation tenantIsolation, @RequestBody TimeoutConfigEntity entity){
        Result<Void> result = service.update(entity);
        return R.result(result);
    }

    @GetMapping("/{entityId}")
    @Operation(summary = "获取对象")
    public R get(@RequestHeader("dcm_headers") TenantIsolation tenantIsolation, @PathVariable Long entityId){
        Result<TimeoutConfigEntity> result = service.getById(entityId);
        return R.result(result);
    }

    @PostMapping("batchSave")
    public R batchSave(@RequestHeader("dcm_headers") TenantIsolation tenantIsolation, @RequestBody List<TimeoutConfigEntity> timeoutConfigList) {
        return R.result(service.batchSave(tenantIsolation, timeoutConfigList));
    }

    @GetMapping("initSupplierTimeoutConfig")
    @Operation(summary = "初始化存量供应商超时配置")
    public R initSupplierTimeoutConfig() {
        return R.result(service.initSupplierTimeoutConfig());
    }

}
