package com.nti56.dcm.server.controller;

import cn.binarywang.wx.miniapp.bean.WxMaSubscribeMessage;
import com.nti56.common.util.R;
import com.nti56.dcm.server.model.dto.DeviceOrderQueryDto;
import com.nti56.dcm.server.model.result.ITResult;
import com.nti56.dcm.server.service.StatisticalReportService;
import com.nti56.dcm.server.service.WxService;
import com.nti56.nlink.common.dto.TenantIsolation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/stat")
@Tag(name = "统计报表")
public class StatisticalReportController {


    @Autowired
    private StatisticalReportService statisticalReportService;

    @PostMapping("getUserCostPage")
    public R getUserCostPage(@RequestHeader("dcm_headers") TenantIsolation tenantIsolation,@RequestBody DeviceOrderQueryDto queryDto) {
       return R.result(statisticalReportService.getUserCostPage(tenantIsolation,queryDto));

    }

    /**
     * 查询设备运行信息
     *
     * @param begin 开始时间，格式：2023-12-26T07:00:00
     * @param end 结束时间，格式：2023-12-26T07:00:00
     */
    @GetMapping("/getRunningInfo")
    public R<Object> getRunningInfo(
            @RequestHeader("dcm_headers") TenantIsolation tenant,
            @RequestParam(value="begin") String begin,
            @RequestParam(value="end") String end,
            @RequestParam(value="deviceTypeId",required = false) String deviceTypeId,
            @RequestParam(value="deviceNo",required = false) String deviceNo,
            @RequestParam(value="deviceName",required = false) String deviceName,
            @RequestParam(value="current",required = false) Integer current,
            @RequestParam(value="size",required = false) Integer size
    ){
        return R.result(statisticalReportService.getRunningInfo(tenant , begin, end,deviceTypeId,deviceNo,deviceName,size,current));
    }

}
