package com.nti56.dcm.server.entity;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 数据权限配置表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-31
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("data_permission")
@ApiModel(value = "DataPermission对象", description = "数据权限配置表")
public class DataPermissionEntity  implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     *id
     */
    private Long id;

    @ApiModelProperty(value = "身份类型：1-供应商，2-客户")
    private Integer idType;

    @ApiModelProperty(value = "角色id")
    private Long roleId;

    @ApiModelProperty(value = "客户id")
    private Long customerId;

    @TableField(exist = false)
    private String customerName;
    @ApiModelProperty(value = "设备id")
    private Long deviceId;

    @ApiModelProperty(value = "覆盖全部设备,0-否,1-是")
    private Integer overrideAllDevice;

    @TableField(exist = false)
    private String deviceCount;
    /**
     *租户id
     */
    @TableField(fill = FieldFill.INSERT)
    private Long tenantId;
    /**
     * 版本号
     */
    @Version
    private Integer version;

    /**
     * 删除
     */
    @TableLogic
    private Integer deleted;
    /**
     *创建人ID
     */
    @TableField(fill = FieldFill.INSERT)
    private Long creatorId;
    /**
     *创建人
     */
    @TableField(fill = FieldFill.INSERT)
    private String creator;
    /**
     *创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;
    /**
     *更新人ID
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Long updatorId;
    /**
     *更新人
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private String updator;
    /**
     *更新时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;
    /**
     *工程ID
     */
    @TableField(fill = FieldFill.INSERT)
    private Long engineeringId;
    /**
     *模块ID
     */
    @TableField(fill = FieldFill.INSERT)
    private Long moduleId;
    /**
     *空间ID
     */
    @TableField(fill = FieldFill.INSERT)
    private Long spaceId;

}
