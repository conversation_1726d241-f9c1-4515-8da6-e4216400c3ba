package com.nti56.dcm.server.domain;

import lombok.Data;

@Data
public class DeviceStateCount {
    public DeviceStateCount() {
        totalCount = 0;
        onlineCount = 0;
        offlineCount = 0;
        faultCount = 0;
        taskCount = 0;
        standByCount = 0;
    }

    public void addCount(DeviceStateCount vo) {
        totalCount += vo.getTotalCount();
        onlineCount += vo.getOnlineCount();
        offlineCount += vo.getOfflineCount();
        faultCount += vo.getFaultCount();
        taskCount += vo.getTaskCount();
        standByCount += vo.getStandByCount();
    }

    /**
     * 所有设备
     */
    private Integer totalCount;
    /**
     * 在线设备
     */
    private Integer onlineCount;
    /**
     * 离线设备
     */
    private Integer offlineCount;
    /**
     * 故障设备
     */
    private Integer faultCount;
    /**
     * 任务设备
     */
    private Integer taskCount;
    /**
     * 空闲设备
     */
    private Integer standByCount;

}
