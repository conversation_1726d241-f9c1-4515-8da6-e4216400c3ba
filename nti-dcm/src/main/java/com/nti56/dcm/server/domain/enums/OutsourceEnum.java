package com.nti56.dcm.server.domain.enums;

import lombok.Getter;

public enum OutsourceEnum {
    YES(1, "yes", "委外"),
    NO(0, "no", "不委外"),
    ;

    @Getter
    private Integer value;

    @Getter
    private String name;

    @Getter
    private String nameDesc;

    OutsourceEnum(Integer value, String name, String nameDesc) {
        this.value = value;
        this.name = name;
        this.nameDesc = nameDesc;
    }

    public static OutsourceEnum typeOfValue(Integer value){
        OutsourceEnum[] values = OutsourceEnum.values();
        for (OutsourceEnum v : values) {
            if (v.value.equals(value)) {
                return v;
            }
        }
        return null;
    }

    public static OutsourceEnum typeOfName(String name){
        OutsourceEnum[] values = OutsourceEnum.values();
        for (OutsourceEnum v : values) {
            if (v.name.equals(name)) {
                return v;
            }
        }
        return null;
    }

    public static OutsourceEnum typeOfNameDesc(String nameDesc){
        OutsourceEnum[] values = OutsourceEnum.values();
        for (OutsourceEnum v : values) {
            if (v.nameDesc.equals(nameDesc)) {
                return v;
            }
        }
        return null;
    }
}
