package com.nti56.dcm.server.domain.enums;

import lombok.Getter;

public enum ReportTypeEnum {
    DAY(1, "day", "日报"),
    WEEK(2, "week", "周报"),
    MONTH(3, "month", "月报"),
    ;

    @Getter
    private Integer value;

    @Getter
    private String name;

    @Getter
    private String nameDesc;

    ReportTypeEnum(Integer value, String name, String nameDesc) {
        this.value = value;
        this.name = name;
        this.nameDesc = nameDesc;
    }

    public static ReportTypeEnum typeOfValue(Integer value){
        ReportTypeEnum[] values = ReportTypeEnum.values();
        for (ReportTypeEnum v : values) {
            if (v.value.equals(value)) {
                return v;
            }
        }
        return null;
    }

    public static ReportTypeEnum typeOfName(String name){
        ReportTypeEnum[] values = ReportTypeEnum.values();
        for (ReportTypeEnum v : values) {
            if (v.name.equals(name)) {
                return v;
            }
        }
        return null;
    }

    public static ReportTypeEnum typeOfNameDesc(String nameDesc){
        ReportTypeEnum[] values = ReportTypeEnum.values();
        for (ReportTypeEnum v : values) {
            if (v.nameDesc.equals(nameDesc)) {
                return v;
            }
        }
        return null;
    }
}
