package com.nti56.dcm.server.constant;

public class DictConstant {
    /**
     * 故障等级-轻微故障
     */
    public static final Integer FAULT_LEVEL_MINOR = 0;
    /**
     * 故障等级-中等故障
     */
    public static final Integer FAULT_LEVEL_MEDIUM = 1;
    /**
     * 故障等级-严重故障
     */
    public static final Integer FAULT_LEVEL_SEVERE = 2;

    /**
     * 是否委外-未委外
     */
    public static final Integer NOT_OUTSOURCE = 0;
    /**
     * 是否委外-委外
     */
    public static final Integer HAS_OUTSOURCE = 1;

    /**
     * 验收未通过
     */
    public static final Integer ACCEPT_NOT_PASS = 0;
    /**
     * 验收通过
     */
    public static final Integer ACCEPT_PASS = 1;
    /**
     * 存入经验库
     */
    public static final Integer REPAIR_SAVE_EXP = 1;
    /**
     * 不存入经验库
     */
    public static final Integer REPAIR_NOT_SAVE_EXP = 0;
    /**
     * idType 客户身份
     */
    public static final Integer ID_TYPE_CUSTOMER = 2;
    /**
     * idType 供应商身份
     */
    public static final Integer ID_TYPE_SUPPLIER = 1;
    /**
     * 查询全部维修工单
     */
    public static final String QUERY_TYPE_ALL = "all";
    /**
     * 查询与我相关
     */
    public static final String QUERY_TYPE_ABOUT_ME = "withMe";
    /**
     * 查询待处理的
     */
    public static final String QUERY_TYPE_WAIT_HANDLE = "waitHandle";
    /**
     * 查询设备台账
     */
    public static final String QUERY_TYPE_DEVICE = "device";

    /**
     * 是否结束-否
     */
    public static final Integer NOT_STOP = 0;
    /**
     * 是否结束-是
     */
    public static final Integer HAS_STOP = 1;

    /**
     * 设备类型界面查询
     */
    public static final Integer DEVICE_TYPE_SELF = 1;
    /**
     * 按类型导出设备
     */
    public static final String EXPORT_WITH_TYPE = "0";
    /**
     * 导出设备
     */
    public static final String EXPORT_BATCH = "1";


    /**
     * 报修信息状态-待处理
     */
    public static final Integer REPORT_REPAIR_INIT = 0;
    /**
     * 报修信息状态-已派单
     */
    public static final Integer REPORT_REPAIR_DISPATCH = 1;
    /**
     * 报修信息状态-已关闭
     */
    public static final Integer REPORT_REPAIR_CANCEL = 9;
    /**
     * 响应状态-正常
     */
    public static final Integer RESPONSE_NORMAL = 0;
    /**
     * 响应状态-响应超时
     */
    public static final Integer RESPONSE_TIMEOUT = 1;
    /**
     * 今天国际的租户id
     */
    public static final Long DEFAULT_TENANT_ID = 1010203040506070809L;
    /**
     * 今天国际的租户id
     */
    public static final String  FULL_DEFAULT_TENANT_NAME = "深圳市今天国际物流技术股份有限公司";
    /**
     * 默认今天国际集成商pmo导入来的设备类型父级 内部数据 的设备类型id
     */
    public static final Long DEFAULT_DEVICE_TYPE_ID = 1409992749555712L;
    /**
     * 默认今天国际集成商pmo导入来的存放空间父级 内部数据 的存放空间id
     */
    public static final Long DEFAULT_DEVICE_LOCATION_ID = 1409996919734272L;
    /**
     * 设备来自于租户自身创建
     */
    public static final Integer NOT_FROM_PMO = 0;
    /**
     * 设备来自于pmo
     */
    public static final Integer IS_PMO = 1;

}
