package com.nti56.dcm.server.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import lombok.Data;

/**
 * <p>
 * 用户角色关系表 实体类
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @Ddate 2024-02-23 15:13:54
 * @since JDK 1.8
 */
@Data
@TableName("user_role")
public class UserRoleEntity extends BaseEntity{
        /**
        * 用户id
        */
        private Long userId;

        /**
        * 角色id
        */
        private Long roleId;



}
