package com.nti56.dcm.server.domain.enums;

import lombok.Getter;

public enum TimeoutEnum {
    NORMAL(1, "NORMAL", "正常"),
    TIMEOUT(2, "TIMEOUT", "超时"),
    ;

    @Getter
    private Integer value;

    @Getter
    private String name;

    @Getter
    private String nameDesc;

    TimeoutEnum(Integer value, String name, String nameDesc) {
        this.value = value;
        this.name = name;
        this.nameDesc = nameDesc;
    }

    public static TimeoutEnum typeOfValue(Integer value){
        TimeoutEnum[] values = TimeoutEnum.values();
        for (TimeoutEnum v : values) {
            if (v.value.equals(value)) {
                return v;
            }
        }
        return null;
    }

    public static TimeoutEnum typeOfName(String name){
        TimeoutEnum[] values = TimeoutEnum.values();
        for (TimeoutEnum v : values) {
            if (v.name.equals(name)) {
                return v;
            }
        }
        return null;
    }

    public static TimeoutEnum typeOfNameDesc(String nameDesc){
        TimeoutEnum[] values = TimeoutEnum.values();
        for (TimeoutEnum v : values) {
            if (v.nameDesc.equals(nameDesc)) {
                return v;
            }
        }
        return null;
    }
}
