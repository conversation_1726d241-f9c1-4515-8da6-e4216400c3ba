package com.nti56.dcm.server.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.nti56.nlink.common.dto.TenantIsolation;
import com.nti56.nlink.common.util.BeanUtilsIntensifier;
import com.nti56.nlink.common.util.PageParam;
import com.nti56.common.util.R;
import com.nti56.nlink.common.util.Result;
import com.nti56.nlink.common.util.ServiceCodeEnum;
import com.nti56.dcm.server.service.UserRoleService;
import com.nti56.dcm.server.entity.UserRoleEntity;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Optional;
import java.util.function.Function;

import lombok.extern.slf4j.Slf4j;


/**
 * 用户角色关系表 控制器
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2024-02-26 16:54:02
 * @since JDK 1.8
 */
@RestController
@RequestMapping("userRole")
@Slf4j
public class UserRoleController {

    @Autowired
    private UserRoleService service;

    /**
     * 根据角色查询用户
     */
    @GetMapping("/listByRoleId/{roleId}")
    public R<List<UserRoleEntity>> listByRoleId(
        @RequestHeader("dcm_headers") TenantIsolation tenant, 
        @PathVariable("roleId") Long roleId
    ){
        Result<List<UserRoleEntity>> result = service.listByRoleId(tenant.getTenantId(), roleId);
        return R.result(result);
    }

    /**
     * 获取列表
     */
    @GetMapping("/list")
    public R<List<UserRoleEntity>> list(@RequestHeader("dcm_headers") TenantIsolation tenant, UserRoleEntity entity){
        Result<List<UserRoleEntity>> result = service.list(tenant.getTenantId(), entity);
        return R.result(result);
    }

    /**
     * 获取对象
     * @param entityId 对象id
     */
    @GetMapping("/{entityId}")
    public R<UserRoleEntity> get(@RequestHeader("dcm_headers") TenantIsolation tenant, @PathVariable Long entityId){
        Result<UserRoleEntity> result = service.getById(tenant.getTenantId(), entityId);
        return R.result(result);
    }
    
    // /**
    //  * 获取分页
    //  * @param pageParam 分页参数
    //  */
    // @GetMapping("/page")
    // public R<Page<UserRoleEntity>> page(@RequestHeader("dcm_headers") TenantIsolation tenant, PageParam pageParam,UserRoleEntity entity){
    //     Page<UserRoleEntity> page = pageParam.toPage(UserRoleEntity.class);
    //     Result<Page<UserRoleEntity>> result = service.getPage(tenant.getTenantId(), entity,page);
    //     return R.result(result);
    // }


    // /**
    //  * 创建对象
    //  */
    // @PostMapping("/create")
    // public R<UserRoleEntity> create(@RequestHeader("dcm_headers") TenantIsolation tenant, @RequestBody UserRoleEntity entity){
    //     Result<UserRoleEntity> result = service.save(tenant.getTenantId(), entity);
    //     return R.result(result);
    // }


    // /**
    //  * 更新对象
    //  */
    // @PutMapping("/update")
    // public R<Void> update(@RequestHeader("dcm_headers") TenantIsolation tenant, @RequestBody UserRoleEntity entity){
    //     if (BeanUtilsIntensifier.checkBeanAndProperties(entity, UserRoleEntity::getId)) {
    //         return R.error(ServiceCodeEnum.CODE_PARAM_ERROR.getCode());
    //     }
    //     Result<Void> result = service.update(tenant.getTenantId(), entity);
    //     return R.result(result);
    // }

    // /**
    //  * 删除对象
    //  * @param entityId 对象id
    //  */
    // @DeleteMapping("/{entityId}")
    // public R<Void> delete(@RequestHeader("dcm_headers") TenantIsolation tenant, @PathVariable Long entityId){
    //     Result<Void> result = service.deleteById(tenant.getTenantId(), entityId);
    //     return R.result(result);
    // }
    
}
