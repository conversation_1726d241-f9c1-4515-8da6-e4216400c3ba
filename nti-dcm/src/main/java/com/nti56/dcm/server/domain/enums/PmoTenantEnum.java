package com.nti56.dcm.server.domain.enums;

import lombok.Getter;

public enum PmoTenantEnum {
    IS_PMO_TENANT(1, "isPmoTenant", "是"),
    NOT_PMO_TENANT(0, "notPmoTenant", "否"),
    ;

    @Getter
    private Integer value;

    @Getter
    private String name;

    @Getter
    private String nameDesc;

    PmoTenantEnum(Integer value, String name, String nameDesc) {
        this.value = value;
        this.name = name;
        this.nameDesc = nameDesc;
    }

    public static PmoTenantEnum typeOfValue(Integer value){
        PmoTenantEnum[] values = PmoTenantEnum.values();
        for (PmoTenantEnum v : values) {
            if (v.value.equals(value)) {
                return v;
            }
        }
        return null;
    }

    public static PmoTenantEnum typeOfName(String name){
        PmoTenantEnum[] values = PmoTenantEnum.values();
        for (PmoTenantEnum v : values) {
            if (v.name.equals(name)) {
                return v;
            }
        }
        return null;
    }

    public static PmoTenantEnum typeOfNameDesc(String nameDesc){
        PmoTenantEnum[] values = PmoTenantEnum.values();
        for (PmoTenantEnum v : values) {
            if (v.nameDesc.equals(nameDesc)) {
                return v;
            }
        }
        return null;
    }
}
