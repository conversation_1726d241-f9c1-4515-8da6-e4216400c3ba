package com.nti56.dcm.server.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.nti56.nlink.common.dto.TenantIsolation;
import com.nti56.nlink.common.util.BeanUtilsIntensifier;
import com.nti56.nlink.common.util.PageParam;
import com.nti56.common.util.R;
import com.nti56.nlink.common.util.Result;
import com.nti56.nlink.common.util.ServiceCodeEnum;
import com.nti56.dcm.server.service.InspectResultItemService;
import com.nti56.dcm.server.entity.InspectResultItemEntity;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

import lombok.extern.slf4j.Slf4j;


/**
 * 点巡检结果项目表 控制器
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2024-02-26 16:54:02
 * @since JDK 1.8
 */
@RestController
@RequestMapping("inspectResultItem")
@Slf4j
public class InspectResultItemController {

    @Autowired
    private InspectResultItemService service;

    /**
     * 获取分页
     * @param pageParam 分页参数
     */
    @GetMapping("/page")
    public R<Page<InspectResultItemEntity>> page(@RequestHeader("dcm_headers") TenantIsolation tenant, PageParam pageParam,InspectResultItemEntity entity){
        Page<InspectResultItemEntity> page = pageParam.toPage(InspectResultItemEntity.class);
        Result<Page<InspectResultItemEntity>> result = service.getPage(tenant.getTenantId(), entity,page);
        return R.result(result);
    }

    /**
     * 获取列表
     */
    @GetMapping("/list")
    public R<List<InspectResultItemEntity>> list(@RequestHeader("dcm_headers") TenantIsolation tenant, InspectResultItemEntity entity){
        Result<List<InspectResultItemEntity>> result = service.list(tenant.getTenantId(), entity);
        return R.result(result);
    }

    /**
     * 创建对象
     */
    @PostMapping("/create")
    public R<InspectResultItemEntity> create(@RequestHeader("dcm_headers") TenantIsolation tenant, @RequestBody InspectResultItemEntity entity){
        Result<InspectResultItemEntity> result = service.save(tenant.getTenantId(), entity);
        return R.result(result);
    }

    /**
     * 更新对象
     */
    @PutMapping("/update")
    public R<Void> update(@RequestHeader("dcm_headers") TenantIsolation tenant, @RequestBody InspectResultItemEntity entity){
        if (BeanUtilsIntensifier.checkBeanAndProperties(entity, InspectResultItemEntity::getId)) {
            return R.error(ServiceCodeEnum.CODE_PARAM_ERROR.getCode());
        }
        Result<Void> result = service.update(tenant.getTenantId(), entity);
        return R.result(result);
    }

    /**
     * 删除对象
     * @param entityId 对象id
     */
    @DeleteMapping("/{entityId}")
    public R<Void> delete(@RequestHeader("dcm_headers") TenantIsolation tenant, @PathVariable Long entityId){
        Result<Void> result = service.deleteById(tenant.getTenantId(), entityId);
        return R.result(result);
    }

    /**
     * 获取对象
     * @param entityId 对象id
     */
    @GetMapping("/{entityId}")
    public R<InspectResultItemEntity> get(@RequestHeader("dcm_headers") TenantIsolation tenant, @PathVariable Long entityId){
        Result<InspectResultItemEntity> result = service.getById(tenant.getTenantId(), entityId);
        return R.result(result);
    }
    
}
