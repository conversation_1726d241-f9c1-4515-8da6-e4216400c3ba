package com.nti56.dcm.server.domain.enums;

import lombok.Getter;

public enum PeriodInspectTypeEnum {
    DAY(1, "day", "每日"),
    WEEK(2, "week", "每周"),
    MONTH(3, "month", "每月"),
    ;

    @Getter
    private Integer value;

    @Getter
    private String name;

    @Getter
    private String nameDesc;

    PeriodInspectTypeEnum(Integer value, String name, String nameDesc) {
        this.value = value;
        this.name = name;
        this.nameDesc = nameDesc;
    }

    public static PeriodInspectTypeEnum typeOfValue(Integer value){
        PeriodInspectTypeEnum[] values = PeriodInspectTypeEnum.values();
        for (PeriodInspectTypeEnum v : values) {
            if (v.value.equals(value)) {
                return v;
            }
        }
        return null;
    }

    public static PeriodInspectTypeEnum typeOfName(String name){
        PeriodInspectTypeEnum[] values = PeriodInspectTypeEnum.values();
        for (PeriodInspectTypeEnum v : values) {
            if (v.name.equals(name)) {
                return v;
            }
        }
        return null;
    }

    public static PeriodInspectTypeEnum typeOfNameDesc(String nameDesc){
        PeriodInspectTypeEnum[] values = PeriodInspectTypeEnum.values();
        for (PeriodInspectTypeEnum v : values) {
            if (v.nameDesc.equals(nameDesc)) {
                return v;
            }
        }
        return null;
    }
}
