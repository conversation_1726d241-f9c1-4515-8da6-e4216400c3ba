package com.nti56.dcm.server.domain.enums;

import lombok.Getter;

public enum DeviceConnectPlanStatusEnum {
    UN_FINISH(0, "UN_FINISH", "接入中"),
    FINISH(1, "FINISH", "已完成"),
    ;

    @Getter
    private Integer value;

    @Getter
    private String name;

    @Getter
    private String nameDesc;

    DeviceConnectPlanStatusEnum(Integer value, String name, String nameDesc) {
        this.value = value;
        this.name = name;
        this.nameDesc = nameDesc;
    }

    public static DeviceConnectPlanStatusEnum typeOfValue(Integer value){
        DeviceConnectPlanStatusEnum[] values = DeviceConnectPlanStatusEnum.values();
        for (DeviceConnectPlanStatusEnum v : values) {
            if (v.value.equals(value)) {
                return v;
            }
        }
        return null;
    }

    public static DeviceConnectPlanStatusEnum typeOfName(String name){
        DeviceConnectPlanStatusEnum[] values = DeviceConnectPlanStatusEnum.values();
        for (DeviceConnectPlanStatusEnum v : values) {
            if (v.name.equals(name)) {
                return v;
            }
        }
        return null;
    }

    public static DeviceConnectPlanStatusEnum typeOfNameDesc(String nameDesc){
        DeviceConnectPlanStatusEnum[] values = DeviceConnectPlanStatusEnum.values();
        for (DeviceConnectPlanStatusEnum v : values) {
            if (v.nameDesc.equals(nameDesc)) {
                return v;
            }
        }
        return null;
    }
}
