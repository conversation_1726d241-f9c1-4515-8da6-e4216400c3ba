package com.nti56.dcm.server.domain;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.bean.copier.CopyOptions;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.nti56.dcm.server.entity.DeviceEntity;
import com.nti56.dcm.server.entity.DeviceTypeEntity;
import com.nti56.dcm.server.entity.DeviceTypeGroupEntity;
import com.nti56.dcm.server.model.dto.DeviceTypeDto;
import com.nti56.dcm.server.model.dto.DeviceTypeGroupDto;
import com.nti56.nlink.common.base.UniqueConstraint;
import com.nti56.nlink.common.fetcher.CommonFetcher;
import com.nti56.nlink.common.util.Result;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

@Slf4j
public class DeviceTypeGroup {

    @Getter
    private DeviceTypeGroupEntity entity;

    private DeviceTypeGroupEntity inheritGroup;


    private static final UniqueConstraint groupUniqueConstraint = new UniqueConstraint("group_name");

    public static Result<DeviceTypeGroupDto> getAllInfos(Long groupId, CommonFetcher commonFetcher) {

        if (Objects.isNull(groupId)) {
            return Result.error("类型分组ID为空！");
        }
        DeviceTypeGroupEntity deviceTypeGroupEntity = commonFetcher.get(groupId, DeviceTypeGroupEntity.class);
        if (Objects.isNull(deviceTypeGroupEntity)) {
            return Result.error("类型分组不存在！");
        }
        DeviceTypeGroupDto deviceTypeGroupDto = BeanUtil.copyProperties(deviceTypeGroupEntity, DeviceTypeGroupDto.class, "params");
        List<DeviceTypeDto> childrenTypeList = new ArrayList<>();
        List<DeviceTypeEntity> deviceTypeEntityList = commonFetcher.list("group_id", groupId, DeviceTypeEntity.class);

        deviceTypeEntityList.forEach(i ->
                {
                    Result<DeviceTypeDto> allInfos = DeviceType.getAllInfos(i.getId(), commonFetcher);
                    childrenTypeList.add(allInfos.getResult());
                }
        );
        deviceTypeGroupDto.setChildrenTypeList(childrenTypeList);

        return Result.ok(deviceTypeGroupDto);
    }


    public static List<DeviceTypeGroupEntity> getParentDeviceTypeGroupList(Long typeId, List<DeviceTypeGroupEntity> list, CommonFetcher commonFetcher) {
        DeviceTypeGroupEntity groupEntity = commonFetcher.get(typeId, DeviceTypeGroupEntity.class);
        if (!Objects.isNull(groupEntity)) {
            list.add(groupEntity);
        }
        Long parentId = Optional.ofNullable(groupEntity).map(DeviceTypeGroupEntity::getParentGroupId).orElse(null);
        if (!Objects.isNull(parentId)) {
            List<DeviceTypeGroupEntity> parentList = commonFetcher.list("id", parentId, DeviceTypeGroupEntity.class);
            for (DeviceTypeGroupEntity parent : parentList) {
                getParentDeviceTypeGroupList(parent.getId(), list, commonFetcher);
            }
        }
        return list;
    }


    public static Result<DeviceTypeGroup> checkCreate(DeviceTypeGroupDto dto, CommonFetcher commonFetcher) {
        if (StrUtil.isBlank(dto.getGroupName())) {
            return Result.error("设备类型分组名称不能为空！");
        }
        if (dto.getGroupName().contains("/")) {
            return Result.error("设备类型分组名称不能包含符号\"/\"！");
        }
        // 校验同级别下是否存在设备类型重名
        List<DeviceTypeGroupEntity> selfLevelTypes = commonFetcher.list("parent_group_id", dto.getParentGroupId(), DeviceTypeGroupEntity.class);
        if (selfLevelTypes.stream().filter(entity-> ObjectUtil.equals(entity.getIdType(), dto.getIdType())).map(i -> i.getGroupName()).collect(Collectors.toList()).contains(dto.getGroupName())) {
            return Result.error("同级别设备类型分组名称已存在：" + dto.getGroupName());
        }
        DeviceTypeGroupEntity entity = new DeviceTypeGroupEntity();
        BeanUtils.copyProperties(dto, entity);
        DeviceTypeGroup deviceType = new DeviceTypeGroup();
        deviceType.entity = entity;
        return Result.ok(deviceType);
    }


    public static Result<DeviceTypeGroup> checkUpdate(DeviceTypeGroupDto dto, CommonFetcher commonFetcher) {

        if (Objects.isNull(dto.getId())) {
            return Result.error("设备类型分组ID不能为空！");
        }
        if (dto.getGroupName().contains("/")) {
            return Result.error("设备类型分组名称不能包含符号\"/\"！");
        }

        DeviceTypeGroupEntity oldEntity = commonFetcher.get(dto.getId(), DeviceTypeGroupEntity.class);
        if (Objects.isNull(oldEntity)) {
            return Result.error("不存在的设备类型分组！");
        }
        // 校验同级别下是否存在设备类型重名
        List<DeviceTypeGroupEntity> selfLevelTypes = commonFetcher.list("parent_group_id", dto.getParentGroupId(), DeviceTypeGroupEntity.class);
        if (selfLevelTypes.stream().filter(i -> ObjectUtil.equals(i.getIdType(), dto.getIdType()) && !i.getId().equals(oldEntity.getId())).map(DeviceTypeGroupEntity::getGroupName).collect(Collectors.toList()).contains(dto.getGroupName())) {
            return Result.error("同级别设备类型分组名称已存在：" + dto.getGroupName());
        }
        DeviceTypeGroup deviceTypeGroup = new DeviceTypeGroup();
        BeanUtil.copyProperties(dto, oldEntity, CopyOptions.create().setIgnoreNullValue(false).setIgnoreProperties("tenantId"));
        deviceTypeGroup.entity = oldEntity;
        return Result.ok(deviceTypeGroup);
    }


    public static Result<Void> checkDelete(Long id, CommonFetcher commonFetcher) {

        DeviceTypeGroupEntity existType = commonFetcher.get(id, DeviceTypeGroupEntity.class);
        if (Objects.isNull(existType)) {
            return Result.error("设备类型分组不存在，无法删除！");
        }
        //检查继承关系
        List<DeviceTypeGroupEntity> inheritMe = commonFetcher.list("parent_group_id", id, DeviceTypeGroupEntity.class);
        if (CollectionUtil.isNotEmpty(inheritMe)) {
            return Result.error("设备类型分组存在子分组，无法删除！");
        }
        //检查是否存在设备类型
        List<DeviceTypeEntity> childrenTypeList = commonFetcher.list("group_id", id, DeviceTypeEntity.class);
        if (CollectionUtil.isNotEmpty(childrenTypeList)) {
            return Result.error("分组或子组中存在设备类型文件，无法删除");
        }
        return Result.ok();
    }


}
