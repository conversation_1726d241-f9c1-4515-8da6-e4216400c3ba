package com.nti56.dcm.server.controller;

import com.nti56.dcm.server.service.StockAlarmService;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/stockAlarm")
@Tag(name = "备件库存预警")
public class StockAlarmController {

    @Autowired
    private StockAlarmService stockAlarmService;

    @RequestMapping("/monitor")
    public String alarm() {
        stockAlarmService.stockAlarm();
        return "ok";
    }

}
