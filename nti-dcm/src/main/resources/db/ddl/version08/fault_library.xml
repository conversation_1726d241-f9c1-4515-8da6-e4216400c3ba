<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.8.xsd">
    
    <changeSet author="zjc" id="20250604161422">
        <sql>
             CREATE TABLE `fault_library` (
                 `id` bigint NOT NULL COMMENT 'id',
                 `tenant_id` bigint DEFAULT NULL COMMENT '租户id',
                 `id_type` tinyint DEFAULT NULL COMMENT '身份，1-供应商，2-客户',
                 `device_type_id` bigint DEFAULT NULL COMMENT '设备类型id',
                 `device_type_name` varchar(255) DEFAULT NULL COMMENT '设备类型名称',
                 `fault_code` varchar(64) NOT NULL COMMENT '故障代码',
                 `fault_desc` varchar(1024) DEFAULT NULL COMMENT '故障描述',
                 `solve_method` varchar(1024) DEFAULT NULL COMMENT '解决方法',
                 `prevent_strategy` varchar(1024) DEFAULT NULL COMMENT '预防策略',
                 `creator` varchar(255) DEFAULT NULL COMMENT '创建人',
                 `create_time` datetime DEFAULT NULL COMMENT '创建时间',
                 `updator` varchar(255) DEFAULT NULL COMMENT '修改人',
                 `update_time` datetime DEFAULT NULL COMMENT '修改时间',
                 `engineering_id` bigint DEFAULT NULL COMMENT '工程ID',
                 `module_id` bigint DEFAULT NULL COMMENT '模块ID',
                 `space_id` bigint DEFAULT NULL COMMENT '空间ID',
                 `version` int DEFAULT '1' COMMENT '版本号',
                 `deleted` int DEFAULT '0' COMMENT '删除',
                 PRIMARY KEY (`id`)
             ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='故障库表';
        </sql>
    </changeSet>

    <changeSet author="zjc" id="20250610161623">
        <sql>
            ALTER TABLE fault_library ADD COLUMN `fault_event` varchar(255) DEFAULT NULL COMMENT '故障事件名称' AFTER fault_code;
        </sql>
    </changeSet>

    <changeSet author="zjc" id="20250611161623">
        <sql>
            ALTER TABLE fault_library modify COLUMN `fault_code` varchar(64) DEFAULT NULL COMMENT '故障代码';
        </sql>
    </changeSet>

</databaseChangeLog>
