package com.nti56.dcm.server.domain;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.nti56.dcm.server.entity.DeviceAssetChangeTypeEntity;
import com.nti56.dcm.server.model.dto.DeviceAssetChangeTypeDto;
import com.nti56.nlink.common.base.FieldValue;
import com.nti56.nlink.common.base.UniqueConstraint;
import com.nti56.nlink.common.fetcher.CommonFetcher;
import com.nti56.nlink.common.util.Result;
import lombok.Getter;
import org.springframework.beans.BeanUtils;

import java.util.List;
import java.util.Objects;
import java.util.Optional;

public class DeviceAssetChangeType {

    @Getter
    private DeviceAssetChangeTypeEntity entity;



    private static final UniqueConstraint deviceAssetChangeTypeUniqueConstraint = new UniqueConstraint("change_type","id_type");

    public static Result<DeviceAssetChangeType> checkCreate(DeviceAssetChangeTypeDto dto, CommonFetcher commonFetcher) {
        if (StrUtil.isBlank(dto.getChangeType())) {
            return Result.error("设备资产变更类型名称不能为空");
        }

        // 校验同级别下是否存在设备设备资产变更类型重名
        DeviceAssetChangeTypeEntity repeat = commonFetcher.get(deviceAssetChangeTypeUniqueConstraint.buildUnique(new FieldValue(dto.getChangeType()),new FieldValue(dto.getIdType())),DeviceAssetChangeTypeEntity.class);
        if (repeat != null ){
            return Result.error("已存在设备资产变更类型：" + dto.getChangeType());
        }
        DeviceAssetChangeTypeEntity entity = new DeviceAssetChangeTypeEntity();
        BeanUtils.copyProperties(dto, entity);
        DeviceAssetChangeType deviceAssetChangeType = new DeviceAssetChangeType();
        deviceAssetChangeType.entity = entity;
        return Result.ok(deviceAssetChangeType);
    }


    public static Result<DeviceAssetChangeType> checkUpdate(DeviceAssetChangeTypeDto dto, CommonFetcher commonFetcher) {

        if (Objects.isNull(dto.getId())) {
            return Result.error("设备资产变更类型ID不能为空！");
        }

        if (StrUtil.isBlank(dto.getChangeType())) {
            return Result.error("设备资产变更类型名称不能为空！");
        }
        DeviceAssetChangeTypeEntity oldEntity = commonFetcher.get(dto.getId(), DeviceAssetChangeTypeEntity.class);
        if (Objects.isNull(oldEntity)) {
            return Result.error("不存在的设备资产变更类型！");
        }

        // 校验修改后同级别下是否存在设备设备资产变更类型重名
        DeviceAssetChangeTypeEntity repeat = commonFetcher.get(deviceAssetChangeTypeUniqueConstraint.buildUnique(new FieldValue(dto.getChangeType()),new FieldValue(dto.getIdType())),DeviceAssetChangeTypeEntity.class);
        if (repeat!=null && !repeat.getId().equals(dto.getId())){
            return Result.error("已存在设备资产变更类型：" + dto.getChangeType());
        }

        oldEntity.setChangeType(dto.getChangeType());
        DeviceAssetChangeType deviceAssetChangeType = new DeviceAssetChangeType();
        deviceAssetChangeType.entity = oldEntity;
        return Result.ok(deviceAssetChangeType);
    }

    public static Result<Void> checkDelete(Long id, CommonFetcher commonFetcher) {
        if (id == null) {
            //
            DeviceAssetChangeType rootDeviceLocation = new DeviceAssetChangeType();
            return Result.error("未传入id，无法删除！");
        }
        DeviceAssetChangeTypeEntity existLocation = commonFetcher.get(id, DeviceAssetChangeTypeEntity.class);
        if (Objects.isNull(existLocation)) {
            return Result.error("设备资产变更类型不存在，无法删除！");
        }
        return Result.ok();
    }



}
