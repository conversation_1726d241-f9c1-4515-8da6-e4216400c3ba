import "experimental/table"
import "date"
import "contrib/tomhollingworth/events"

queryBegin = time(v: {begin}+08:00)
queryEnd = time(v: {end}+08:00)
nowTime = now()
nowOrEnd = if nowTime < queryEnd then nowTime else queryEnd
nowOrBegin = if nowTime < queryBegin then nowTime else queryBegin

t0 =
from(bucket:"{influxBucket}")
|> range(start: 0, stop: queryBegin)
|> filter(fn: (r) => r["deviceId"] =~  /{deviceIds}/)
|> filter(fn: (r) => r["property"] == "State_OnLine" )
|> last(column: "_value")
|> map(fn: (r) => ({ r with _time: queryBegin  }))  

t1 =
from(bucket:"{influxBucket}")
|> range(start: queryBegin, stop: nowOrEnd)
|> filter(fn: (r) => r["deviceId"] =~ /{deviceIds}/)
|> filter(fn: (r) => r["property"] == "State_OnLine")
|> window(every: 1d, createEmpty: true)
|> table.fill()
|> fill(usePrevious: true)

union(tables: [t0, t1])|> group()
|> map (fn: (r) => ({ r with _time: if not(exists r._value) then r._start else r._time}))
|> sort(columns:["deviceId","_start"])
|> events.duration(unit: 1s,stop:nowOrEnd)
|> map (fn: (r) => ({ r with
       startTime: uint(v: r._start),
       stopTime: uint(v: r._stop),
       timeTime: uint(v: r._time),
       time_to_stop: (int(v: r._stop) - int(v: r._time))/1000/1000/1000 ,
       start_to_time: (int(v: r._time) - int(v: r._start))/1000/1000/1000,
       duration: if r.duration<0 then  (int(v: nowOrEnd) - int(v: r._time))/1000/1000/1000 else r.duration
        }))
|> fill(usePrevious: true)
|> filter(fn: (r) => r["_start"] > time(v: 0) and (r["_value"]==true or r["_value"]==false) )
|> keep(columns: ["_time", "_value","_start","_stop","duration","start_to_time","startTime","stopTime","time_to_stop","timeTime","deviceId"])
|> group(columns:["_start","deviceId"])
|> reduce( fn: (r, accumulator) => ( {
    start:r._start,
    preItem: r._value,
    deviceId: r.deviceId,
    spend:
        if r._value ==false and accumulator.preItem==false and r.start_to_time!=0 then accumulator.spend + r.start_to_time else
        if r._value ==false and accumulator.preItem==true and r.start_to_time!=0 then accumulator.spend else
        if r._value ==true  and r._time==r._start and r.start_to_time==0 then
               if  r.duration>r.time_to_stop  then accumulator.spend+r.time_to_stop else accumulator.spend+r.duration
        else
        if r._value ==true  and accumulator.preItem==false and r.start_to_time!=0  then
          if  r.duration>r.time_to_stop  then accumulator.spend+r.time_to_stop else accumulator.spend+r.duration
        else
          accumulator.spend
} ), identity: {preItem:false,spend: 0,start:time(v: 2025-06-03T14:53:39+08:00),deviceId:""})
|> group(columns:["_start"])
|> mean(column:"spend")
|> map(fn: (r) => ({r with _time: r._start,onlineSumSeconds:int(v:r.spend),onlineSumStr:string(v:duration(v:int(v:r.spend)*1000*1000*1000))}))
|> group()