package com.nti56.common.util;

import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.ObjUtil;
import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.ApplicationArguments;
import org.springframework.boot.ApplicationRunner;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.util.UriComponentsBuilder;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.Map;

@Component
@Slf4j
public class InitApplicationRunner implements ApplicationRunner {
    @Autowired
    @Qualifier("restTemplate")
    private RestTemplate restHttpTemplate;

    @Override
    public void run(ApplicationArguments args) throws Exception {
        try {
            log.info("进行法定节假日列表获取！");
            HolidayUtil.holidayList = new ArrayList<>();
            HolidayUtil.fixWorkList = new ArrayList<>();
            HttpHeaders headers = new HttpHeaders();
            headers.add("user-agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/54.0.2840.99 Safari/537.36");
            HttpEntity<String> httpEntity = new HttpEntity<String>(headers);
            String body = restHttpTemplate.exchange("http://timor.tech/api/holiday/year/", HttpMethod.GET,
                    httpEntity, String.class).getBody();
            Map res = JSONObject.parseObject(body, Map.class);
            Integer code = (Integer) res.get("code");
            if (code != 0) {
                return;
            }
            Map<String, Map<String, Object>> holidayMap = (Map<String, Map<String, Object>>) res.get("holiday");
            holidayMap.forEach((k, v) -> {
                Integer wage = (Integer) v.get("wage");
                if (!ObjUtil.equals(wage, 1)) {
                    HolidayUtil.holidayList.add((String) v.get("date"));
                } else {
                    HolidayUtil.fixWorkList.add((String) v.get("date"));
                }
            });
            return;
        } catch (Exception e) {
            log.error("通过外部接口获取年度节假日信息出现异常");
        }
        try {
            log.info("读取本地配置信息/config/hoolidayConfig.txt");
            // 使用类加载器获取资源文件的输入流
            String resourcePath = "config/holidayConfig.txt";
            // 使用 Hutool 的 FileUtil 读取输入流内容
            String body = FileUtil.readUtf8String(resourcePath);
            Map res = JSONObject.parseObject(body, Map.class);
            Integer code = (Integer) res.get("code");
            if (code != 0) {
                return;
            }
            Map<String, Map<String, Object>> holidayMap = (Map<String, Map<String, Object>>) res.get("holiday");
            holidayMap.forEach((k, v) -> {
                Integer wage = (Integer) v.get("wage");
                if (!ObjUtil.equals(wage, 1)) {
                    HolidayUtil.holidayList.add((String) v.get("date"));
                } else {
                    HolidayUtil.fixWorkList.add((String) v.get("date"));
                }
            });
        } catch (Exception e) {
            log.error("读取本地配置信息获取年度节假日信息出现异常,请前往 http://timor.tech/api/holiday/year/ 获取节假日信息数据，并设置到/config/holidayConfig.txt中！");
        }
    }
}
