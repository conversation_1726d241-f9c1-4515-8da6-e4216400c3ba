package com.nti56.dcm.server.controller;

import com.nti56.common.util.R;
import com.nti56.dcm.server.domain.enums.AcceptanceModeEnum;
import com.nti56.dcm.server.entity.OrderProcessConfigEntity;
import com.nti56.dcm.server.exception.BizException;
import com.nti56.dcm.server.model.dto.ResponsibleDto;
import com.nti56.dcm.server.service.OrderProcessConfigService;
import com.nti56.nlink.common.dto.TenantIsolation;
import com.nti56.nlink.common.util.Result;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.Objects;


/**
 * 工单流程配置 控制器
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2024-06-17 09:27:30
 * @since JDK 1.8
 */
@RestController
@RequestMapping("orderProcessConfig")
@Slf4j
public class OrderProcessConfigController {

    @Autowired
    private OrderProcessConfigService service;
    

    /**
     * 创建对象
     */
    @PostMapping("create")
    public R<OrderProcessConfigEntity> create(@RequestHeader("dcm_headers") TenantIsolation tenant, @RequestBody OrderProcessConfigEntity entity){
        checkConfig(entity);
        Result<OrderProcessConfigEntity> result = service.save(tenant.getTenantId(),tenant.getIdType(), entity);
        return R.result(result);
    }

    private void checkConfig(OrderProcessConfigEntity entity) {
        if (Objects.isNull(entity)) {
            throw new BizException("参数不能为空");
        }
        if (Objects.isNull(entity.getOrderType())) {
            throw new BizException("工单类型不能为空");
        }
        if (Objects.isNull(entity.getIsOutsourcing())) {
            throw new BizException("是否外委不能为空");
        }
        if (Objects.isNull(entity.getIsDataRecord())) {
            throw new BizException("是否数据型不能为空");
        }

        if (!entity.getIsAutoDispatch()&& (Objects.isNull(entity.getDispatchResponsible()) || entity.getDispatchResponsible().length == 0)) {
            throw new BizException("指定人员情况下，派工负责人不能为空");
        }
        for (ResponsibleDto user : entity.getDispatchResponsible()) {
            if (Objects.isNull(user.getId()) || Objects.isNull(user.getIsRole())) {
                throw new BizException("请确认派工负责人");
            }
        }

        if (Objects.isNull(entity.getAllowForward())) {
            throw new BizException("是否允许转交不能为空");
        }
        if (Objects.isNull(entity.getAcceptanceMode())) {
            entity.setAcceptanceMode(AcceptanceModeEnum.NOT_ACCEPTANCE.getValue());
        }
        if (entity.getAcceptanceMode().equals(AcceptanceModeEnum.DESIGNEE.getValue())) {
            if (Objects.isNull(entity.getAcceptor()) || entity.getAcceptor().length == 0) {
                throw new BizException("指定人员不能为空");
            }
        }
    }


    /**
      * 更新对象
      */
     @PutMapping("/update")
     public R<Void> update(@RequestHeader("dcm_headers") TenantIsolation tenant, @RequestBody OrderProcessConfigEntity entity){
         checkConfig(entity);
         Result<Void> result = service.update(tenant.getTenantId(),tenant.getIdType(), entity);
         return R.result(result);
     }


     /**
      * 获取对象
      * @param orderType 工单类型：1-维修，2-保养，3-巡检
      * @param isOutsourcing 是否外委
      * @param isDataRecord 是否数据型
      */
     @GetMapping("{orderType}/{isOutsourcing}/{isDataRecord}")
     public R<OrderProcessConfigEntity> get(@RequestHeader("dcm_headers") TenantIsolation tenant, @PathVariable Integer orderType,@PathVariable Boolean isOutsourcing,@PathVariable Boolean isDataRecord){
         Result<OrderProcessConfigEntity> result = service.getById(tenant.getTenantId(),tenant.getIdType(), orderType, isOutsourcing, isDataRecord);
         return R.result(result);
     }
    
}
