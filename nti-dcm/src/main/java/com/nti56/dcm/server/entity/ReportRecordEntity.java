package com.nti56.dcm.server.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 报表记录表 实体类
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @Ddate 2025-04-07 16:06:08
 * @since JDK 1.8
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@TableName("report_record")
public class ReportRecordEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
    * id
    */
    private Long id;

    /**
     * 租户id
     */
    @TableField(fill = FieldFill.INSERT)
    private Long tenantId;

    /**
    * 类型，1-日报，2-周报，3-月报
    */
    private Integer reportType;

    /**
    * 归属时间
    */
    private String belongDate;

    private LocalDateTime startTime;

    private LocalDateTime endTime;

    /**
    * 新增客户数
    */
    private Integer newCustomerCount;

    /**
    * 新增客户ID
    */
    private String newCustomer;

    /**
    * 新增设备台账数
    */
    private Integer newDeviceCount;

    /**
    * 新增物联设备数
    */
    private Integer newConnectDeviceCount;

    /**
    * 设备告警次数
    */
    private Integer newAlarmCount;

    /**
    * 设备故障次数
    */
    private Integer newErrorCount;

    /**
    * 新增质保临期客户数
    */
    private Integer newWarrantyAdventCusCount;

    /**
    * 新增质保临期客户ID
    */
    private String newWarrantyAdventCus;

    /**
    * 新增质保过期客户数
    */
    private Integer newWarrantyExpireCusCount;

    /**
    * 新增质保过期客户ID
    */
    private String newWarrantyExpireCus;

    /**
    * 累计质保临期客户数
    */
    private Integer cumWarrantyAdventCusCount;

    /**
    * 累计质保临期客户ID
    */
    private String cumWarrantyAdventCus;

    /**
    * 累计质保过期客户数
    */
    private Integer cumWarrantyExpireCusCount;

    /**
    * 累计质保过期客户ID
    */
    private String cumWarrantyExpireCus;

    /**
    * 累计质保临期设备数
    */
    private Integer cumWarrantyAdventDeviceCount;

    /**
    * 累计质保过期设备数
    */
    private Integer cumWarrantyExpireDeviceCount;

    /**
    * 新增维保临期客户数
    */
    private Integer newMaintenanceAdventCusCount;

    /**
    * 新增维保临期客户ID
    */
    private String newMaintenanceAdventCus;

    /**
    * 新增维保过期客户数
    */
    private Integer newMaintenanceExpireCusCount;

    /**
    * 新增维保过期客户ID
    */
    private String newMaintenanceExpireCus;

    /**
    * 累计维保临期客户数
    */
    private Integer cumMaintenanceAdventCusCount;

    /**
    * 累计维保临期客户ID
    */
    private String cumMaintenanceAdventCus;

    /**
    * 累计维保过期客户数
    */
    private Integer cumMaintenanceExpireCusCount;

    /**
    * 累计维保过期客户ID
    */
    private String cumMaintenanceExpireCus;

    /**
    * 累计维保临期设备数
    */
    private Integer cumMaintenanceAdventDeviceCount;

    /**
    * 累计维保过期设备数
    */
    private Integer cumMaintenanceExpireDeviceCount;

    /**
    * 服务客户数
    */
    private Integer serviceCustomerCount;

    /**
    * 服务客户ID
    */
    private String serviceCustomer;

    /**
    * 完成工单总数
    */
    private Integer finishOrderCount;

    /**
    * 维修服务客户数
    */
    private Integer repairServiceCustomerCount;

    /**
    * 维修服务客户ID
    */
    private String repairServiceCustomer;

    /**
    * 完成维修数
    */
    private Integer repairCompleteOrderCount;

    /**
    * 维修中数
    */
    private Integer repairIngOrderCount;

    /**
    * 保养服务客户数
    */
    private Integer maintenanceServiceCustomerCount;

    /**
    * 保养服务客户ID
    */
    private String maintenanceServiceCustomer;

    /**
    * 完成保养数
    */
    private Integer maintenanceCompleteOrderCount;

    /**
    * 保养中数
    */
    private Integer maintenanceIngOrderCount;

    /**
    * 巡检服务客户数
    */
    private Integer inspectServiceCustomerCount;

    /**
    * 巡检服务客户ID
    */
    private String inspectServiceCustomer;

    /**
    * 完成巡检数
    */
    private Integer inspectCompleteOrderCount;

    /**
    * 巡检中数
    */
    private Integer inspectIngOrderCount;

    /**
    * 版本号
    */
    @Version
    private Integer version;

    /**
    * 删除
    */
    @TableLogic
    private Integer deleted;

    /**
    * 创建人ID
    */
    @TableField(fill = FieldFill.INSERT)
    private Long creatorId;

    /**
    * 创建人
    */
    @TableField(fill = FieldFill.INSERT)
    private String creator;

    /**
    * 创建时间
    */
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
    * 更新人ID
    */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Long updatorId;

    /**
    * 更新人
    */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private String updator;

    /**
    * 更新时间
    */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    /**
    * 工程ID
    */
    @TableField(fill = FieldFill.INSERT)
    private Long engineeringId;

    /**
    * 模块ID
    */
    @TableField(fill = FieldFill.INSERT)
    private Long moduleId;

    /**
    * 空间ID
    */
    @TableField(fill = FieldFill.INSERT)
    private Long spaceId;



}
