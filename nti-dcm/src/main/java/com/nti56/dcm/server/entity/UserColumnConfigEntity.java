package com.nti56.dcm.server.entity;

import com.baomidou.mybatisplus.annotation.*;

import java.time.LocalDateTime;
import java.io.Serializable;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 用户表格列配置表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-02-07
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("user_column_config")
@ApiModel(value="UserColumnConfig对象", description="用户表格列配置表")
public class UserColumnConfigEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "id")
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @ApiModelProperty(value = "身份类型：1-供应商，2-客户")
    private Integer idType;

    @ApiModelProperty(value = "用户id")
    private Long userId;

    @ApiModelProperty(value = "保存列信息")
    private String columnText;

    @ApiModelProperty(value = "所属页面")
    private Integer belongPage;

    /**
     *租户id
     */
    @TableField(fill = FieldFill.INSERT)
    private Long tenantId;
    /**
     * 版本号
     */
    @Version
    private Integer version;

    /**
     * 删除
     */
    @TableLogic
    private Integer deleted;
    /**
     *创建人ID
     */
    @TableField(fill = FieldFill.INSERT)
    private Long creatorId;
    /**
     *创建人
     */
    @TableField(fill = FieldFill.INSERT)
    private String creator;
    /**
     *创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;
    /**
     *更新人ID
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Long updatorId;
    /**
     *更新人
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private String updator;
    /**
     *更新时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;
    /**
     *工程ID
     */
    @TableField(fill = FieldFill.INSERT)
    private Long engineeringId;
    /**
     *模块ID
     */
    @TableField(fill = FieldFill.INSERT)
    private Long moduleId;
    /**
     *空间ID
     */
    @TableField(fill = FieldFill.INSERT)
    private Long spaceId;
}
