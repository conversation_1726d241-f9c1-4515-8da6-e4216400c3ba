package com.nti56.dcm.server.model.dto;

import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

@Data
public class SafeStockDto {


    /**
     * itemName
     */
    private String itemName;

    /**
     * unit
     */
    private String unit;

    /**
     * itemCode
     */
    private String itemCode;

    /**
     * baseQty
     */
    private BigDecimal baseQty;

    /**
     * maxQty
     */
    private BigDecimal maxQty;

    /**
     * storeArea
     */
    private String storeArea;

    /**
     * minQty
     */
    private BigDecimal minQty;

    /**
     * warehouseId
     */
    private String warehouseId;

    /**
     * warehouseName
     */
    private String warehouseName;

    /**
     * warehouseCode
     */
    private String warehouseCode;

    private String spareAlarmType;

    private LocalDateTime spareAlarmTime;

}
