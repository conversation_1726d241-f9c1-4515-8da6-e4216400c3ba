package com.nti56.dcm.server.controller;


import com.nti56.dcm.server.entity.UserColumnConfigEntity;
import com.nti56.dcm.server.model.dto.DataPermissionDto;
import com.nti56.dcm.server.service.IUserColumnConfigService;
import com.nti56.nlink.common.dto.TenantIsolation;
import com.nti56.nlink.common.util.R;
import io.swagger.annotations.ApiOperation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * <p>
 * 用户表格列配置表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2025-02-07
 */
@RestController
@RequestMapping("/userColumnConfig")
@Tag(name = "用户表格列配置")
public class UserColumnConfigController {
    @Autowired
    private IUserColumnConfigService userColumnConfigService;

    @PostMapping("/saveUserConfig")
    @ApiOperation("保存配置")
    public R saveUserConfig(@RequestHeader("dcm_headers") TenantIsolation tenantIsolation, @RequestBody UserColumnConfigEntity entity) {
        return R.result(userColumnConfigService.saveUserConfig( entity,tenantIsolation));
    }

    @GetMapping("/getConfig")
    @ApiOperation("获取某个界面当前登录用户的列配置")
    public R getDetail(@RequestHeader("dcm_headers") TenantIsolation tenantIsolation, Integer belongPage) {
        return R.result(userColumnConfigService.getUserConfig(belongPage,tenantIsolation));
    }

}
