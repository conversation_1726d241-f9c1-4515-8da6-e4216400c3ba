package com.nti56.dcm.server.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.Date;

import com.baomidou.mybatisplus.annotation.Version;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 
 * @TableName device_type
 */
@TableName(value ="device_type")
@Data
@AllArgsConstructor
@NoArgsConstructor
public class DeviceTypeEntity implements Serializable {
    /**
     * 类型ID
     */
    @TableId
    private Long id;

    /**
     * 类型名称
     */
    private String typeName;

    /**
     * 类型编号
     */
    private String typeCode;

    /**
     * 父级类型id
     */
    private Long parentId;

    /**
     * 状态
     */
    private Integer status;

    /**
     * 参数信息
     */
    private String params;


    /**
     * 任务数据
     */
    private String taskData;


    /**
     * 工况数据
     */
    private String workData;


    /**
     * 能耗数据
     */
    private String energyData;

    /**
     * 是否核心设备
     */
    private Integer isCore;

    /**
     * 备注
     */
    private String remark;


    /**
     * 创建人
     */
    @TableField(fill = FieldFill.INSERT)
    private String creator;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 修改人
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private String updator;

    /**
     * 修改时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    /**
     * 租户ID
     */
    @TableField(fill = FieldFill.INSERT)
    private Long tenantId;

    /**
     * 工程ID
     */
    @TableField(fill = FieldFill.INSERT)
    private Long engineeringId;

    /**
     * 模块ID
     */
    @TableField(fill = FieldFill.INSERT)
    private Long moduleId;

    /**
     * 空间ID
     */
    @TableField(fill = FieldFill.INSERT)
    private Long spaceId;

    /**
     * 版本号
     */
    @Version
    private Integer version;

    /**
     * 所属身份 1-供应商 2-客户
     */
    private Integer idType;

    /**
     * 删除
     */
    @TableLogic
    private Integer deleted;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;

}