package com.nti56.dcm.server.domain.enums;

import lombok.Getter;

public enum AlarmOrderStatusEnum {
    WAIT_DISPATCH(1, "WAIT_DISPATCH", "待分派"),
    WAIT_RECEIVE(2, "WAIT_RECEIVE", "待接单"),
    EXECUTING(3, "EXECUTING", "执行中"),
    WAIT_ACCEPT(4, "WAIT_ACCEPT", "待验收"),
    WAIT_PROCESS(5, "WAIT_PROCESS", "待处理"),
    ;

    @Getter
    private Integer value;

    @Getter
    private String name;

    @Getter
    private String nameDesc;

    AlarmOrderStatusEnum(Integer value, String name, String nameDesc) {
        this.value = value;
        this.name = name;
        this.nameDesc = nameDesc;
    }

    public static AlarmOrderStatusEnum typeOfValue(Integer value){
        AlarmOrderStatusEnum[] values = AlarmOrderStatusEnum.values();
        for (AlarmOrderStatusEnum v : values) {
            if (v.value.equals(value)) {
                return v;
            }
        }
        return null;
    }

    public static AlarmOrderStatusEnum typeOfName(String name){
        AlarmOrderStatusEnum[] values = AlarmOrderStatusEnum.values();
        for (AlarmOrderStatusEnum v : values) {
            if (v.name.equals(name)) {
                return v;
            }
        }
        return null;
    }

    public static AlarmOrderStatusEnum typeOfNameDesc(String nameDesc){
        AlarmOrderStatusEnum[] values = AlarmOrderStatusEnum.values();
        for (AlarmOrderStatusEnum v : values) {
            if (v.nameDesc.equals(nameDesc)) {
                return v;
            }
        }
        return null;
    }
}
