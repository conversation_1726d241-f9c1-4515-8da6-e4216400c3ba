package com.nti56.dcm.server.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 保养工单表 实体类
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @Ddate 2024-03-01 16:28:19
 * @since JDK 1.8
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@TableName("maintenance_order")
public class MaintenanceOrderEntity implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
    * ID
    */
    private Long id;

    /**
    * 保养计划ID
    */
    private Long maintenancePlanId;

    /**
    * 保养工单编号
    */
    private String orderNumber;

    /**
    * 保养模式：1-单次，2-周期
    */
    private Integer maintenanceMode;

    /**
    * 保养说明
    */
    private String orderDesc;

    /**
    * 计划保养时间
    */
    //@JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private LocalDateTime planMaintenanceTime;

    /**
    * 设备ID
    */
    private Long deviceId;

    /**
    * 是否停机：0-不停机，1-停机
    */
    private Integer halt;

    /**
    * 保养标准ID(多选 逗号隔开)
    */
    private String maintenanceStandardId;

    /**
     * 执行人id
     */
    private Long executeUserId;

    /**
    * 委外状态：0-内部，1-委外
    */
    private Integer outsource;

    /**
     * 外委供应商类型：1-租户型，2 数据型
     */
    private Integer outsourceSupplierType;

    /**
     * 外委客户类型：1-租户型，2 数据型
     */
    private Integer outsourceCustomerType;

    /**
    * 委外供应商ID
    */
    private Long supplierId;

    /**
    * 委外供应商名称
    */
    private String supplierName;

    /**
    * 快速委外关联原始单据id
    */
    private Long outsourceOriginId;

    /**
     * 保养开始时间
     */
    private LocalDateTime maintenanceBegin;

    /**
     * 保养结束时间
     */
    private LocalDateTime maintenanceEnd;

    /**
    * 工单状态，1-待分配，2-待接收，3-保养中，4-待验收，5-验收通过，6-验收不通过，7-已撤销
    */
    private Integer status;

    /**
    * 是否逾期 1-未逾期，2-逾期）
    */
    private Integer overdue;

    /**
     *创建人ID
     */
    @TableField(fill = FieldFill.INSERT)
    private Long creatorId;

    /**
    * 创建人
    */
    @TableField(fill = FieldFill.INSERT)
    private String creator;

    /**
    * 创建时间
    */
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     *更新人ID
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Long updatorId;

    /**
    * 修改人
    */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private String updator;

    /**
    * 修改时间
    */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    /**
    * 租户ID
    */
    @TableField(fill = FieldFill.INSERT)
    private Long tenantId;

    /**
    * 工程ID
    */
    @TableField(fill = FieldFill.INSERT)
    private Long engineeringId;

    /**
    * 模块ID
    */
    @TableField(fill = FieldFill.INSERT)
    private Long moduleId;

    /**
    * 空间ID
    */
    @TableField(fill = FieldFill.INSERT)
    private Long spaceId;

    /**
    * 版本号
    */
    @Version
    private Integer version;

    /**
    * 删除
    */
    @TableLogic
    private Integer deleted;

    /**
     * 委外客户名称
     */
    private String customerName;

    /**
     * 创建来源 1-供应商/集成商 2-客户
     */
    private Integer createSource;

    /**
     * 工单等级 1-紧急 2-重要 3-普通 4-其他
     */
    private Integer level;

    /**
     * 是否超时 1-正常 2-超时
     */
    private Integer timeout;

    /**
     * 审批流程唯一标识
     */
    private String processInstanceId;

}
