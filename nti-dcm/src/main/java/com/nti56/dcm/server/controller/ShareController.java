package com.nti56.dcm.server.controller;

import com.nti56.basic.domain.response.ResultVO;
import com.nti56.dcm.server.model.dto.ShareDto;
import com.nti56.dcm.server.service.ShareService;
import com.nti56.nlink.common.dto.TenantIsolation;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

/**
 * 角色表 控制器
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2024-02-26 16:54:02
 * @since JDK 1.8
 */
@RestController
@Tag(name = "分享")
@RequestMapping("/share")
@Slf4j
public class ShareController {

    @Autowired
    private ShareService shareService;

    @Operation(summary = "分享通知")
    @PostMapping(value = "/shareNotice")
    public ResultVO shareNotice(@RequestHeader("dcm_headers") TenantIsolation tenantIsolation,
                                @Validated @RequestBody ShareDto dto) {
        shareService.shareNotice(tenantIsolation, dto);
        return ResultVO.succeed();
    }

}
