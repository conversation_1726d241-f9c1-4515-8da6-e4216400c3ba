package com.nti56.dcm.server.config;

import liquibase.integration.spring.SpringLiquibase;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.annotation.Order;
import org.springframework.core.env.Environment;

import javax.sql.DataSource;
import java.util.Objects;

@Configuration
@Order(1)
public class LiquibaseConfig {

    @Autowired
    private Environment environment;

    @Bean
    public SpringLiquibase liquibase(DataSource dataSource) {
        String enable = environment.getProperty("spring.liquibase.enabled");
        if (Objects.isNull(enable) || Boolean.parseBoolean(enable)) {
            SpringLiquibase liquibase = new SpringLiquibase();
            liquibase.setDataSource(dataSource);
            liquibase.setChangeLog(Objects.requireNonNull(environment.getProperty("spring.liquibase.change-log")));
            return liquibase;
        }else {
            return null;
        }
    }
}
