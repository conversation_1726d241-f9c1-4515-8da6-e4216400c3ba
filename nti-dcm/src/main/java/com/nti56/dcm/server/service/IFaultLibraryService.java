package com.nti56.dcm.server.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.nti56.dcm.server.entity.FaultLibraryEntity;
import com.nti56.dcm.server.model.dto.FaultLibraryDto;
import com.nti56.dcm.server.model.vo.FaultLibraryVo;
import com.nti56.nlink.common.dto.TenantIsolation;
import com.nti56.nlink.common.util.Result;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <p>
 * 故障库表 服务类
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @Ddate 2025-06-04 14:42:27
 * @since JDK 1.8
 */
public interface IFaultLibraryService extends IService<FaultLibraryEntity> {

    /**
     * 保存故障库记录
     * @param entity 故障库实体
     * @return 保存结果
     */
    Result<FaultLibraryEntity> save(TenantIsolation tenantIsolation, FaultLibraryEntity entity);

    /**
     * 分页查询故障库记录
     * @param dto 查询条件
     * @param page 分页参数
     * @return 分页结果
     */
    Result<Page<FaultLibraryVo>> getPage(TenantIsolation tenantIsolation, FaultLibraryDto dto, Page<FaultLibraryVo> page);

    /**
     * 查询故障库列表
     * @param entity 查询条件
     * @return 列表结果
     */
    Result<List<FaultLibraryEntity>> list(TenantIsolation tenantIsolation, FaultLibraryEntity entity);

    /**
     * 更新故障库记录
     * @param entity 故障库实体
     * @return 更新结果
     */
    Result<Void> update(TenantIsolation tenantIsolation, FaultLibraryEntity entity);

    /**
     * 根据ID删除故障库记录
     * @param entityId 记录ID
     * @return 删除结果
     */
    Result<Void> deleteById(@NotNull Long entityId);

    /**
     * 根据ID批量删除故障库记录
     * @param entityIds 记录ID列表
     * @return 删除结果
     */
    Result<Void> batchDelete(@NotNull List<Long> entityIds);

    /**
     * 根据ID获取故障库记录
     * @param entityId 记录ID
     * @return 查询结果
     */
    Result<FaultLibraryEntity> getById(@NotNull Long entityId);

    void syncOTEvent();
}
