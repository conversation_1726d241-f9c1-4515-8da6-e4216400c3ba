package com.nti56.dcm.server.domain.enums;

import lombok.Getter;

public enum CreateSourceEnum {
    SUPPLIER(1, "supplier", "供应商"),
    CUSTOMER(2, "customer", "客户"),
    ;

    @Getter
    private Integer value;

    @Getter
    private String name;

    @Getter
    private String nameDesc;

    CreateSourceEnum(Integer value, String name, String nameDesc) {
        this.value = value;
        this.name = name;
        this.nameDesc = nameDesc;
    }

    public static CreateSourceEnum typeOfValue(Integer value){
        CreateSourceEnum[] values = CreateSourceEnum.values();
        for (CreateSourceEnum v : values) {
            if (v.value.equals(value)) {
                return v;
            }
        }
        return null;
    }

    public static CreateSourceEnum typeOfName(String name){
        CreateSourceEnum[] values = CreateSourceEnum.values();
        for (CreateSourceEnum v : values) {
            if (v.name.equals(name)) {
                return v;
            }
        }
        return null;
    }

    public static CreateSourceEnum typeOfNameDesc(String nameDesc){
        CreateSourceEnum[] values = CreateSourceEnum.values();
        for (CreateSourceEnum v : values) {
            if (v.nameDesc.equals(nameDesc)) {
                return v;
            }
        }
        return null;
    }
}
