package com.nti56.dcm.server.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.nti56.dcm.server.model.dto.DeviceDto;
import com.nti56.dcm.server.model.dto.SupplierListQueryDto;
import com.nti56.dcm.server.model.vo.SupplierListPcVo;
import com.nti56.dcm.server.service.SupplierViewService;
import com.nti56.nlink.common.dto.TenantIsolation;
import com.nti56.nlink.common.util.R;
import com.nti56.nlink.common.util.Result;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;


/**
 * <p>
 * 供应商概览 控制器
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @Ddate 2024-12-10 10:42:46
 * @since JDK 1.8
 */
@RestController
@RequestMapping("/supplierView")
@Tag(name = "供应商概览")
public class SupplierViewController {

    @Autowired
    private SupplierViewService service;

    @GetMapping("/getStats")
    @Operation(summary = "获取概览统计数据")
    public R getStats(@RequestHeader("dcm_headers") TenantIsolation tenantIsolation) {
        return R.result(service.queryStatsInfo(tenantIsolation));
    }

    @GetMapping("/querySupplierList")
    @Operation(summary = "查询供应商列表")
    public R list(Long deviceTypeId, String supplierName, @RequestHeader("dcm_headers") TenantIsolation tenantIsolation) {
        return R.result(service.querySupplierList(deviceTypeId, supplierName, tenantIsolation));
    }

    @GetMapping("/detail")
    @Operation(summary = "查询供应商详情")
    public R detail(@RequestHeader("dcm_headers") TenantIsolation tenantIsolation, Long supplierId) {
        return R.result(service.getDetail(tenantIsolation, supplierId));
    }



    @GetMapping("/getPcDetail")
    @Operation(summary = "查询供应商详情-PC")
    public R getPcDetail(@RequestHeader("dcm_headers") TenantIsolation tenantIsolation, Long supplierId) {
        return R.result(service.getPcDetail(tenantIsolation, supplierId));
    }


    @PostMapping("/queryListStats")
    @Operation(summary = "获取列表统计数据")
    public R queryListStats(@RequestHeader("dcm_headers") TenantIsolation tenantIsolation, @RequestBody SupplierListQueryDto supplierListQueryDto) {
        return R.result(service.queryListStats(tenantIsolation,supplierListQueryDto));
    }

    @PostMapping("/pageList")
    @Operation(summary = "获取供应商列表分页")
    public R page(@RequestHeader("dcm_headers") TenantIsolation tenantIsolation, @RequestBody SupplierListQueryDto supplierListQueryDto) {
        Page<SupplierListPcVo> page = supplierListQueryDto.toPage(SupplierListPcVo.class);
        page.setOrders(supplierListQueryDto.getOrderItems());
        Result<Page<SupplierListPcVo>> result = service.pageList(supplierListQueryDto, page, tenantIsolation);
        return R.result(result);
    }
}
