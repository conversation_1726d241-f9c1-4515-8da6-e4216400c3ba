package com.nti56.dcm.server.domain.enums;

import lombok.Getter;

public enum ConnectOtEnum {
    CONNECT(1, "connect", "连接"),
    NOT_CONNECT(0, "notConnect", "未连接"),
    ;

    @Getter
    private Integer value;

    @Getter
    private String name;

    @Getter
    private String nameDesc;

    ConnectOtEnum(Integer value, String name, String nameDesc) {
        this.value = value;
        this.name = name;
        this.nameDesc = nameDesc;
    }

    public static ConnectOtEnum typeOfValue(Integer value){
        ConnectOtEnum[] values = ConnectOtEnum.values();
        for (ConnectOtEnum v : values) {
            if (v.value.equals(value)) {
                return v;
            }
        }
        return null;
    }

    public static ConnectOtEnum typeOfName(String name){
        ConnectOtEnum[] values = ConnectOtEnum.values();
        for (ConnectOtEnum v : values) {
            if (v.name.equals(name)) {
                return v;
            }
        }
        return null;
    }

    public static ConnectOtEnum typeOfNameDesc(String nameDesc){
        ConnectOtEnum[] values = ConnectOtEnum.values();
        for (ConnectOtEnum v : values) {
            if (v.nameDesc.equals(nameDesc)) {
                return v;
            }
        }
        return null;
    }
}
