package com.nti56.dcm.server.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.beust.jcommander.internal.Lists;
import com.nti56.common.util.R;
import com.nti56.dcm.server.domain.enums.IdTypeEnum;
import com.nti56.dcm.server.domain.enums.PmoTenantEnum;
import com.nti56.dcm.server.entity.DeviceEntity;
import com.nti56.dcm.server.entity.TenantInfoEntity;
import com.nti56.dcm.server.model.dto.BindDeviceDto;
import com.nti56.dcm.server.model.dto.DeviceBatchOperationDto;
import com.nti56.dcm.server.model.dto.DeviceConnectDto;
import com.nti56.dcm.server.model.dto.DeviceConnectValidDto;
import com.nti56.dcm.server.model.dto.DeviceDto;
import com.nti56.dcm.server.model.dto.DeviceExcelBindOtDto;
import com.nti56.dcm.server.model.dto.DeviceExcelTemplateDto;
import com.nti56.dcm.server.model.dto.DeviceImportErrorDto;
import com.nti56.dcm.server.model.dto.DeviceTemplateRelationDto;
import com.nti56.dcm.server.model.dto.ExportCustomerPmoDeviceDto;
import com.nti56.dcm.server.model.dto.OtChannelDto;
import com.nti56.dcm.server.model.dto.OtDeviceDto;
import com.nti56.dcm.server.model.dto.OtDeviceTemplateDto;
import com.nti56.dcm.server.model.dto.OtEdgeGatewayDto;
import com.nti56.dcm.server.model.dto.OtTagDto;
import com.nti56.dcm.server.schedule.DeviceSchedule;
import com.nti56.dcm.server.service.DeviceService;
import com.nti56.dcm.server.service.DeviceTypeService;
import com.nti56.dcm.server.service.TenantInfoService;
import com.nti56.nlink.common.dto.TenantIsolation;
import com.nti56.nlink.common.util.PageParam;
import com.nti56.nlink.common.util.Result;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.UnsupportedEncodingException;
import java.util.List;

@RestController
@RequestMapping("/device")
@Tag(name = "设备模块")
@Slf4j
public class DeviceController {

    @Autowired
    private DeviceService deviceService;

    @Autowired
    private DeviceTypeService deviceTypeService;

    @Autowired
    private TenantInfoService tenantInfoService;

    @PostMapping("/page")
    @Operation(summary = "获取设备分页")
    public R<Page<DeviceDto>> page(@RequestHeader("dcm_headers") TenantIsolation tenantIsolation, @RequestBody DeviceDto deviceDto) {
        Page<DeviceDto> page = deviceDto.toPage(DeviceDto.class);
        Result<Page<DeviceDto>> result = deviceService.getPage(deviceDto, page, tenantIsolation);
        return R.result(result);
    }

    @PostMapping("/pageForDataPermission")
    @Operation(summary = "获取设备分页_数据权限用")
    public R<Page<DeviceDto>> pageForDataPermission(@RequestHeader("dcm_headers") TenantIsolation tenantIsolation, @RequestBody DeviceDto deviceDto) {
        Page<DeviceDto> page = deviceDto.toPage(DeviceDto.class);
        Result<Page<DeviceDto>> result = deviceService.getPageForDataPermission(deviceDto, page, tenantIsolation);
        return R.result(result);
    }

    @GetMapping("/getCustomAndTenantId")
    @Operation(summary = "获取数据型与租户型的租户id")
    public R<List<Long>> getCustomAndTenantId(@RequestHeader("dcm_headers") TenantIsolation tenantIsolation) {
        List<Long> tenantIdList = Lists.newArrayList();
        tenantIdList.add(tenantIsolation.getTenantId());
        if (IdTypeEnum.CUSTOMER.getValue().equals(tenantIsolation.getIdType()) && PmoTenantEnum.IS_PMO_TENANT.getValue().equals(tenantIsolation.getIsPmoTenant())) {
            Result<TenantInfoEntity> tenantInfoResult = tenantInfoService.getById(tenantIsolation.getTenantId(), tenantIsolation.getTenantId());
            if (tenantInfoResult.getSignal() && tenantInfoResult.getResult() != null) {
                tenantIdList.add(tenantInfoResult.getResult().getPmoTenantId());
            }
        }
        return R.result(Result.ok(tenantIdList));
    }


    @PostMapping("/listDevice")
    @Operation(summary = "查询设备列表")
    public R<List<DeviceDto>> listDevice(@RequestHeader("dcm_headers") TenantIsolation tenantIsolation, @RequestBody DeviceDto deviceDto) {
        Result<List<DeviceDto>> result = deviceService.listDevice(deviceDto, tenantIsolation);
        return R.result(result);
    }
    @PostMapping("")
    @Operation(summary = "新增设备")
    public R<DeviceEntity> createDeviceType(@RequestHeader("dcm_headers") TenantIsolation tenantIsolation, @RequestBody @Validated DeviceDto deviceDto) {
        return R.result(deviceService.create(deviceDto, tenantIsolation));
    }


    @PutMapping("/{id}")
    @Operation(summary = "修改设备")
    public R editChannel(@RequestHeader("dcm_headers") TenantIsolation tenantIsolation, @RequestBody @Validated DeviceDto deviceDto) {
        return R.result(deviceService.edit(deviceDto, tenantIsolation));
    }

    @DeleteMapping("/{id}")
    @Operation(summary = "根据id删除设备")
    public R deleteDeviceType(@RequestHeader("dcm_headers") TenantIsolation tenantIsolation, @PathVariable Long id) {
        return R.result(deviceService.deleteById(id, tenantIsolation));
    }

    @GetMapping("/{id}")
    @Operation(summary = "根据id查找设备详情")
    public R<DeviceDto> getById(@RequestHeader("dcm_headers") TenantIsolation tenantIsolation, @PathVariable("id") Long id) {
        return R.result(deviceService.getDeviceInfo(tenantIsolation, id));
    }

    @GetMapping("/getDeviceInfoCustomer")
    @Operation(summary = "根据id查找客户设备详情")
    public R<DeviceDto> getCustomerDetailById(@RequestHeader("dcm_headers") TenantIsolation tenantIsolation, Long id, String serialNumber) {
        return R.result(deviceService.getDeviceInfoCustomer(tenantIsolation, id, serialNumber));
    }


    @GetMapping("/getDeviceMonitorConfig/{id}")
    @Operation(summary = "根据id查找设备监控配置信息")
    public R<DeviceTemplateRelationDto> getDeviceMonitorConfig(@RequestHeader("dcm_headers") TenantIsolation tenantIsolation, @PathVariable("id") Long id) {
        return R.result(deviceService.getDeviceMonitorConfig(tenantIsolation, id));
    }


    @GetMapping("/serialNumber/{serialNumber}")
    @Operation(summary = "根据id查找设备详情")
    public R<DeviceDto> getBySerialNumber(@RequestHeader("dcm_headers") TenantIsolation tenantIsolation, @PathVariable("serialNumber") String serialNumber) {
        return R.result(deviceService.getDeviceInfoBySerialNumber(tenantIsolation, serialNumber));
    }

    @PostMapping("/template")
    @Operation(summary = "根据设备类型下载导入模板")
    public void getDeviceTemplate(@RequestHeader("dcm_headers") TenantIsolation tenantIsolation, @RequestBody DeviceExcelTemplateDto deviceExcelTemplateDto, HttpServletResponse response) throws UnsupportedEncodingException {
        deviceService.downloadTemplate(tenantIsolation, deviceExcelTemplateDto, response);

    }

    @PostMapping("/downloadBindTemplate")
    @Operation(summary = "下载设备动态绑定模板")
    public void downloadBindTemplate(@RequestHeader("dcm_headers") TenantIsolation tenantIsolation, @RequestBody DeviceExcelBindOtDto deviceExcelBindOtDto, HttpServletResponse response) throws UnsupportedEncodingException {
        deviceService.downloadBindTemplate(tenantIsolation, deviceExcelBindOtDto, response);
    }


    @Operation(summary = "导入批量创建设备")
    @PostMapping(value = "/uploadDevice", name = "模板导入创建设备")
    public R<List<DeviceImportErrorDto>> uploadDataExcel(@RequestHeader("dcm_headers") TenantIsolation tenantIsolation, @RequestParam("file") MultipartFile file, Long typeId) {
        try {
            return R.result(deviceService.uploadData(file, typeId, tenantIsolation));
        } catch (Exception ex) {
            log.error("导入创建设备上传文件异常，{}", ex);
            return R.error("导入失败！失败原因：" + ex.toString());
        }
    }

    @Operation(summary = "获取OT可用网关信息")
    @GetMapping(value = "/getEdgeGatewayList")
    public R<List<OtEdgeGatewayDto>> getEdgeGatewayList(@RequestHeader("dcm_headers") TenantIsolation tenantIsolation, String customerName) {
        try {
            return deviceService.getEdgeGatewayList(tenantIsolation, customerName);
        } catch (Exception ex) {
            log.error("获取可用网关信息异常，{}", ex);
            return R.error("获取可用网关信息失败！失败原因：" + ex.toString());
        }
    }

    @Operation(summary = "获取OT设备列表")
    @GetMapping(value = "/getDeviceList")
    public R<Page<OtDeviceDto>> getDeviceList(@RequestHeader("dcm_headers") TenantIsolation tenantIsolation, PageParam pageParam, String deviceName, String tagSearch) {
        try {
            Page<OtDeviceDto> pageQuery = pageParam.toPage(OtDeviceDto.class);
            return deviceService.getDeviceList(tenantIsolation, pageQuery, deviceName, tagSearch);
        } catch (Exception ex) {
            log.error("获取已存在ot设备列表异常，{}", ex);
            return R.error("获取已存在ot设备列表失败！失败原因：" + ex.toString());
        }
    }

    @Operation(summary = "获取OT设备模板列表")
    @GetMapping(value = "/getDeviceTemplateList")
    public R<List<OtDeviceTemplateDto>> getDeviceTemplateList(@RequestHeader("dcm_headers") TenantIsolation tenantIsolation) {
        try {
            return deviceService.getDeviceTemplateList(tenantIsolation);
        } catch (Exception ex) {
            log.error("获取OT设备模板列表异常，{}", ex);
            return R.error("获取OT设备模板列表失败！失败原因：" + ex.toString());
        }
    }

    @Operation(summary = "获取OT标记信息")
    @GetMapping(value = "/getTagList")
    public R<List<OtTagDto>> getTagList(@RequestHeader("dcm_headers") TenantIsolation tenantIsolation, String customerName) {
        try {
            return deviceService.getTagList(tenantIsolation, customerName);
        } catch (Exception ex) {
            log.error("获取OT标记信息异常，{}", ex);
            return R.error("获取OT标记信息失败！失败原因：" + ex.toString());
        }
    }

    @Operation(summary = "校验设备模板")
    @PostMapping(value = "/validTemplate")
    public R<OtChannelDto> validTemplate(@RequestHeader("dcm_headers") TenantIsolation tenantIsolation, @Validated @RequestBody DeviceConnectValidDto validDto) {
        try {
            return deviceService.validTemplate(tenantIsolation, validDto);
        } catch (Exception ex) {
            log.error("校验OT设备模板异常，{}", ex);
            return R.error("校验OT设备模板异常！失败原因：" + ex.toString());
        }
    }

    @Operation(summary = "根据设备模板创建OT设备")
    @PostMapping(value = "/createByTemplate")
    public R<Long> createByTemplate(@RequestHeader("dcm_headers") TenantIsolation tenantIsolation, @Validated @RequestBody DeviceConnectDto connectDto) {
        try {
            return deviceService.createByTemplate(tenantIsolation, connectDto);
        } catch (Exception ex) {
            log.error("根据设备模板创建OT设备异常，{}", ex);
            return R.error("根据设备模板创建OT设备异常！失败原因：" + ex.toString());
        }
    }

    @Operation(summary = "绑定已存在的OT设备")
    @PutMapping(value = "/bind")
    public R<Void> bindOtDevice(@RequestHeader("dcm_headers") TenantIsolation tenantIsolation, @Validated @RequestBody BindDeviceDto bindDeviceDto) {
        return deviceService.bindOtDevice(tenantIsolation, bindDeviceDto);
    }

    @Operation(summary = "解除OT设备绑定")
    @PutMapping(value = "/un-bind")
    public R<Void> unBindOtDevice(@RequestHeader("dcm_headers") TenantIsolation tenantIsolation, @RequestBody BindDeviceDto bindDeviceDto) {
        return deviceService.unBindOtDevice(tenantIsolation, bindDeviceDto);
    }

    @PostMapping("/batchExport")
    @Operation(summary = "批量导出设备台账")
    public void batchExport(@RequestHeader("dcm_headers") TenantIsolation tenantIsolation, @Validated @RequestBody DeviceBatchOperationDto dto, HttpServletResponse response) throws UnsupportedEncodingException {
        deviceService.batchExport(tenantIsolation, dto, response);
    }

    @PostMapping("/batchExportPmoDevice")
    @Operation(summary = "批量导出Pmo设备台账")
    public void batchExportPmoDevice(@RequestHeader("dcm_headers") TenantIsolation tenantIsolation, @Validated @RequestBody DeviceBatchOperationDto dto, HttpServletResponse response) throws UnsupportedEncodingException {
        deviceService.batchExportPmoDevice(tenantIsolation, dto, response);
    }

    @PostMapping("/exportAllCustomerPmoDevice")
    @Operation(summary = "导出客户的全部Pmo设备台账")
    public void batchExportPmoDevice(@RequestHeader("dcm_headers") TenantIsolation tenantIsolation, @RequestBody ExportCustomerPmoDeviceDto dto, HttpServletResponse response) throws UnsupportedEncodingException {
        deviceService.exportAllCustomerPmoDevice(tenantIsolation, dto, response);
    }

    @Operation(summary = "导入批量绑定ot设备")
    @PostMapping(value = "/uploadBindOtDevice", name = "导入批量绑定ot设备")
    public R<List<DeviceImportErrorDto>> uploadBindOtDevice(@RequestHeader("dcm_headers") TenantIsolation tenantIsolation, @RequestParam("file") MultipartFile file) {
        try {
            return R.result(deviceService.uploadBindOtDevice(file, tenantIsolation));
        } catch (Exception ex) {
            log.error("导入批量绑定ot设备异常，{}", ex);
            return R.error("导入失败！失败原因：" + ex.toString());
        }
    }

    @PutMapping("/batchDelete")
    @Operation(summary = "批量删除设备台账")
    public R batchDelete(@RequestHeader("dcm_headers") TenantIsolation tenantIsolation, @Validated @RequestBody DeviceBatchOperationDto dto) {
        return R.result(deviceService.batchDelete(tenantIsolation, dto));
    }

    @PutMapping("/batchUpdateLocation")
    @Operation(summary = "批量更新设备台账存放空间")
    public R batchUpdateLocation(@RequestHeader("dcm_headers") TenantIsolation tenantIsolation, @Validated @RequestBody DeviceBatchOperationDto dto) {
        return R.result(deviceService.batchUpdateLocation(tenantIsolation, dto));
    }


    @Autowired
    private DeviceSchedule deviceSchedule;

    @GetMapping("/testSendDeviceMessage")
    @Operation(summary = "测试发送设备接入消息")
    public void testSendDeviceMessage() throws InterruptedException {
        deviceSchedule.sendDeviceConnectAndCreateMessage();
    }
}
