package com.nti56.dcm.server.controller;

import javax.servlet.http.HttpServletResponse;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.nti56.dcm.server.model.dto.QueryDeviceFileDto;
import com.nti56.dcm.server.model.vo.DeviceFileVo;
import com.nti56.nlink.common.util.PageParam;
import io.swagger.v3.oas.annotations.Operation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import com.nti56.dcm.server.model.dto.FileDto;
import com.nti56.dcm.server.model.vo.FileVo;
import com.nti56.dcm.server.service.FileService;
import com.nti56.nlink.common.dto.TenantIsolation;
import com.nti56.common.util.R;
import com.nti56.nlink.common.util.Result;

import lombok.extern.slf4j.Slf4j;

import java.util.List;

/**
 * 文件管理
 * 
 * <AUTHOR>
 * @version 1.0
 * @date 2023-11-02 10:50:57
 * @since JDK 1.8
 */
@RestController
@RequestMapping("file")
@Slf4j
public class FileController {
    
    @Autowired
    FileService fileService;

    /**
     * 上传文件
     * @param file 文件
     * @return 文件信息
     */
    @PostMapping("/uploadFile")
    public R<FileVo> upload(@RequestHeader("dcm_headers")TenantIsolation tenant, @RequestParam("file") MultipartFile file){
        Result<FileVo> result = fileService.upload(tenant.getTenantId(), file);
        return R.result(result);
    }

    /**
     * 删除文件记录及路径
     * @param fileId 文件id
     */
    @DeleteMapping("/removeFileAndRecord/{fileId}")
    public R<Void> removeFileAndRecord(@RequestHeader("dcm_headers")TenantIsolation tenant, @PathVariable("fileId") Long fileId){
        Result<Void> result = fileService.removeFileAndRecord(tenant.getTenantId(), fileId);
        return R.result(result);
    }

    /**
     * 保存文件名称及备注信息
     * @param fileId 文件id
     */
    @PutMapping("/saveFileInfo")
    public R<Void> saveFileInfo(@RequestHeader("dcm_headers")TenantIsolation tenant, @RequestBody FileDto dto){
        Result<Void> result = fileService.saveFileInfo(tenant.getTenantId(), dto);
        return R.result(result);
    }


    /**
     * 删除文件路径
     * @param path 文件路径
     */
    @PostMapping("/removeFile")
    public R<Void> remove(@RequestHeader("dcm_headers")TenantIsolation tenant, @RequestParam("path") String path){
        Result<Void> result = fileService.remove(tenant.getTenantId(), path);
        return R.result(result);
    }

    /**
     * 下载文件
     * @param fileId 文件id
     */
    @GetMapping("/downloadFile/{fileId}")
    public void download(HttpServletResponse response ,@RequestHeader("dcm_headers") TenantIsolation tenant, @PathVariable("fileId") Long fileId){
        fileService.download(response, tenant.getTenantId() , fileId);
    }

    @PostMapping("/uploadOtherFile")
    @Operation(summary = "上传其他资料")
    public R<Void> uploadOtherFile(@RequestHeader("dcm_headers")TenantIsolation tenant, @RequestBody List<FileVo> fileList) {
        return R.result(fileService.uploadOtherFile(tenant, fileList));
    }

    @GetMapping("/getDeviceFile")
    @Operation(summary = "获取设备资料")
    public R<Page<DeviceFileVo>> getDeviceFile(@RequestHeader("dcm_headers")TenantIsolation tenant, PageParam pageParam,
                                               QueryDeviceFileDto dto) {
        Page<DeviceFileVo> page = pageParam.toPage(DeviceFileVo.class);
        return R.result(fileService.getDeviceFile(tenant, page, dto));
    }

    @PutMapping("/batchRemoveFile")
    @Operation(summary = "批量删除文件")
    public R<Void> batchRemoveFile(@RequestHeader("dcm_headers")TenantIsolation tenant, @RequestParam("fileIds") List<Long> fileIds) {
        return R.result(fileService.batchRemoveFile(tenant, fileIds));
    }

}
