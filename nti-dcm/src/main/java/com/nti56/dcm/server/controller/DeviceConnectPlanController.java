package com.nti56.dcm.server.controller;

import com.nti56.common.util.R;
import com.nti56.dcm.server.entity.DeviceConnectPlanEntity;
import com.nti56.dcm.server.service.IDeviceConnectPlanService;
import com.nti56.nlink.common.dto.TenantIsolation;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * <p>
 * 物联设备接入进度表 控制器
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @Ddate 2024-12-16 15:11:19
 * @since JDK 1.8
 */
@RestController
@RequestMapping("/deviceConnectPlan")
@Tag(name = "物联设备接入进度表模块")
public class DeviceConnectPlanController {

    @Autowired
    IDeviceConnectPlanService service;

    @GetMapping("listByCustomerId")
    @Operation(summary = "根据客户ID查询物联设备接入进度表列表")
    public R listByCustomerId(@RequestHeader("dcm_headers") TenantIsolation tenantIsolation,
                              @RequestParam("customerId") Long customerId) {
        return R.result(service.listByCustomerId(tenantIsolation, customerId));
    }

    @PostMapping("editStatus")
    @Operation(summary = "编辑物联设备接入进度表状态")
    public R editStatus(@RequestHeader("dcm_headers") TenantIsolation tenantIsolation,
                        @RequestBody DeviceConnectPlanEntity entity) {
        return R.result(service.editStatus(tenantIsolation, entity));
    }

}
