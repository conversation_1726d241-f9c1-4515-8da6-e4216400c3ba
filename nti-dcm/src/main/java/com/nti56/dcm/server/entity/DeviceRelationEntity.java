package com.nti56.dcm.server.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 
 * @TableName device_relation
 */
@TableName(value ="device_relation")
@Data
@AllArgsConstructor
@NoArgsConstructor
public class DeviceRelationEntity implements Serializable {
    /**
     * 关联关系ID
     */
    @TableId
    private Long id;

    /**
     * 设备ID
     */
    private Long deviceId;

    /**
     * 关联类型 父/子 1/0
     */
    private Integer relationType;

    /**
     * 关联设备ID
     */
    private Long relationDeviceId;

    /**
     * 关联设备编号
     */
    private String relationDeviceNo;

    /**
     * 关联设备名称
     */
    private String relationDeviceName;

    /**
     * 关联设备规格
     */
    private String relationDeviceSpecs;

    /**
     * 关联设备品牌
     */
    private String relationDeviceBrand;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;

}