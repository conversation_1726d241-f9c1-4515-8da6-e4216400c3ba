package com.nti56.dcm.server.domain.enums;

import lombok.Getter;

public enum LevelEnum {
    EMERGENCY(1, "EMERGENCY", "紧急"),
    IMPORTANT(2, "IMPORTANT", "重要"),
    NORMAL(3, "NORMAL", "普通"),
    OTHER(4, "OTHER", "其他"),
    ;

    @Getter
    private Integer value;

    @Getter
    private String name;

    @Getter
    private String nameDesc;

    LevelEnum(Integer value, String name, String nameDesc) {
        this.value = value;
        this.name = name;
        this.nameDesc = nameDesc;
    }

    public static LevelEnum typeOfValue(Integer value){
        LevelEnum[] values = LevelEnum.values();
        for (LevelEnum v : values) {
            if (v.value.equals(value)) {
                return v;
            }
        }
        return null;
    }

    public static LevelEnum typeOfName(String name){
        LevelEnum[] values = LevelEnum.values();
        for (LevelEnum v : values) {
            if (v.name.equals(name)) {
                return v;
            }
        }
        return null;
    }

    public static LevelEnum typeOfNameDesc(String nameDesc){
        LevelEnum[] values = LevelEnum.values();
        for (LevelEnum v : values) {
            if (v.nameDesc.equals(nameDesc)) {
                return v;
            }
        }
        return null;
    }
}
