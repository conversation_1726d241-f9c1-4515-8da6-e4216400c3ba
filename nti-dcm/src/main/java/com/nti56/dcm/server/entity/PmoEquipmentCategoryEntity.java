package com.nti56.dcm.server.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import java.io.Serializable;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 货品种类表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-16
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("pmo_equipment_category")
public class PmoEquipmentCategoryEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键id
     */
    @TableId(value = "id", type = IdType.NONE)
    private Long id;

    /**
     * 所属集团
     */
    private Long clientId;

    /**
     * 所属仓库
     */
    private Long whseId;

    /**
     * 父级id
     */
    private Long pId;

    /**
     * 排序
     */
    private Integer sortNo;

    /**
     * 种类编码
     */
    private String categoryCode;

    /**
     * 种类名称
     */
    private String categoryName;

    /**
     * 种类图标
     */
    private String categoryIcon;

    /**
     * 级别
     */
    private String level;

    /**
     * 全路径
     */
    private String fullPath;

    /**
     * 全路径名称
     */
    private String fullPathName;

    /**
     * 备注
     */
    private String remark;

    /**
     * 删除状态 0:未删除，1：已删除
     */
    private Integer deleted;

    /**
     * 创建人
     */
    private String createBy;

    /**
     * 创建人姓名
     */
    private String createByName;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 修改人
     */
    private String modifyBy;

    /**
     * 创建人姓名
     */
    private String modifyByName;

    /**
     * 更新时间
     */
    private LocalDateTime modifyTime;

    /**
     * 版本号
     */
    private Integer version;


}
