package com.nti56.dcm.server.controller;


import com.nti56.common.util.R;
import com.nti56.dcm.server.model.dto.DeviceExcelWithTypeTemplateDto;
import com.nti56.dcm.server.model.dto.DeviceImportErrorDto;
import com.nti56.dcm.server.schedule.DeviceSchedule;
import com.nti56.dcm.server.service.IPmoDeviceService;
import io.swagger.v3.oas.annotations.Operation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <p>
 * pmo生成设备台账信息 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-16
 */
@RestController
@RequestMapping("/pmoDevice")
@Slf4j
public class PmoDeviceController {

    @Autowired
    private IPmoDeviceService pmoDeviceService;
    @Autowired
    private DeviceSchedule deviceSchedule;

    @GetMapping("/generate")
    @Operation(summary = "生成pmo设备台账")
    public void generate() {
        pmoDeviceService.generatePmoDevice();
    }

    @GetMapping("/syncPmoDevice")
    @Operation(summary = "从Etl平台同步pmo设备台账")
    public void syncPmoDevice() {
        try {
            deviceSchedule.syncPmoDevice();
        } catch (Exception e) {
            log.error("从Etl平台同步pmo数据出现异常",e);
        }
    }

    @PostMapping("/import")
    @Operation(summary = "批量引入pmo设备台账信息")
    public R<List<DeviceImportErrorDto>> importPmoDevice(@RequestBody List<DeviceExcelWithTypeTemplateDto> list) {
        return R.result(pmoDeviceService.importPmoDevice(list));
    }
}
