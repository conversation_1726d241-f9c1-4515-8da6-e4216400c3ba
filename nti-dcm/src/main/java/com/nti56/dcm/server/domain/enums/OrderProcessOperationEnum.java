package com.nti56.dcm.server.domain.enums;

import lombok.Getter;

@Getter
public enum OrderProcessOperationEnum {
    BEGIN("1", "BEGIN", "发起"),
    DISPATCH("2", "DISPATCH", "派工"),
    WITHDRAW_DISPATCH("3", "WITHDRAW_DISPATCH", "撤回派工"),
    FORWARD("4", "FORWA<PERSON>", "转交"),
    EXECUTE("5", "EXECUTE", "执行"),
    WITHDRAW_EXECUTE("6", "WITHDRAW_EXECUTE", "撤回执行"),
    ACCEPTANCE("7", "ACCEPTANCE", "验收通过"),
    ACCEPTANCE_FAILED("8", "ACCEPTANCE_FAILED", "验收不通过"),
    AUTO_ACCEPT("9", "AUTO_ACCEPT", "自动验收通过"),
    WITHDRAW("10", "WITHDRAW", "验收驳回"),
    TERMINATE("11", "TERMINATE", "撤销"),
    VENDOR_WITHDRAW("12", "VENDOR_WITHDRAW", "供应商验收驳回"),
    CUSTOMER_WITHDRAW("13", "CUSTOMER_WITHDRAW", "客户验收驳回"),
    VENDOR_ACCEPTANCE("14", "VENDOR_ACCEPTANCE", "供应商验收通过"),
    VENDOR_AUTO_ACCEPT("15", "VENDOR_AUTO_ACCEPT", "供应商自动验收通过"),
    WITHDRAW_RECEIVE("16", "WITHDRAW_RECEIVE", "撤回接收工单"),
    RECEIVE("17", "RECEIVE", "接收工单"),

    ;

    @Getter
    private String value;

    @Getter
    private String name;

    @Getter
    private String nameDesc;

    OrderProcessOperationEnum(String value, String name, String nameDesc) {
        this.value = value;
        this.name = name;
        this.nameDesc = nameDesc;
    }

    public static OrderProcessOperationEnum typeOfValue(String value){
        OrderProcessOperationEnum[] values = OrderProcessOperationEnum.values();
        for (OrderProcessOperationEnum v : values) {
            if (v.value.equals(value)) {
                return v;
            }
        }
        return null;
    }

    public static OrderProcessOperationEnum typeOfName(String name){
        OrderProcessOperationEnum[] values = OrderProcessOperationEnum.values();
        for (OrderProcessOperationEnum v : values) {
            if (v.name.equals(name)) {
                return v;
            }
        }
        return null;
    }

    public static OrderProcessOperationEnum typeOfNameDesc(String nameDesc){
        OrderProcessOperationEnum[] values = OrderProcessOperationEnum.values();
        for (OrderProcessOperationEnum v : values) {
            if (v.nameDesc.equals(nameDesc)) {
                return v;
            }
        }
        return null;
    }
}
