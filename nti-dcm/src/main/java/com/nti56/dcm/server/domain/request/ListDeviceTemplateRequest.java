package com.nti56.dcm.server.domain.request;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.Version;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 设备模板表 实体类
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @Ddate 2023-03-07 17:33:19
 * @since JDK 1.8
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ListDeviceTemplateRequest {
    private Long id;

    /**
     * 设备模板名称
     */
    private String name;

    /**
     * 文件大小
     */
    private Long docSize;

    /**
     * 生成类型 0本地 1导入
     */
    private Integer generateType;

    /**
     * 下载链接
     */
    private String downloadLink;

    /**
     * 平台版本
     */
    private String platformVersion;

    /**
     * 模板描述
     */
    private String descript;

    /**
     * 继承物模型名称
     */
    private String inheritThingModelName;

    /**
     * 通道类型
     */
    private String channelType;

    /**
     * 模板标签数量
     */
    private Integer labelCount;

    /**
     * 版本号
     */
    private Integer version;

    /**
     * 逻辑删除，1-删除
     */
    private Boolean deleted;

    /**
     * 创建人ID
     */
    private Long creatorId;

    /**
     * 创建人
     */
    private String creator;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 创建人ID
     */
    private Long updatorId;

    /**
     * 创建人
     */
    private String updator;

    /**
     * 创建时间
     */
    private LocalDateTime updateTime;

    /**
     * 租户id
     */
    private Long tenantId;

    /**
     * 工程ID
     */
    private Long engineeringId;

    /**
     * 模块ID
     */
    private Long moduleId;

    /**
     * 空间ID
     */
    private Long spaceId;


}
