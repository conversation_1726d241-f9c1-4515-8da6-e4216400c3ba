package com.nti56.dcm.server.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.nti56.common.util.R;
import com.nti56.dcm.server.domain.enums.DeviceMaintenanceOperationTypeEnum;
import com.nti56.dcm.server.entity.OutboundOrderEntity;
import com.nti56.dcm.server.model.dto.*;
import com.nti56.dcm.server.model.vo.InspectPlanVo;
import com.nti56.dcm.server.service.IOutboundOrderService;
import com.nti56.nlink.common.dto.TenantIsolation;
import com.nti56.nlink.common.util.BeanUtilsIntensifier;
import com.nti56.nlink.common.util.PageParam;
import com.nti56.nlink.common.util.Result;
import com.nti56.nlink.common.util.ServiceCodeEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Objects;


/**
 * 出库单 控制器
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2024-05-09 16:54:02
 * @since JDK 1.8
 */
@RestController
@RequestMapping("outbound")
@Slf4j
public class OutboundOrderController {

    @Autowired
    private IOutboundOrderService service;

    /**
     * 领用出库单列表
     */
    @GetMapping("list/{belongOrderType}/{belongOrderId}")
    public R<List<OutboundOrderDetailDto>> listByOrder(@RequestHeader("dcm_headers") TenantIsolation tenant, @PathVariable Long belongOrderId, @PathVariable Integer belongOrderType ){
        DeviceMaintenanceOperationTypeEnum operationType = DeviceMaintenanceOperationTypeEnum.typeOfValue(belongOrderType);
        if (Objects.isNull(operationType)) {
            return R.error("工单类型错误");
        }
        Result<List<OutboundOrderDetailDto>> result = service.listByOrder(tenant.getTenantId(),tenant.getIdType(), belongOrderId, operationType);
        return R.result(result);
    }

    /**
     * 获取出库单分页
     * @param pageParam 分页参数
     */
    @GetMapping("page")
    public R<Page<InspectPlanVo>> page(
            @RequestHeader("dcm_headers") TenantIsolation tenant,
            PageParam pageParam,
            InspectPlanParams params
    ){
        Page<OutboundOrderEntity> page = pageParam.toPage(OutboundOrderEntity.class);
//        Result<Page<InspectPlanVo>> result = service.getPage(tenant.getTenantId(), tenant.getIdType(), params, page);
//        return R.result(result);
        return R.ok();
    }

    /**
     * 创建出库单
     */
    @PostMapping("create")
    public R<OutboundOrderEntity> create(@RequestHeader("dcm_headers") TenantIsolation tenant, @RequestBody OutboundOrderDto dto){
        Result<OutboundOrderEntity> result = service.create(tenant.getTenantId(), tenant.getIdType(), dto);
        return R.result(result);
    }

    /**
     * 开始出库单
     * @param entityId 出库单id
     */
    @PutMapping("start/{entityId}")
    public R<Void> startOrder(@RequestHeader("dcm_headers") TenantIsolation tenant, @PathVariable Long entityId){
        Result<Void> result = service.startOrder(tenant.getTenantId(), tenant.getIdType(), entityId);
        return R.result(result);
    }

    /**
     * 获取出库单详情
     * @param entityId 对象id
     */
    @GetMapping("{entityId}")
    public R<OutboundOrderDetailDto> get(@RequestHeader("dcm_headers") TenantIsolation tenant, @PathVariable Long entityId){
        Result<OutboundOrderDetailDto> result = service.getDetailById(tenant.getTenantId(), tenant.getIdType(), entityId);
        return R.result(result);
    }


     /**
      * 更新对象
      */
     @PutMapping("update")
     public R<Void> update(@RequestHeader("dcm_headers") TenantIsolation tenant, @RequestBody OutboundOrderDto orderDto){
         if (BeanUtilsIntensifier.checkBeanAndProperties(orderDto, OutboundOrderDto::getId)) {
             return R.error(ServiceCodeEnum.CODE_PARAM_ERROR.getCode());
         }
         Result<Void> result = service.update(tenant.getTenantId(), tenant.getIdType(), orderDto);
         return R.result(result);
     }

     /**
      * 删除对象
      * @param entityId 对象id
      */
     @DeleteMapping("{entityId}")
     public R<Void> delete(@RequestHeader("dcm_headers") TenantIsolation tenant, @PathVariable Long entityId){
         Result<Void> result = service.deleteById(tenant.getTenantId(), tenant.getIdType(), entityId);
         return R.result(result);
     }

    
}
