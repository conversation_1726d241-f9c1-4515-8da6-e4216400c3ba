package com.nti56.dcm.server.domain.enums;

import lombok.Getter;

public enum OrderCreateStrategyEnum {
    BEFORE(1, "BEFORE", "提前生成"),
    IMMEDIATE(2, "IMMEDIATE", "立即生成"),
    ;

    @Getter
    private Integer value;

    @Getter
    private String name;

    @Getter
    private String nameDesc;

    OrderCreateStrategyEnum(Integer value, String name, String nameDesc) {
        this.value = value;
        this.name = name;
        this.nameDesc = nameDesc;
    }

    public static OrderCreateStrategyEnum typeOfValue(Integer value){
        OrderCreateStrategyEnum[] values = OrderCreateStrategyEnum.values();
        for (OrderCreateStrategyEnum v : values) {
            if (v.value.equals(value)) {
                return v;
            }
        }
        return null;
    }

    public static OrderCreateStrategyEnum typeOfName(String name){
        OrderCreateStrategyEnum[] values = OrderCreateStrategyEnum.values();
        for (OrderCreateStrategyEnum v : values) {
            if (v.name.equals(name)) {
                return v;
            }
        }
        return null;
    }

    public static OrderCreateStrategyEnum typeOfNameDesc(String nameDesc){
        OrderCreateStrategyEnum[] values = OrderCreateStrategyEnum.values();
        for (OrderCreateStrategyEnum v : values) {
            if (v.nameDesc.equals(nameDesc)) {
                return v;
            }
        }
        return null;
    }
}
