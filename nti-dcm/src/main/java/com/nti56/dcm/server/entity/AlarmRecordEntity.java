package com.nti56.dcm.server.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * <p>
 * 告警记录表 实体类
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @Ddate 2024-07-11 14:56:11
 * @since JDK 1.8
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@TableName("alarm_record")
public class AlarmRecordEntity implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
    * id
    */
    private Long id;

    /**
    * 租户id
    */
    @TableField(fill = FieldFill.INSERT)
    private Long tenantId;

    /**
    * 告警类型， 1-故障设备，2-设备离线，3-低健康设备，4-备件库存预警
    */
    private Integer alarmType;

    /**
    * 告警时间
    */
    private LocalDateTime alarmTime;

    /**
     * 告警结束时间
     */
    private LocalDateTime alarmEndTime;

    /**
    * 告警持续时长
    */
    private String alarmDuration;

    /**
    * 设备id
    */
    private Long deviceId;

    /**
    * 故障等级 1：一般， 2：警告， 3：严重， 4：致命
    */
    private Integer faultLevel;

    /**
    * 故障描述
    */
    private String faultDesc;

    /**
     * 故障事件
     */
    private String faultEvent;

    /**
    * 健康度
    */
    private Integer heathLevel;

    /**
     * 工单id
     */
    private Long orderId;

    /**
    * 工单编号
    */
    private String orderNo;

    /**
     * 委外工单客户ID
     */
    private Long orderCustomerId;

    /**
    * 工单来源，1-维修工单，2-保养工单，3-巡检工单，4-故障报修
    */
    private Integer orderSource;

    /**
    * 工单进度，1-待分派，2-待接收，3-执行中，4-待验收，5-待处理
    */
    private Integer orderStatus;

    /**
    * 版本号
    */
    @Version
    private Integer version;

    /**
     * 所属身份,1-供应商，2-客户
     */
    private Integer idType;
    /**
    * 删除
    */
    @TableLogic
    private Integer deleted;

    /**
    * 创建人ID
    */
    private Long creatorId;

    /**
    * 创建人
    */
    @TableField(fill = FieldFill.INSERT)
    private String creator;

    /**
    * 创建时间
    */
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
    * 更新人ID
    */
    private Long updatorId;

    /**
    * 更新人
    */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private String updator;

    /**
    * 更新时间
    */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    /**
    * 工程ID
    */
    @TableField(fill = FieldFill.INSERT)
    private Long engineeringId;

    /**
    * 模块ID
    */
    @TableField(fill = FieldFill.INSERT)
    private Long moduleId;

    /**
    * 空间ID
    */
    @TableField(fill = FieldFill.INSERT)
    private Long spaceId;

    /**
     * 状态，0-待处理，1-已处理
     */
    private Integer status;

    /**
     * 结束状态，0-未结束，1-已结束
     */
    private Integer isFinish;

    /**
     * 计划到期时间
     */
    private LocalDate planExpireDate;

    /**
     * 到期描述
     */
    private String expireDesc;

    /**
     * 备件名称
     */
    private String spareName;

    /**
     * 备件编码
     */
    private String spareNo;

    /**
     * 备件告警类型
     */
    private String spareAlarmType;

    /**
     * 备件库存数量
     */
    private BigDecimal spareQty;

    /**
     * 故障代码
     */
    private String faultCode;

}
