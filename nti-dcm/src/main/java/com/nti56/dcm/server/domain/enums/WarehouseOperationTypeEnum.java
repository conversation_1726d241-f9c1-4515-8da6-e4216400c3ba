package com.nti56.dcm.server.domain.enums;

import lombok.Getter;

@Getter
public enum WarehouseOperationTypeEnum {
    IN(1, "入库", ""),
    OUT(2, "出库", ""),
    ;

    private Integer value;

    private String name;

    private String nameDesc;

    WarehouseOperationTypeEnum(Integer value, String name, String nameDesc) {
        this.value = value;
        this.name = name;
        this.nameDesc = nameDesc;
    }

    public static WarehouseOperationTypeEnum typeOfValue(Integer value){
        WarehouseOperationTypeEnum[] values = WarehouseOperationTypeEnum.values();
        for (WarehouseOperationTypeEnum v : values) {
            if (v.value.equals(value)) {
                return v;
            }
        }
        return null;
    }

    public static WarehouseOperationTypeEnum typeOfName(String name){
        WarehouseOperationTypeEnum[] values = WarehouseOperationTypeEnum.values();
        for (WarehouseOperationTypeEnum v : values) {
            if (v.name.equals(name)) {
                return v;
            }
        }
        return null;
    }

    public static WarehouseOperationTypeEnum typeOfNameDesc(String nameDesc){
        WarehouseOperationTypeEnum[] values = WarehouseOperationTypeEnum.values();
        for (WarehouseOperationTypeEnum v : values) {
            if (v.nameDesc.equals(nameDesc)) {
                return v;
            }
        }
        return null;
    }
}
