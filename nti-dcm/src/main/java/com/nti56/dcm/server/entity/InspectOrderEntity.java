package com.nti56.dcm.server.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import java.time.LocalDateTime;
import lombok.Data;

/**
 * <p>
 * 点巡检工单表 实体类
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @Ddate 2024-02-23 15:13:54
 * @since JDK 1.8
 */
@Data
@TableName("inspect_order")
public class InspectOrderEntity extends BaseEntity{
        /**
        * 所属计划id
        */
        private Long inspectPlanId;

        /**
        * 工单编码
        */
        private String orderNumber;

        /**
        * 工单状态，1-待分派，2-待接收，3-执行中，4-待验收，5-验收通过，6-验收不通过，7-已撤销
        */
        private Integer status;

        /**
        * 计划执行时间
        */
        private LocalDateTime planInspectTime;

        /**
        * 是否委外，1-是，0-否
        */
        private Integer outsource;

        /**
        * 执行人id
        */
        private Long executeUserId;

        /**
        * 委外供应商id
        */
        private Long supplierId;

        /**
        * 委外供应商名称
        */
        private String supplierName;

        // /**
        // * 检查开始时间
        // */
        // private LocalDateTime inspectBegin;

        // /**
        // * 检查束时间
        // */
        private LocalDateTime inspectEnd;

        /**
        * 委外客户id
        */
        private Long customerId;

        /**
        * 委外客户名称
        */
        private String customerName;

        /**
        * 检查类型，1-点检，2-巡检
        */
        private Integer inspectType;

        /**
        * 复制工单id
        */
        private Long copyOrderId;
        
        /**
         * 创建来源 1-供应商/集成商 2-客户
         */
        private Integer createSource;
        
        /**
         * 工单等级 1-紧急 2-重要 3-普通 4-其他
         */
        private Integer level;

        /**
         * 是否超时 1-正常 2-超时
         */
        private Integer timeout;

        /**
         * 是否逾期 0-未逾期，1-逾期
         */
        private Integer overdue;

        /**
         * 审批流程唯一标识
         */
        private String processInstanceId;

        /**
        * 外委供应商类型：1-租户型，2 数据型
        */
        private Integer outsourceSupplierType;

        /**
        * 外委客户类型：1-租户型，2 数据型
        */
        private Integer outsourceCustomerType;

}
