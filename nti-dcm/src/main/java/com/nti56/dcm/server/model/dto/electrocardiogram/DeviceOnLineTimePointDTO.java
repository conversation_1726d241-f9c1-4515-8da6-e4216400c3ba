package com.nti56.dcm.server.model.dto.electrocardiogram;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;


/**
 * 设备在线时间点DTO
 * 
 * <AUTHOR>
 * @version 1.0
 * @since JDK 1.8
 */
@Data
public class DeviceOnLineTimePointDTO {
    
    /**
     * 开始时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss'Z'", timezone = "UTC")
    private String _start;
    
    /**
     * 时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss'Z'", timezone = "UTC")
    private String _time;
    
    /**
     * 在线总秒数
     */
    private String onlineSumSeconds;
    
    /**
     * 在线总时长字符串格式
     */
    private String onlineSumStr;
    
    /**
     * 花费时间
     */
    private Double spend;
}
