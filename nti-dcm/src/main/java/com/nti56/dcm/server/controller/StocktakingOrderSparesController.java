package com.nti56.dcm.server.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.nti56.nlink.common.dto.TenantIsolation;
import com.nti56.nlink.common.util.BeanUtilsIntensifier;
import com.nti56.nlink.common.util.PageParam;
import com.nti56.common.util.R;
import com.nti56.nlink.common.util.Result;
import com.nti56.nlink.common.util.ServiceCodeEnum;
import com.nti56.dcm.server.service.StocktakingOrderSparesService;
import com.nti56.dcm.server.entity.StocktakingOrderSparesEntity;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Optional;
import java.util.function.Function;

import lombok.extern.slf4j.Slf4j;


/**
 * 盘点货位表 控制器
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2024-05-08 13:49:40
 * @since JDK 1.8
 */
@RestController
@RequestMapping("/stocktaking_order_spares")
@Slf4j
public class StocktakingOrderSparesController {

    @Autowired
    private StocktakingOrderSparesService service;

    /**
     * 获取分页
     * @param pageParam 分页参数
     */
    @GetMapping("/page")
    public R<Page<StocktakingOrderSparesEntity>> page(@RequestHeader("dcm_headers") TenantIsolation tenant, PageParam pageParam,StocktakingOrderSparesEntity entity){
        Page<StocktakingOrderSparesEntity> page = pageParam.toPage(StocktakingOrderSparesEntity.class);
        Result<Page<StocktakingOrderSparesEntity>> result = service.getPage(tenant.getTenantId(), entity,page);
        return R.result(result);
    }

    /**
     * 获取列表
     */
    @GetMapping("/list")
    public R<List<StocktakingOrderSparesEntity>> list(@RequestHeader("dcm_headers") TenantIsolation tenant, StocktakingOrderSparesEntity entity){
        Result<List<StocktakingOrderSparesEntity>> result = service.list(tenant.getTenantId(), entity);
        return R.result(result);
    }

    /**
     * 创建对象
     */
    @PostMapping("/create")
    public R<StocktakingOrderSparesEntity> create(@RequestHeader("dcm_headers") TenantIsolation tenant, @RequestBody StocktakingOrderSparesEntity entity){
        Result<StocktakingOrderSparesEntity> result = service.save(tenant.getTenantId(), entity);
        return R.result(result);
    }

    /**
     * 更新对象
     */
    @PutMapping("/update")
    public R<Void> update(@RequestHeader("dcm_headers") TenantIsolation tenant, @RequestBody StocktakingOrderSparesEntity entity){
        if (BeanUtilsIntensifier.checkBeanAndProperties(entity, StocktakingOrderSparesEntity::getId)) {
            return R.error(ServiceCodeEnum.CODE_PARAM_ERROR.getCode());
        }
        Result<Void> result = service.update(tenant.getTenantId(), entity);
        return R.result(result);
    }

    /**
     * 删除对象
     * @param entityId 对象id
     */
    @DeleteMapping("/{entityId}")
    public R<Void> delete(@RequestHeader("dcm_headers") TenantIsolation tenant, @PathVariable Long entityId){
        Result<Void> result = service.deleteById(tenant.getTenantId(), entityId);
        return R.result(result);
    }

    /**
     * 获取对象
     * @param entityId 对象id
     */
    @GetMapping("/{entityId}")
    public R<StocktakingOrderSparesEntity> get(@RequestHeader("dcm_headers") TenantIsolation tenant, @PathVariable Long entityId){
        Result<StocktakingOrderSparesEntity> result = service.getById(tenant.getTenantId(), entityId);
        return R.result(result);
    }
    
}
