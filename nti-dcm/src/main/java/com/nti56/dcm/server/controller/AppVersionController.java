package com.nti56.dcm.server.controller;


import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.nti56.common.util.R;
import com.nti56.dcm.server.entity.AppVersion;
import com.nti56.dcm.server.model.dto.AppVersionDto;
import com.nti56.dcm.server.service.IAppVersionService;
import com.nti56.nlink.common.dto.TenantIsolation;
import com.nti56.nlink.common.util.PageParam;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;

/**
 * <p>
 * 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-01-24
 */
@RestController
@RequestMapping("/app-version")
@Tag(name = "app版本模块")
@Slf4j
public class AppVersionController {

    @Autowired
    private IAppVersionService appVersionService;

    @GetMapping("page")
    @Operation(summary = "获取app版本分页")
    public R<Page<AppVersion>> createAppVersion(@RequestHeader("dcm_headers") TenantIsolation tenantIsolation, PageParam pageParam, AppVersionDto versionInfo) {
        Page<AppVersion> page = pageParam.toPage(AppVersion.class);
        return R.result(appVersionService.getPage(versionInfo, page, tenantIsolation));
    }

    @PostMapping("")
    @Operation(summary = "新增版本信息")
    public R<AppVersion> createAppVersion(@RequestHeader("dcm_headers") TenantIsolation tenantIsolation,
                                          @RequestBody @Validated AppVersionDto versionInfo) {
        return R.result(appVersionService.create(versionInfo, tenantIsolation));
    }

    @GetMapping("last-version")
    @Operation(summary = "获取最新app版本")
    public R<AppVersion> getLastVersion(@RequestHeader("dcm_headers") TenantIsolation tenantIsolation) {
        return R.result(appVersionService.getLastVersion(tenantIsolation));
    }

    @PutMapping("/{id}")
    @Operation(summary = "修改app版本信息")
    public R<Void> editAppVersion(@RequestHeader("dcm_headers") TenantIsolation tenantIsolation,
                                  @RequestBody @Validated AppVersionDto versionInfo) {
        return R.result(appVersionService.editAppVersion(versionInfo, tenantIsolation));
    }

    @DeleteMapping("/{id}")
    @Operation(summary = "根据id删除app版本信息")
    public R deleteAppVersion(@RequestHeader("dcm_headers") TenantIsolation tenantIsolation,
                              @PathVariable Long id) {
        return R.result(appVersionService.deleteAppVersion(id, tenantIsolation));
    }


    @GetMapping("/getQRCode")
    public void getQRCode(HttpServletResponse response) throws IOException {
        appVersionService.getQRCode(response);
    }
}
