package com.nti56.dcm.server.domain.enums;

import lombok.Getter;

public enum AlarmTypeEnum {
    DEVICE_ERROR(1, "device_error", "设备故障"),
    DEVICE_OFFLINE(2, "device_offline", "设备离线"),
    LOW_HEALTH_DEVICE(3, "low_health_device", "低健康设备"),
    SPARES_STOCK(4, "spares_stock", "备件库存预警"),
    ORDER_TIMEOUT(5, "order_timeout", "超时工单"),
    RECEIVE_ORDER(6, "receive_order", "收到新工单"),
    DEVICE_MAINTENANCE_TIMEOUT(7, "device_maintenance_timeout", "设备超时未保养"),

    DEVICE_MAINTENANCE_ADVENT(8, "device_maintenance_advent", "设备维保临期"),
    DEVICE_MAINTENANCE_EXPIRE(9, "device_maintenance_expire", "设备维保到期"),
    DEVICE_WARRANTY_ADVENT(10, "device_warranty_advent", "设备质保临期"),
    DEVICE_WARRANTY_EXPIRE(11, "device_warranty_expire", "设备质保到期"),
    DEVICE_SCRAP_ADVENT(12, "device_scrap_advent", "设备预计报废临期"),
    DEVICE_SCRAP_EXPIRE(13, "device_scrap_expire", "设备预计报废到期"),
    DEVICE_ONLINE(14, "device_online", "设备在线"),
    DEVICE_REPAIR(21, "device_repair", "设备告警"),
    ;

    @Getter
    private Integer value;

    @Getter
    private String name;

    @Getter
    private String nameDesc;

    AlarmTypeEnum(Integer value, String name, String nameDesc) {
        this.value = value;
        this.name = name;
        this.nameDesc = nameDesc;
    }

    public static AlarmTypeEnum typeOfValue(Integer value){
        AlarmTypeEnum[] values = AlarmTypeEnum.values();
        for (AlarmTypeEnum v : values) {
            if (v.value.equals(value)) {
                return v;
            }
        }
        return null;
    }

    public static AlarmTypeEnum typeOfName(String name){
        AlarmTypeEnum[] values = AlarmTypeEnum.values();
        for (AlarmTypeEnum v : values) {
            if (v.name.equals(name)) {
                return v;
            }
        }
        return null;
    }

    public static AlarmTypeEnum typeOfNameDesc(String nameDesc){
        AlarmTypeEnum[] values = AlarmTypeEnum.values();
        for (AlarmTypeEnum v : values) {
            if (v.nameDesc.equals(nameDesc)) {
                return v;
            }
        }
        return null;
    }
}
