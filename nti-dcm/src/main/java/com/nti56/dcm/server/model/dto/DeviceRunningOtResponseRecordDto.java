package com.nti56.dcm.server.model.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;


@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder

// 记录类
public class DeviceRunningOtResponseRecordDto {
    private String table;
    private DeviceRunningOtResponseRecordValueDto values;
    private String time;
    private String start;
    private String stop;
    private String measurement;
    private String value;
    private String field;

    // setter 和 getter 方法
    public String getTable() {
        return table;
    }

    public void setTable(String table) {
        this.table = table;
    }

    public DeviceRunningOtResponseRecordValueDto getValues() {
        return values;
    }

    public void setValues(DeviceRunningOtResponseRecordValueDto values) {
        this.values = values;
    }

    public String getTime() {
        return time;
    }

    public void setTime(String time) {
        this.time = time;
    }

    public String getStart() {
        return start;
    }

    public void setStart(String start) {
        this.start = start;
    }

    public String getStop() {
        return stop;
    }

    public void setStop(String stop) {
        this.stop = stop;
    }

    public String getMeasurement() {
        return measurement;
    }

    public void setMeasurement(String measurement) {
        this.measurement = measurement;
    }

    public String getValue() {
        return value;
    }

    public void setValue(String value) {
        this.value = value;
    }

    public String getField() {
        return field;
    }

    public void setField(String field) {
        this.field = field;
    }
}