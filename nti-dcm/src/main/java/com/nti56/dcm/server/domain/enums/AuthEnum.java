package com.nti56.dcm.server.domain.enums;

import lombok.Getter;

public enum AuthEnum {
    REPORT_REPAIR_HANDLE("reportRepairHandle", "设备保修处理"),
    ;

    @Getter
    private String name;

    @Getter
    private String nameDesc;

    AuthEnum(String name, String nameDesc) {
        this.name = name;
        this.nameDesc = nameDesc;
    }

    public static AuthEnum typeOfName(String name){
        AuthEnum[] values = AuthEnum.values();
        for (AuthEnum v : values) {
            if (v.name.equals(name)) {
                return v;
            }
        }
        return null;
    }

    public static AuthEnum typeOfNameDesc(String nameDesc){
        AuthEnum[] values = AuthEnum.values();
        for (AuthEnum v : values) {
            if (v.nameDesc.equals(nameDesc)) {
                return v;
            }
        }
        return null;
    }
}
