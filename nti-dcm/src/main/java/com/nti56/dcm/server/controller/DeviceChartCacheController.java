package com.nti56.dcm.server.controller;

import java.util.List;
import java.util.Map;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;

import com.nti56.dcm.server.model.vo.CustomerDeviceStateCountVo;
import com.nti56.dcm.server.model.vo.DeviceBlockChartVo;
import com.nti56.dcm.server.model.vo.DeviceStateCountVo;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.nti56.common.util.R;
import com.nti56.dcm.server.model.vo.LocationDeviceStateCountVo;
import com.nti56.dcm.server.model.vo.PageWithStateCount;
import com.nti56.dcm.server.model.vo.PropertyValueWithTime;
import com.nti56.dcm.server.model.vo.StateValueWithTime;
import com.nti56.dcm.server.model.vo.TypeDeviceStateCountVo;
import com.nti56.dcm.server.domain.enums.DeviceCondEnum;
import com.nti56.dcm.server.domain.enums.DeviceOrderByEnum;
import com.nti56.dcm.server.domain.enums.DeviceStateEnum;
import com.nti56.dcm.server.model.result.ITResult;
import com.nti56.dcm.server.model.vo.DeviceStateVo;
import com.nti56.dcm.server.model.vo.DeviceVo;
import com.nti56.dcm.server.model.vo.FileVo;
import com.nti56.dcm.server.service.DeviceChartCacheService;
import com.nti56.dcm.server.service.DeviceChartService;
import com.nti56.nlink.common.dto.TenantIsolation;
import com.nti56.nlink.common.util.Result;

import io.swagger.v3.oas.annotations.Parameter;
import lombok.extern.slf4j.Slf4j;

/**
 * 类说明: 设备心电图缓存控制器
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2023-12-21 10:19:55
 * @since JDK 1.8
 */
@RestController
@RequestMapping("device-chart/cache")
@Slf4j
public class DeviceChartCacheController {

    @Autowired
    DeviceChartCacheService deviceChartCacheService;

    /**
     * 缓存查询所有设备利用率
     * 
     * @param begin 开始时间，格式：2023-12-26T07:00:00
     * @param end 结束时间，格式：2023-12-26T07:00:00
     */
    @GetMapping("/queryUseRateOfAllDevice")
    public R<Object> queryUseRateOfAllDevice(
        @RequestHeader("dcm_headers") TenantIsolation tenant, 
        @RequestParam("begin") String begin,
        @RequestParam("end") String end
    ){
        ITResult<Object> result = deviceChartCacheService.queryUseRateOfAllDevice(tenant , begin, end);
        return R.result(result);
    }

    /**
     * 缓存查询所有设备空闲率
     * 
     * @param begin 开始时间，格式：2023-12-26T07:00:00
     * @param end 结束时间，格式：2023-12-26T07:00:00
     */
    @GetMapping("/queryStandByRateOfAllDevice")
    public R<Object> queryStandByRateOfAllDevice(
        @RequestHeader("dcm_headers") TenantIsolation tenant, 
        @RequestParam("begin") String begin,
        @RequestParam("end") String end
    ){
        ITResult<Object> result = deviceChartCacheService.queryStandByRateOfAllDevice(tenant , begin, end);
        return R.result(result);
    }

    /**
     * 缓存查询所有设备故障率
     * 
     * @param begin 开始时间，格式：2023-12-26T07:00:00
     * @param end 结束时间，格式：2023-12-26T07:00:00
     */
    @GetMapping("/queryFaultRateOfAllDevice")
    public R<Object> queryFaultRateOfAllDevice(
        @RequestHeader("dcm_headers") TenantIsolation tenant, 
        @RequestParam("begin") String begin,
        @RequestParam("end") String end
    ){
        ITResult<Object> result = deviceChartCacheService.queryFaultRateOfAllDevice(tenant , begin, end);
        return R.result(result);
    }

    /**
     * 缓存查询所有设备累计故障次数TopN
     * 
     * @param begin 开始时间，格式：2023-12-26T07:00:00
     * @param end 结束时间，格式：2023-12-26T07:00:00
     */
    @GetMapping("/queryDevicesFaultCountTopnOfAllDevice")
    public R<Object> queryDevicesFaultCountTopnOfAllDevice(
        @RequestHeader("dcm_headers") TenantIsolation tenant, 
        @RequestParam("begin") String begin,
        @RequestParam("end") String end,
        @RequestParam(value = "n", defaultValue = "10") Integer n
    ){
        ITResult<Object> result = deviceChartCacheService.queryDevicesFaultCountTopnOfAllDevice(tenant , begin, end, n);
        return R.result(result);
    }

    /**
     * 缓存查询所有设备累计故障时长TopN
     * 
     * @param begin 开始时间，格式：2023-12-26T07:00:00
     * @param end 结束时间，格式：2023-12-26T07:00:00
     */
    @GetMapping("/queryDevicesFaultTimeSumTopnOfAllDevice")
    public R<Object> queryDevicesFaultTimeSumTopnOfAllDevice(
        @RequestHeader("dcm_headers") TenantIsolation tenant, 
        @RequestParam("begin") String begin,
        @RequestParam("end") String end,
        @RequestParam(value = "n", defaultValue = "10") Integer n
    ){
        ITResult<Object> result = deviceChartCacheService.queryDevicesFaultTimeSumTopnOfAllDevice(tenant , begin, end, n);
        return R.result(result);
    }



}
