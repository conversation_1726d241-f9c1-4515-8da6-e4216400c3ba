package com.nti56.dcm.server.domain.enums;

import lombok.Getter;

public enum IdTypeEnum {
    SUPPLIER(1, "supplier", "供应商"),
    CUSTOMER(2, "CUSTOMER", "业主"),
    ;

    @Getter
    private Integer value;

    @Getter
    private String name;

    @Getter
    private String nameDesc;

    IdTypeEnum(Integer value, String name, String nameDesc) {
        this.value = value;
        this.name = name;
        this.nameDesc = nameDesc;
    }

    public static IdTypeEnum typeOfValue(Integer value){
        IdTypeEnum[] values = IdTypeEnum.values();
        for (IdTypeEnum v : values) {
            if (v.value.equals(value)) {
                return v;
            }
        }
        return null;
    }

    public static IdTypeEnum typeOfName(String name){
        IdTypeEnum[] values = IdTypeEnum.values();
        for (IdTypeEnum v : values) {
            if (v.name.equals(name)) {
                return v;
            }
        }
        return null;
    }

    public static IdTypeEnum typeOfNameDesc(String nameDesc){
        IdTypeEnum[] values = IdTypeEnum.values();
        for (IdTypeEnum v : values) {
            if (v.nameDesc.equals(nameDesc)) {
                return v;
            }
        }
        return null;
    }
}
