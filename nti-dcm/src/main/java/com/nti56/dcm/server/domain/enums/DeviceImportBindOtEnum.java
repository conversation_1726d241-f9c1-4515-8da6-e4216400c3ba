package com.nti56.dcm.server.domain.enums;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.nti56.dcm.server.model.dto.DeviceExcelBindOtDto;
import com.nti56.dcm.server.model.dto.DeviceImportErrorDto;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.function.BiConsumer;
import java.util.function.BiFunction;


public enum DeviceImportBindOtEnum {
    deviceName("deviceName", "设备名称", (obj, importList) -> {
        String deviceName = (String) obj;
        if (StrUtil.isNotBlank(deviceName)) {
            if (deviceName.length() > 255) {
                return new DeviceImportErrorDto("设备名称长度大于255，数据有误！");
            }
            if (importList.stream().filter(i -> i.getDeviceName().equals(deviceName)).count() > 1) {
                return new DeviceImportErrorDto("批量绑定的表格数据中存在重复的设备名称！");
            }
        }
        return null;
    }),
    deviceNo("deviceNo", "设备编号", (obj, importList) -> {
        String validField = (String) obj;
        if (StrUtil.isNotBlank(validField) && validField.length() > 255) {
            return new DeviceImportErrorDto("设备编号长度大于255，数据有误！");
        }
        return null;
    }),
    deviceAliasName("deviceAliasName", "物联数据名称", (obj, importList) -> {
        String deviceAliasName = (String) obj;
        if (StrUtil.isBlank(deviceAliasName)) {
            return new DeviceImportErrorDto("物联数据名称为空！");
        }
        if (deviceAliasName.length() > 255) {
            return new DeviceImportErrorDto("物联数据名称长度大于255，数据有误！");
        }
        if (importList.stream().filter(i -> ObjectUtil.equals(deviceAliasName, i.getDeviceAliasName())).count() > 1) {
            return new DeviceImportErrorDto("批量绑定的表格数据中存在重复的物联数据名称！");
        }
        return null;
    }),
    ;
    /**
     * 数据库的列名
     * 类中的变量名
     */
    private String objColName;

    /**
     * 对应的列名（excel）
     */
    private String excelColTitle;

    /**
     * 校验列数据的方法
     */
    private BiFunction<Object, List<DeviceExcelBindOtDto>, DeviceImportErrorDto> function;

    DeviceImportBindOtEnum(String objColName, String excelColTitle, BiFunction<Object, List<DeviceExcelBindOtDto>, DeviceImportErrorDto> function) {
        this.objColName = objColName;
        this.excelColTitle = excelColTitle;
        this.function = function;
    }

    BiConsumer<Object, List<DeviceImportErrorDto>> test = (str, num) -> {
        // 在这里执行操作
    };

    public String getObjColName() {
        return objColName;
    }

    public void setObjColName(String objColName) {
        this.objColName = objColName;
    }

    public String getExcelColTitle() {
        return excelColTitle;
    }

    public void setExcelColTitle(String excelColTitle) {
        this.excelColTitle = excelColTitle;
    }

    public static List<String> getObjColNames() {
        List<String> list = new ArrayList<>();
        DeviceImportBindOtEnum[] values = DeviceImportBindOtEnum.values();
        for (DeviceImportBindOtEnum disctBBuMappingEnum : values) {
            list.add(disctBBuMappingEnum.getObjColName());
        }
        return list;
    }

    public static List<String> getExcelColTitles() {
        List<String> list = new ArrayList<>();
        DeviceImportBindOtEnum[] values = DeviceImportBindOtEnum.values();
        for (DeviceImportBindOtEnum exportEnum : values) {
            list.add(exportEnum.getExcelColTitle());
        }
        return list;
    }


    /**
     * excel列名为key, 对象属性为 value
     *
     * @return
     */
    public static Map<String, String> getObjNameMap() {
        Map<String, String> colMap = new HashMap<>();
        for (DeviceImportBindOtEnum obj : DeviceImportBindOtEnum.values()) {
            colMap.put(obj.getExcelColTitle(), obj.getObjColName());
        }
        return colMap;
    }

    public static DeviceImportErrorDto executeValidFunctionByColName(String objColName, Object value, List<DeviceExcelBindOtDto> importList) {
        if (StrUtil.isBlank(objColName)) {
            return null;
        }
        for (DeviceImportBindOtEnum obj : DeviceImportBindOtEnum.values()) {
            if (obj.objColName.equals(objColName)) {
                return obj.function.apply(value, importList);
            }
        }
        return null;
    }

    public static String getValueStringTemplate() {
        StringBuilder sb = new StringBuilder();
        sb.append("[");
        DeviceImportBindOtEnum[] values = DeviceImportBindOtEnum.values();
        for (int i = 0; i < values.length; i++) {
            DeviceImportBindOtEnum exportEnums = values[i];
            if (i == 0) {
                sb.append(exportEnums.getExcelColTitle());
            } else {
                sb.append(", " + exportEnums.getExcelColTitle());
            }
        }
        sb.append("]");
        return sb.toString();
    }
}
