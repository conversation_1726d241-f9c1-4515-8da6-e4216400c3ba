package com.nti56.dcm.server.domain.enums;

import lombok.Getter;

public enum NoticeTypeEnum {
    DEVICE_TASK(1, "task", "设备任务"),
    DEVICE_STAND_BY(2, "standBy", "设备空闲"),
    DEVICE_FAULT(3, "deviceFault", "设备故障"),
    DEVICE_OFFLINE(4, "deviceOffline", "设备离线"),
    ;

    @Getter
    private Integer value;

    @Getter
    private String name;

    @Getter
    private String nameDesc;

    NoticeTypeEnum(Integer value, String name, String nameDesc) {
        this.value = value;
        this.name = name;
        this.nameDesc = nameDesc;
    }

    public static NoticeTypeEnum typeOfValue(Integer value){
        NoticeTypeEnum[] values = NoticeTypeEnum.values();
        for (NoticeTypeEnum v : values) {
            if (v.value.equals(value)) {
                return v;
            }
        }
        return null;
    }

    public static NoticeTypeEnum typeOfName(String name){
        NoticeTypeEnum[] values = NoticeTypeEnum.values();
        for (NoticeTypeEnum v : values) {
            if (v.name.equals(name)) {
                return v;
            }
        }
        return null;
    }

    public static NoticeTypeEnum typeOfNameDesc(String nameDesc){
        NoticeTypeEnum[] values = NoticeTypeEnum.values();
        for (NoticeTypeEnum v : values) {
            if (v.nameDesc.equals(nameDesc)) {
                return v;
            }
        }
        return null;
    }
}
