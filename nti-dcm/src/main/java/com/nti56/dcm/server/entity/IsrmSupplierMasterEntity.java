package com.nti56.dcm.server.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * <p>
 * 供应商主数据 实体类
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @Ddate 2024-07-31 15:26:44
 * @since JDK 1.8
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@TableName("isrm_supplier_master")
public class IsrmSupplierMasterEntity implements Serializable {
    private static final long serialVersionUID = 1L;
    private String id;

    /**
    * 租户唯一标识
    */
    @TableField(fill = FieldFill.INSERT)
    private String tenantId;

    /**
    * 标准规范编码
    */
    private String ntiId;

    /**
    * 供应商id
    */
    private String supplierId;

    /**
    * 供应商ERP编码
    */
    private String supplierCode;

    /**
    * 供应商名称
    */
    private String supplierName;

    /**
    * 供应商状态：0：陌生供应商、1：潜在供应商、2：合格供应商、3：淘汰供应商
    */
    private String supplierStatus;

    /**
    * 审批状态：0：未审批、1：审批中、2：审批通过、3：审批拒绝
    */
    private String auditStatus;

    /**
    * 流程id
    */
    private String flowId;

    /**
    * 来源类型：0：公开注册、1：邀请注册、2：采购代注册
    */
    private String sourceType;

    /**
    * 供应商类型
    */
    private String supplierType;

    /**
    * 供应商级别ABCD
    */
    private String supplierLevel;

    /**
    * 绩效评级ABCD
    */
    private String performanceLevel;

    /**
    * 币别
    */
    private String currency;

    /**
    * 税码
    */
    private String taxCode;

    /**
    * 账户组
    */
    private String accountGroup;

    /**
    * 付款条件
    */
    private String paymentClause;

    private String fbk1;

    private String fbk2;

    private String fbk3;

    private String fbk4;

    private String fbk5;

    private String fbk6;

    private String fbk7;

    private String fbk8;

    private String fbk9;

    private String fbk10;

    private String fbk11;

    private String fbk12;

    private String fbk13;

    private String fbk14;

    private String fbk15;

    private String fbk16;

    private String fbk17;

    private String fbk18;

    private String fbk19;

    private String fbk20;

    /**
    * 扩展字段
    */
    private String extendField;

    /**
    * 企业简称
    */
    private String shortName;

    /**
    * 企业描述
    */
    private String description;

    /**
    * 行业大类
    */
    private String industryClass;

    /**
    * 行业中类
    */
    private String industryDivision;

    /**
    * 企业性质
    */
    private String kind;

    /**
    * erp系统
    */
    private String erpSource;

    /**
    * 统一社会信息代码
    */
    private String uniformCerCode;

    /**
    * 统一社会证书地址
    */
    private String uniformCerImg;

    /**
    * 企业logo
    */
    private String logoImg;

    /**
    * 邓白氏企业号
    */
    private String dbsEnterpriseCode;

    /**
    * 注册日期
    */
    private LocalDate registrationDate;

    /**
    * 注册资金
    */
    private String registrationCapital;

    /**
    * 国家
    */
    private String country;

    /**
    * 省份
    */
    private String province;

    /**
    * 城市
    */
    private String city;

    /**
    * 区县
    */
    private String county;

    /**
    * 地址
    */
    private String address;

    /**
    * 固话
    */
    private String phone;

    /**
    * 邮件地址
    */
    private String email;

    /**
    * 公司网站
    */
    private String website;

    /**
    * 手机号码
    */
    private String mobile;

    /**
    * 传真
    */
    private String fax;

    /**
    * 法人
    */
    private String corporation;

    /**
    * 删除标识：0：未删除、1：已删除
    */
    private Integer isDeleted;

    /**
    * 创建人
    */
    private String createBy;

    /**
    * 创建时间
    */
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
    * 最后修改人
    */
    private String updateBy;

    /**
    * 最后修改时间
    */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;



}
