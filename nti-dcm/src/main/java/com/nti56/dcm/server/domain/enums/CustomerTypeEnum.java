package com.nti56.dcm.server.domain.enums;

import lombok.Getter;

public enum CustomerTypeEnum {
    INDUSTRY(1, "INDUSTRY", "行业"),
    REGION(2, "REGION", "地域"),
    ;

    @Getter
    private Integer value;

    @Getter
    private String name;

    @Getter
    private String nameDesc;

    CustomerTypeEnum(Integer value, String name, String nameDesc) {
        this.value = value;
        this.name = name;
        this.nameDesc = nameDesc;
    }

    public static CustomerTypeEnum typeOfValue(Integer value){
        CustomerTypeEnum[] values = CustomerTypeEnum.values();
        for (CustomerTypeEnum v : values) {
            if (v.value.equals(value)) {
                return v;
            }
        }
        return null;
    }

    public static CustomerTypeEnum typeOfName(String name){
        CustomerTypeEnum[] values = CustomerTypeEnum.values();
        for (CustomerTypeEnum v : values) {
            if (v.name.equals(name)) {
                return v;
            }
        }
        return null;
    }

    public static CustomerTypeEnum typeOfNameDesc(String nameDesc){
        CustomerTypeEnum[] values = CustomerTypeEnum.values();
        for (CustomerTypeEnum v : values) {
            if (v.nameDesc.equals(nameDesc)) {
                return v;
            }
        }
        return null;
    }
}
