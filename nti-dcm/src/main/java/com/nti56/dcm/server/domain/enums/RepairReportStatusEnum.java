package com.nti56.dcm.server.domain.enums;

import lombok.Getter;

public enum RepairReportStatusEnum {
    WAIT_HANDLE(0, "WAIT_HANDLE", "待处理"),
    DISPATCH_ORDER(1, "DISPATCH_ORDER", "已派单"),
    CANCELED(9, "CANCELED", "已关闭"),
    ;

    @Getter
    private Integer value;

    @Getter
    private String name;

    @Getter
    private String nameDesc;

    RepairReportStatusEnum(Integer value, String name, String nameDesc) {
        this.value = value;
        this.name = name;
        this.nameDesc = nameDesc;
    }

    public static RepairReportStatusEnum typeOfValue(Integer value){
        RepairReportStatusEnum[] values = RepairReportStatusEnum.values();
        for (RepairReportStatusEnum v : values) {
            if (v.value.equals(value)) {
                return v;
            }
        }
        return null;
    }

    public static RepairReportStatusEnum typeOfName(String name){
        RepairReportStatusEnum[] values = RepairReportStatusEnum.values();
        for (RepairReportStatusEnum v : values) {
            if (v.name.equals(name)) {
                return v;
            }
        }
        return null;
    }

    public static RepairReportStatusEnum typeOfNameDesc(String nameDesc){
        RepairReportStatusEnum[] values = RepairReportStatusEnum.values();
        for (RepairReportStatusEnum v : values) {
            if (v.nameDesc.equals(nameDesc)) {
                return v;
            }
        }
        return null;
    }
}
