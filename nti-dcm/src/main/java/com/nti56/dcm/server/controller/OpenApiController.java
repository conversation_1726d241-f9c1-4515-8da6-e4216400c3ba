package com.nti56.dcm.server.controller;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.nti56.common.util.R;
import com.nti56.dcm.server.constant.DictConstant;
import com.nti56.dcm.server.domain.enums.IdTypeEnum;
import com.nti56.dcm.server.model.dto.*;
import com.nti56.dcm.server.model.vo.MaintenanceOrderVo;
import com.nti56.dcm.server.model.vo.MaintenancePlanVo;
import com.nti56.dcm.server.service.*;
import com.nti56.nlink.common.dto.TenantIsolation;
import com.nti56.nlink.common.util.BeanUtilsIntensifier;
import com.nti56.nlink.common.util.Result;
import io.swagger.v3.oas.annotations.Operation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.HashMap;
import java.util.List;

/**
 * 开放api
 */
@RestController
@RequestMapping("openapi")
@Slf4j
@Validated
public class OpenApiController {

    @Autowired
    private DeviceService deviceService;
    @Autowired
    private IDeviceRepairService deviceRepairService;

    @Autowired
    private MaintenancePlanService maintenancePlanService;

    @Autowired
    private MaintenanceOrderService maintenanceOrderService;

    @Autowired
    private CustomerRelationService customerRelationService;

    @Operation(summary = "解除OT设备绑定")
    @PutMapping(value = "/un-bind")
    public R<Void> unBindOtDevice(@RequestBody HashMap<String,Object> payload) {
        log.info("解除OT设备绑定,参数:{}",JSONObject.toJSONString(payload));
        ChangeNoticeMsgDto<OpenUnBindDeviceDto> changeNoticeMsgDto = JSONObject.parseObject(JSONObject.toJSONString(payload.get("payload")), new TypeReference<ChangeNoticeMsgDto<OpenUnBindDeviceDto>>() {});
        return deviceService.unBindOtDeviceOpen(changeNoticeMsgDto.getMsg());
    }


    @Operation(summary = "更新OT设备绑定")
    @PutMapping(value = "/update-bind")
    public R<Void> updateBindOtDevice(@RequestBody HashMap<String,Object> payload) {
        log.info("更新OT设备绑定,参数:{}",JSONObject.toJSONString(payload));
        ChangeNoticeMsgDto<OpenUnBindDeviceDto> changeNoticeMsgDto = JSONObject.parseObject(JSONObject.toJSONString(payload.get("payload")), new TypeReference<ChangeNoticeMsgDto<OpenUnBindDeviceDto>>() {});
        return deviceService.updateBindOtDevice(changeNoticeMsgDto.getMsg());
    }

    @PostMapping("/pageDeviceRepairOrder")
    @Operation(summary = "获取设备维修工单分页")
    public R<Page<DeviceRepairDto>> pageDeviceRepairOrder(@Validated @RequestBody PMOPageOrderDto dto) {
        // 构建查询条件
        DeviceRepairQueryDto queryDto = BeanUtilsIntensifier.copyBean(dto,DeviceRepairQueryDto.class);
        Page<DeviceRepairQueryDto> page = queryDto.toPage(DeviceRepairQueryDto.class);
        if (dto.getLastModifyStartDate() != null) {
            queryDto.setLastModifyStartDate(LocalDateTime.of(dto.getLastModifyStartDate(), LocalTime.of(0, 0, 0)));
        }
        if (dto.getLastModifyEndDate() != null) {
            queryDto.setLastModifyEndDate(LocalDateTime.of(dto.getLastModifyEndDate(), LocalTime.of(23, 59, 59)));
        }
        queryDto.setTenantId(dto.getTenantId());
        queryDto.setQueryType(DictConstant.QUERY_TYPE_ALL);
        queryDto.setOutsourceStatus(DictConstant.NOT_OUTSOURCE);
        // 构建租户信息
        TenantIsolation tenantIsolation = new TenantIsolation();
        tenantIsolation.setTenantId(dto.getTenantId());
        tenantIsolation.setIdType(IdTypeEnum.SUPPLIER.getValue());

        return R.result(deviceRepairService.getPage(queryDto, page, tenantIsolation));
    }

    @PostMapping("/pageMaintenancePlan")
    @Operation(summary = "供应商身份创建的保养计划分页")
    public R pageBySupplierCreate(@RequestBody MaintenancePlanPMODto dto) {
        QueryMaintenancePlanDto queryDto = BeanUtilsIntensifier.copyBean(dto, QueryMaintenancePlanDto.class);
        Page<MaintenancePlanVo> page = queryDto.toPage(MaintenancePlanVo.class);
        queryDto.setCreateSource(IdTypeEnum.SUPPLIER.getValue());
        if (dto.getLastModifyStartDate() != null) {
            queryDto.setLastModifyStartDate(LocalDateTime.of(dto.getLastModifyStartDate(), LocalTime.of(0, 0, 0)));
        }
        if (dto.getLastModifyEndDate() != null) {
            queryDto.setLastModifyEndDate(LocalDateTime.of(dto.getLastModifyEndDate(), LocalTime.of(23, 59, 59)));
        }
        Result<Page<MaintenancePlanVo>> result = maintenancePlanService.getPage(queryDto, page);
        return R.result(result);
    }

    @PostMapping("/pageMaintenanceOrder")
    @Operation(summary = "保养工单分页")
    public R pageMaintenanceOrder(@RequestBody PMOPageOrderDto dto) {
        Page<MaintenanceOrderVo> page = dto.toPage(MaintenanceOrderVo.class);
        Result<Page<MaintenanceOrderVo>> result = maintenanceOrderService.pageMaintenanceOrderPMO(dto, page);
        return R.result(result);
    }

    @PostMapping("/syncNtiDataCustomer")
    @Operation(summary = "同步今天国际数据型客户")
    public R syncNtiDataCustomer() {
        return R.result(customerRelationService.syncNtiDataCustomer());
    }

    @PostMapping("/syncNtiDataSupplier")
    @Operation(summary = "同步今天国际数据型供应商")
    public R syncNtiDataSupplier() {
        return R.result(customerRelationService.syncNtiDataSupplier());
    }

    @PostMapping("/syncDataCustomer")
    @Operation(summary = "同步租户的数据型客户")
    public R syncDataCustomer(@RequestBody @Valid List<DataCustomerDto> dtoList) {
        return R.result(customerRelationService.syncDataCustomer(dtoList));
    }

    @PostMapping("/syncDataSupplier")
    @Operation(summary = "同步租户的数据型供应商")
    public R syncDataSupplier(@RequestBody @Valid List<DataCustomerDto> dtoList) {
        return R.result(customerRelationService.syncDataSupplier(dtoList));
    }

}
