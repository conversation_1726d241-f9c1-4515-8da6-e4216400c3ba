package com.nti56.dcm.server.controller;


import com.nti56.common.util.R;
import com.nti56.dcm.server.entity.WorkGroupShiftEntity;
import com.nti56.dcm.server.model.dto.WorkGroupShiftDto;
import com.nti56.dcm.server.service.IWorkGroupShiftService;
import com.nti56.nlink.common.dto.TenantIsolation;
import com.nti56.nlink.common.util.Result;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <p>
 * 班次表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-19
 */
@RestController
@RequestMapping("/workGroupShift")
@Tag(name = "班组班次")
public class WorkGroupShiftController {


    @Autowired
    private IWorkGroupShiftService workGroupShiftService;

    @GetMapping("/listAll")
    @Operation(summary = "查询全部班次信息")
    public R<List<WorkGroupShiftEntity>> listAll(@RequestHeader("dcm_headers") TenantIsolation tenantIsolation) {
        Result<List<WorkGroupShiftEntity>> result = workGroupShiftService.listAll(tenantIsolation);
        return R.result(result);
    }

    @PostMapping("")
    @Operation(summary = "新增班组班次")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "返回值",
                    content = {@Content(mediaType = MediaType.APPLICATION_JSON_VALUE,
                            schema = @Schema(implementation = WorkGroupShiftEntity.class)
                    )})
    })
    public R createWorkGroupShift(@RequestHeader("dcm_headers") TenantIsolation tenantIsolation,
                                  @RequestBody @Validated WorkGroupShiftDto dto) {
        return R.result(workGroupShiftService.create(dto, tenantIsolation));
    }

    @PutMapping("/{id}")
    @Operation(summary = "修改班组班次")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "返回值",
                    content = {@Content(mediaType = MediaType.APPLICATION_JSON_VALUE,
                            schema = @Schema(implementation = WorkGroupShiftEntity.class)
                    )})
    })
    public R updateWorkGroupShift(@RequestHeader("dcm_headers") TenantIsolation tenantIsolation,
                                  @RequestBody @Validated WorkGroupShiftDto dto) {
        return R.result(workGroupShiftService.edit(dto, tenantIsolation));
    }

    @DeleteMapping("/{id}")
    @Operation(summary = "根据id删除班组班次")
    public R deleteWorkGroupShift(@RequestHeader("dcm_headers") TenantIsolation tenantIsolation,
                                  @PathVariable Long id) {
        return R.result(workGroupShiftService.deleteById(id, tenantIsolation));
    }

}
