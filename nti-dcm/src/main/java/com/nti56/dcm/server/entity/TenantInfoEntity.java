package com.nti56.dcm.server.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import lombok.Data;

/**
 * <p>
 * 租户信息表 实体类
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @Ddate 2024-05-20 13:43:15
 * @since JDK 1.8
 */
@Data
@TableName("tenant_info")
public class TenantInfoEntity extends BaseEntity{
        /**
        * 租户编码
        */
        private String code;

        /**
         * 租户真实名称
         */
        private String tenantName;

        /**
         * 是否pmo租户
         */
        private Integer isPmoTenant;

        /**
         * 同名的pmo数据型租户id
         */
        private Long pmoTenantId;


}
