package com.nti56.dcm.server.service;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.beust.jcommander.internal.Maps;
import com.google.common.collect.HashMultiset;
import com.google.common.collect.Multiset;
import com.google.common.collect.Sets;
import com.nti56.dcm.server.constant.DictConstant;
import com.nti56.dcm.server.domain.DeviceLocation;
import com.nti56.dcm.server.domain.DeviceStateCount;
import com.nti56.dcm.server.domain.DeviceType;
import com.nti56.dcm.server.domain.enums.ConnectOtEnum;
import com.nti56.dcm.server.domain.enums.DeviceCondEnum;
import com.nti56.dcm.server.domain.enums.DeviceOrderByEnum;
import com.nti56.dcm.server.domain.enums.DeviceStateEnum;
import com.nti56.dcm.server.domain.enums.IdTypeEnum;
import com.nti56.dcm.server.domain.enums.MessageTypeEnum;
import com.nti56.dcm.server.domain.enums.PmoTenantEnum;
import com.nti56.dcm.server.domain.enums.SupplierTypeEnum;
import com.nti56.dcm.server.domain.enums.YesNoEnum;
import com.nti56.dcm.server.entity.AlarmRecordEntity;
import com.nti56.dcm.server.entity.BaseEntity;
import com.nti56.dcm.server.entity.DeviceEntity;
import com.nti56.dcm.server.entity.DeviceTypeEntity;
import com.nti56.dcm.server.entity.TenantInfoEntity;
import com.nti56.dcm.server.exception.BizException;
import com.nti56.dcm.server.feign.OtController;
import com.nti56.dcm.server.mapper.DeviceEmpowerMapper;
import com.nti56.dcm.server.mapper.DeviceMapper;
import com.nti56.dcm.server.mapper.DeviceTypeMapper;
import com.nti56.dcm.server.mapper.PmoDeviceMapper;
import com.nti56.dcm.server.model.dto.DeviceDto;
import com.nti56.dcm.server.model.dto.DeviceLocationDto;
import com.nti56.dcm.server.model.dto.DeviceLocationTreeDto;
import com.nti56.dcm.server.model.dto.DeviceTypeGroupTreeDto;
import com.nti56.dcm.server.model.dto.DeviceTypeTreeDto;
import com.nti56.dcm.server.model.dto.DoServiceTaskDto;
import com.nti56.dcm.server.model.dto.electrocardiogram.DeviceOnLineTimePointDTO;
import com.nti56.dcm.server.model.dto.electrocardiogram.QueryDeviceRuntimeDataRequest;
import com.nti56.dcm.server.model.result.ITResult;
import com.nti56.dcm.server.model.vo.CustomerDeviceStateCountVo;
import com.nti56.dcm.server.model.vo.CustomerKey;
import com.nti56.dcm.server.model.vo.CustomerStateCountVo;
import com.nti56.dcm.server.model.vo.DeviceBlockChartSeriesVo;
import com.nti56.dcm.server.model.vo.DeviceBlockChartVo;
import com.nti56.dcm.server.model.vo.DeviceEmpowerFirstVo;
import com.nti56.dcm.server.model.vo.DeviceStateCountVo;
import com.nti56.dcm.server.model.vo.DeviceStateVo;
import com.nti56.dcm.server.model.vo.DeviceVo;
import com.nti56.dcm.server.model.vo.LocationDeviceStateCountVo;
import com.nti56.dcm.server.model.vo.PageWithStateCount;
import com.nti56.dcm.server.model.vo.PageWithStateCountGroupByCustomer;
import com.nti56.dcm.server.model.vo.PropertyValueWithTime;
import com.nti56.dcm.server.model.vo.StateValueWithTime;
import com.nti56.dcm.server.model.vo.TypeDeviceStateCountVo;
import com.nti56.nlink.common.dto.TenantIsolation;
import com.nti56.nlink.common.fetcher.CommonFetcher;
import com.nti56.nlink.common.fetcher.CommonFetcherFactory;
import com.nti56.nlink.common.util.Result;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.bean.copier.CopyOptions;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjUtil;
import cn.hutool.core.util.StrUtil;
import lombok.extern.slf4j.Slf4j;

/**
 * 类说明: 设备心电图服务
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2023-12-21 10:27:29
 * @since JDK 1.8
 */
@Service
@Slf4j
public class DeviceChartService {

    @Autowired
    private DeviceLocationService deviceLocationService;

    @Autowired
    private DeviceMapper deviceMapper;

    @Autowired
    private OtController otController;

    @Autowired
    private CommonFetcherFactory commonFetcherFactory;

    @Autowired
    private DeviceTypeService deviceTypeService;
    @Autowired
    private DeviceTypeGroupService deviceTypeGroupService;

    @Autowired
    private DeviceTypeMapper deviceTypeMapper;
    @Autowired
    private TenantInfoService tenantInfoService;

    @Autowired
    private DeviceEmpowerMapper deviceEmpowerMapper;
    @Autowired
    private CustomerRelationService customerRelationService;

    @Autowired
    private AlarmRecordService alarmRecordService;



    private static final Long defaultTenantId = DictConstant.DEFAULT_TENANT_ID;

    public Result<Pair<List<DeviceVo>, DeviceStateCountVo>> listDeviceWithState(
            TenantIsolation tenantIsolation,
            DeviceCondEnum cond, 
            String deviceName, 
            String deviceSerialNumber, 
            String customerName, 
            Long customerId,
            DeviceOrderByEnum orderBy, Boolean asc,
            Date startConnectTime,
            Date endConnectTime
    ) {

        Long tenantId = tenantIsolation.getTenantId();
        Integer idType = tenantIsolation.getIdType();
        List<DeviceEntity> deviceList = new ArrayList<>();
        // deviceId -> empower
        Map<Long, DeviceEmpowerFirstVo> deviceIdEmpowerMap = new HashMap<>();           
        if(IdTypeEnum.CUSTOMER.getValue().equals(idType)){
            DeviceDto deviceDto = new DeviceDto();
            Long pmoTenantId = null;
            if (PmoTenantEnum.IS_PMO_TENANT.getValue().equals(tenantIsolation.getIsPmoTenant())){
                TenantInfoEntity tenantInfo = tenantInfoService.getById(tenantIsolation.getTenantId(), tenantIsolation.getTenantId()).getResult();
                pmoTenantId = tenantInfo.getPmoTenantId();
            }
            deviceDto.setConnectOt(ConnectOtEnum.CONNECT.getValue());
            deviceDto.setPmoTenantId(pmoTenantId);
            List<DeviceDto> deviceDtoList = deviceMapper.listDevice(deviceDto, tenantIsolation);
            List<DeviceEntity> deviceEntities = BeanUtil.copyToList(deviceDtoList, DeviceEntity.class);
            for (DeviceEntity deviceEntity : deviceEntities) {
                if (DictConstant.IS_PMO.equals(deviceEntity.getIsPmo())){
                    deviceEntity.setTenantId(DictConstant.DEFAULT_TENANT_ID);
                }
            }
            deviceList.addAll(deviceEntities);
        }else{
            // 供应商身份，看客户授权的设备
            List<DeviceEmpowerFirstVo> list = deviceEmpowerMapper.listBySupplierId(tenantId);
            list.forEach(t -> {
                deviceIdEmpowerMap.put(t.getDeviceId(), t);
            });
            
            List<Long> ids = new ArrayList<>();
            if(list != null && list.size() > 0){
                if(customerName != null && !"".equals(customerName)){
                    list = list.stream()
                        .filter(t -> {
                            if(t.getCustomerName() == null){
                                return false;
                            }
                            return t.getCustomerName().contains(customerName);
                        })
                        .collect(Collectors.toList());
                }
                if(customerId != null){
                    list = list.stream()
                        .filter(t -> {
                            return customerId.equals(t.getCustomerId());
                        })
                        .collect(Collectors.toList());
                }
                ids = list.stream()
                    .map(DeviceEmpowerFirstVo::getDeviceId)
                    .collect(Collectors.toList());
            }
            if (defaultTenantId.equals(tenantId) && IdTypeEnum.SUPPLIER.getValue().equals(idType) ){
                List<DeviceVo> pmoDevices = deviceEmpowerMapper.listPmoDevice(null);
                pmoDevices.forEach(i->{
                    DeviceEmpowerFirstVo vo = new DeviceEmpowerFirstVo();
                    vo.setCustomerId(i.getTenantId());
                    vo.setCustomerName(i.getCustomerName());
                    deviceIdEmpowerMap.put(i.getId(), vo);
                });
                // 调用ot查出客户授权的设备的当前状态
                if(pmoDevices != null && pmoDevices.size() > 0){
                    if(customerName != null && !"".equals(customerName)){
                        pmoDevices = pmoDevices.stream()
                                .filter(t -> {
                                    if(t.getCustomerName() == null){
                                        return false;
                                    }
                                    return t.getCustomerName().contains(customerName);
                                })
                                .collect(Collectors.toList());
                    }
                    if(customerId != null){
                        pmoDevices = pmoDevices.stream()
                                .filter(t -> {
                                    return customerId.equals(t.getTenantId());
                                })
                                .collect(Collectors.toList());
                    }
                    List<Long> pmoIds = pmoDevices.stream()
                            .map(DeviceVo::getId)
                            .collect(Collectors.toList());
                    ids.addAll(pmoIds);
                }
            }
            if(ids != null && ids.size() > 0){
                deviceList = deviceMapper.listByIds(ids);
            }
        }
        if (deviceList == null || deviceList.size() <= 0) {
            return Result.ok(Pair.of(new ArrayList<>(), DeviceStateCountVo.init()));
        }

        List<String> aliasNameList = deviceList.stream()
                .filter(t -> {
                    return ConnectOtEnum.CONNECT.getValue().equals(t.getConnectOt());
                })
                .map(DeviceEntity::getDeviceAliasName)
                .filter(t -> {
                    return t != null && !"".equals(t);
                })
                .collect(Collectors.toList());
        if (aliasNameList == null || aliasNameList.size() <= 0) {
            return Result.ok(Pair.of(new ArrayList<>(), DeviceStateCountVo.init()));
        }
        List<String> tenantIdList = deviceList.stream()
                .filter(t -> {
                    return ConnectOtEnum.CONNECT.getValue().equals(t.getConnectOt())&& StringUtils.isNotEmpty(t.getDeviceAliasName());
                })
                .map(i->{
                    if (DictConstant.IS_PMO.equals(i.getIsPmo())){
                        return defaultTenantId;
                    }else {
                        return i.getTenantId();
                    }
                })
                .filter(t -> {
                    return t != null;
                })
                .map(t -> {
                    return t.toString();
                })
                .collect(Collectors.toList());
        

        Map<String, StateValueWithTime> resultMap = new HashMap<>();
        if (aliasNameList != null && aliasNameList.size() > 0 && tenantIdList != null && tenantIdList.size() > 0) {
            String body = JSONObject.toJSONString(new HashMap<String, Object>() {{
                put("deviceNames", aliasNameList);
                put("tenantIds", tenantIdList);
            }});
            String otHeaders = "{\"tenantId\":" + tenantId + "}";
            // 调用ot查出所有设备的当前状态
            Result<Map<String, StateValueWithTime>> r = otController.propertyAndTimeOfDeviceByNamesWithoutTenant("State", otHeaders, body);
            if (r == null) {
                return Result.error("查询状态失败");
            }
            if (r.getSignal() == null) {
                return Result.error("查询状态失败:" + r.getMessage());
            }
            if (!r.getSignal()) {
                return Result.error(r.getMessage());
            }
            resultMap = r.getResult();
        }
        final Map<String, StateValueWithTime> finalResultMap = resultMap;

        
        List<DeviceVo> filterList = deviceList.stream()
            .map(t -> {
                DeviceVo vo = new DeviceVo();
                BeanUtils.copyProperties(t, vo);
                DeviceEmpowerFirstVo empowerVo = deviceIdEmpowerMap.get(t.getId());
                if(empowerVo != null){
                    vo.setCustomerId(empowerVo.getCustomerId());
                    vo.setCustomerName(empowerVo.getCustomerName());
                }
                String alias = t.getDeviceAliasName();
                Long tid = DictConstant.IS_PMO.equals(t.getIsPmo()) ? defaultTenantId : t.getTenantId();
                String key = tid + "_" + alias;
                StateValueWithTime pair = finalResultMap.get(key);
                if (pair != null) {
                    Integer state = pair.getValue();
                    vo.setState(DeviceStateEnum.typeOfValue(state));
                    Long time = pair.getTime();
                    vo.setStateTime(time);
                }
                if (vo.getState() == null) {
                    vo.setState(DeviceStateEnum.OFF_LINE);
                }
                // if(vo.getStateTime() == null){
                //     vo.setStateTime(0L);
                // }

                return vo;
            })
            .filter(t -> {
                if(deviceName != null && !"".equals(deviceName)){
                    return t.getDeviceName().contains(deviceName);
                }
                return true;
            })
            .filter(t -> {
                if(deviceSerialNumber != null && !"".equals(deviceSerialNumber)){
                    return t.getSerialNumber().equals(deviceSerialNumber);
                }
                return true;
            })
            .filter(t -> {
                if(startConnectTime != null && endConnectTime != null){
                    return t.getConnectTime() != null &&
                            !t.getConnectTime().before(startConnectTime) &&
                            !t.getConnectTime().after(endConnectTime);
                }
                return true;
            })
            .collect(Collectors.toList());
        
        Multiset<Integer> deviceStateCounter = HashMultiset.create();
        DeviceStateCountVo deviceStateCount = DeviceStateCountVo.init();

        filterList.stream().forEach(vo -> {
            DeviceStateEnum s = vo.getState();
            if(s != null){
                deviceStateCounter.add(s.getValue());
            }
        });
        Integer totalCount = filterList.size();
        Integer faultCount = deviceStateCounter.count(DeviceStateEnum.FAULT.getValue());
        Integer taskCount = deviceStateCounter.count(DeviceStateEnum.TASK.getValue());
        Integer standByCount = deviceStateCounter.count(DeviceStateEnum.STAND_BY.getValue());
        Integer offlineCount = deviceStateCounter.count(DeviceStateEnum.OFF_LINE.getValue());
        deviceStateCount.setTotalCount(totalCount);
        deviceStateCount.setFaultCount(faultCount);
        deviceStateCount.setTaskCount(taskCount);
        deviceStateCount.setStandByCount(standByCount);
        deviceStateCount.setOnlineCount(taskCount + standByCount + faultCount);
        deviceStateCount.setOfflineCount(totalCount - taskCount - standByCount - faultCount);

        filterList = filterList.stream()
                .filter(t -> {
                    // if(t.getState() == null){
                    //     if(cond == null){
                    //         return true;
                    //     }else{
                    //         return false;
                    //     }
                    // }
                    if (DeviceCondEnum.FAULT.equals(cond)) {
                        return DeviceStateEnum.FAULT.equals(t.getState());
                    } else if (DeviceCondEnum.OFF_LINE.equals(cond)) {
                        return DeviceStateEnum.OFF_LINE.equals(t.getState());
                    } else if (DeviceCondEnum.STAND_BY.equals(cond)) {
                        return DeviceStateEnum.STAND_BY.equals(t.getState());
                    } else if (DeviceCondEnum.TASK.equals(cond)) {
                        return DeviceStateEnum.TASK.equals(t.getState());
                    }
                    return true;
                })
                
                .collect(Collectors.toList());

        // 排序
        Comparator<DeviceVo> comparator;
        if (DeviceOrderByEnum.NAME.equals(orderBy)) {
            if (asc) {
                comparator = Comparator.comparing(DeviceVo::getDeviceName);
            } else {
                comparator = Comparator.comparing(DeviceVo::getDeviceName).reversed();
            }
        } else {
            if (asc) {
                comparator = Comparator.comparing(
                        DeviceVo::getStateTime,
                        Comparator.nullsLast(Comparator.naturalOrder())
                );
            } else {
                comparator = Comparator.comparing(
                        DeviceVo::getStateTime,
                        Comparator.nullsLast(Comparator.naturalOrder())
                ).reversed();
            }
        }
        filterList.sort(comparator);
        return Result.ok(Pair.of(filterList, deviceStateCount));
    }

    public Result<PageWithStateCountGroupByCustomer> pageDeviceWithStateGroupByCustomer(
        TenantIsolation tenantIsolation,
        DeviceCondEnum cond, 
        String deviceName, 
        String deviceSerialNumber, 
        String customerName, 
        Long customerId,
        DeviceOrderByEnum orderBy, Boolean asc,
        Integer size, Integer current,
        Date startConnectTime,
        Date endConnectTime
    ){
        Long tenantId = tenantIsolation.getTenantId();
        Integer idType = tenantIsolation.getIdType();
        Result<Pair<List<DeviceVo>, DeviceStateCountVo>> r = listDeviceWithState(
            tenantIsolation, cond, deviceName, deviceSerialNumber, customerName, customerId, orderBy, asc, startConnectTime, endConnectTime
        );
        if(!r.getSignal()){
            return Result.error(r.getMessage());
        }
        Pair<List<DeviceVo>, DeviceStateCountVo> p = r.getResult();
        List<DeviceVo> filterList = p.getLeft();
        DeviceStateCountVo deviceStateCount = p.getRight();
        if(filterList == null || filterList.size() <= 0){
            PageWithStateCountGroupByCustomer empty = emptyPageCustomer(size, current);
            empty.setDeviceStateCount(deviceStateCount);
            return Result.ok(empty);
        }

        // 按客户分组
        Map<CustomerKey, List<DeviceVo>> group = filterList.stream().collect(Collectors.groupingBy(t -> {
            return new CustomerKey(t.getCustomerId(), t.getCustomerName());
        }));

        // 每组统计
        List<CustomerStateCountVo> voList = new ArrayList<>();
        group.entrySet().stream().forEach(entry -> {
            CustomerKey key = entry.getKey();
            List<DeviceVo> deviceList = entry.getValue();

            CustomerStateCountVo vo = new CustomerStateCountVo();
            vo.setCustomerId(key.getCustomerId());
            vo.setCustomerName(key.getCustomerName());

            Multiset<Integer> deviceStateCounter = HashMultiset.create();
            DeviceStateCountVo countVo = DeviceStateCountVo.init();
            deviceList.forEach(t -> {
                DeviceStateEnum s = t.getState();
                if(s != null){
                    deviceStateCounter.add(s.getValue());
                }
            });

            Integer totalCount = deviceList.size();
            Integer faultCount = deviceStateCounter.count(DeviceStateEnum.FAULT.getValue());
            Integer taskCount = deviceStateCounter.count(DeviceStateEnum.TASK.getValue());
            Integer standByCount = deviceStateCounter.count(DeviceStateEnum.STAND_BY.getValue());
            Integer offlineCount = deviceStateCounter.count(DeviceStateEnum.OFF_LINE.getValue());
            countVo.setTotalCount(totalCount);
            countVo.setFaultCount(faultCount);
            countVo.setTaskCount(taskCount);
            countVo.setStandByCount(standByCount);
            countVo.setOnlineCount(taskCount + standByCount + faultCount);
            countVo.setOfflineCount(totalCount - taskCount - standByCount - faultCount);

            vo.setStateCount(countVo);

            voList.add(vo);
        });

        // 分页
        Integer total = voList.size();
        Integer skip = size * (current - 1);
        Integer limit = size;
        List<CustomerStateCountVo> limitList = voList.stream().skip(skip).limit(limit).collect(Collectors.toList());
        PageWithStateCountGroupByCustomer page = new PageWithStateCountGroupByCustomer();
        page.setRecords(limitList);
        page.setSize(size);
        page.setTotal(total);
        page.setCurrent(current);
        page.setDeviceStateCount(deviceStateCount);

        return Result.ok(page);
    }

    public Result<PageWithStateCount> pageDeviceWithState(
            TenantIsolation tenantIsolation,
            DeviceCondEnum cond, 
            String deviceName, 
            String deviceSerialNumber, 
            String customerName, 
            Long customerId,
            DeviceOrderByEnum orderBy, Boolean asc,
            Integer size, Integer current,
            Date startConnectTime,
            Date endConnectTime
    ) {
        Long tenantId = tenantIsolation.getTenantId();
        Integer idType = tenantIsolation.getIdType();
        log.info("pageDeviceWithState running....");
        Result<Pair<List<DeviceVo>, DeviceStateCountVo>> r = listDeviceWithState(
                tenantIsolation, cond, deviceName, deviceSerialNumber, customerName, customerId, orderBy, asc, startConnectTime, endConnectTime
        );
        if(!r.getSignal()){
            return Result.error(r.getMessage());
        }
        Pair<List<DeviceVo>, DeviceStateCountVo> p = r.getResult();
        List<DeviceVo> filterList = p.getLeft();
        DeviceStateCountVo deviceStateCount = p.getRight();
        if(filterList == null || filterList.size() <= 0){
            PageWithStateCount empty = emptyPage(size, current);
            empty.setDeviceStateCount(deviceStateCount);
            return Result.ok(empty);
        }

        // 统计设备告警数量
        List<Long> deviceIds = filterList.stream().map(DeviceVo::getId).collect(Collectors.toList());
        if (CollectionUtil.isNotEmpty(deviceIds)) {
            Map<Long, Long> deviceFaultCountMap = alarmRecordService.list(Wrappers.<AlarmRecordEntity>lambdaQuery()
                            .in(AlarmRecordEntity::getDeviceId, deviceIds)
                            .eq(AlarmRecordEntity::getTenantId, tenantId)
                            .eq(AlarmRecordEntity::getIdType, tenantIsolation.getIdType())
                            .eq(AlarmRecordEntity::getAlarmType, MessageTypeEnum.DEVICE_FAULT.getValue())
                            .eq(AlarmRecordEntity::getIsFinish, YesNoEnum.NO.getValue()))
                    .stream().collect(Collectors.groupingBy(AlarmRecordEntity::getDeviceId, Collectors.counting()));
            filterList.forEach(deviceVo -> {
                deviceVo.setDeviceAlarmCount(deviceFaultCountMap.getOrDefault(deviceVo.getId(), 0L));
            });
        }
        log.info("pageDeviceWithState ending....");
        // 分页
        Integer total = filterList.size();
        Integer skip = size * (current - 1);
        Integer limit = size;
        List<DeviceVo> limitList = filterList.stream().skip(skip).limit(limit).collect(Collectors.toList());
        PageWithStateCount page = new PageWithStateCount();
        page.setRecords(limitList);
        page.setSize(size);
        page.setTotal(total);
        page.setCurrent(current);
        page.setDeviceStateCount(deviceStateCount);

        return Result.ok(page);
    }

    private PageWithStateCount emptyPage(Integer size, Integer current) {
        DeviceStateCountVo deviceStateCount = DeviceStateCountVo.init();
        PageWithStateCount page = new PageWithStateCount();
        page.setRecords(new ArrayList<>());
        page.setSize(size);
        page.setTotal(0);
        page.setCurrent(current);
        page.setDeviceStateCount(deviceStateCount);
        return page;
    }

    private PageWithStateCountGroupByCustomer emptyPageCustomer(Integer size, Integer current) {
        DeviceStateCountVo deviceStateCount = DeviceStateCountVo.init();
        PageWithStateCountGroupByCustomer page = new PageWithStateCountGroupByCustomer();
        page.setRecords(new ArrayList<>());
        page.setSize(size);
        page.setTotal(0);
        page.setCurrent(current);
        page.setDeviceStateCount(deviceStateCount);
        return page;
    }

    public Result<TypeDeviceStateCountVo> deviceStateCountOfType(
        TenantIsolation tenantIsolation,
        Long deviceTypeId, 
        String deviceName, 
        String deviceSerialNumber, 
        DeviceStateEnum state
    ) {
        Long tenantId = tenantIsolation.getTenantId();

        CommonFetcher commonFetcher = commonFetcherFactory.buildCommonFetcher(tenantId);

        Long pmoTenantId = null;
        if (PmoTenantEnum.IS_PMO_TENANT.getValue().equals(tenantIsolation.getIsPmoTenant())) {
            TenantInfoEntity tenantInfo = tenantInfoService.getById(tenantIsolation.getTenantId(), tenantIsolation.getTenantId()).getResult();
            pmoTenantId = tenantInfo.getPmoTenantId();
        }
        CommonFetcher pmoCommonFetcher = commonFetcherFactory.buildCommonFetcher(pmoTenantId);
        CommonFetcher defaultCommonFetcher = commonFetcherFactory.buildCommonFetcher(DictConstant.DEFAULT_TENANT_ID);
        Boolean needPmo = PmoTenantEnum.IS_PMO_TENANT.getValue().equals(tenantIsolation.getIsPmoTenant());
        Result<DeviceType> deviceTypeResult = DeviceType.checkInfo(tenantId, deviceTypeId, commonFetcher, needPmo, defaultCommonFetcher);
        if (!deviceTypeResult.getSignal()) {
            return Result.error(deviceTypeResult.getMessage());
        }
        DeviceType deviceType = deviceTypeResult.getResult();
        deviceType.loadDevices(commonFetcher, needPmo, pmoCommonFetcher, defaultCommonFetcher);
        Map<Long, List<DeviceVo>> collect = deviceType.getAllDevices().stream().collect(Collectors.groupingBy(i -> i.getTenantId()));
        for (Entry<Long, List<DeviceVo>> entry : collect.entrySet()) {
            Long currentTenant = entry.getKey();
            List<DeviceVo> value = entry.getValue();
            List<String> aliasNameList = value.stream()
                    .filter(t -> {
                        return ConnectOtEnum.CONNECT.getValue().equals(t.getConnectOt());
                    })
                    .map(DeviceEntity::getDeviceAliasName)
                    .filter(t -> {
                        return t != null && !"".equals(t);
                    })
                    .collect(Collectors.toList());
            if (aliasNameList != null && aliasNameList.size() > 0) {
                String otHeaders = "{\"tenantId\":" + currentTenant + "}";
                String body = JSONObject.toJSONString(new HashMap<String, Object>() {{
                    put("deviceNames", aliasNameList);
                }});
                // 调用ot查出所有设备的当前状态
                Result<Map<String, Integer>> r = otController.propertyOfDeviceByNames("State", otHeaders, body);
                if (r == null) {
                    return Result.error("查询状态失败");
                }
                if (r.getSignal() == null) {
                    return Result.error("查询状态失败:" + r.getMessage());
                }
                if (!r.getSignal()) {
                    return Result.error(r.getMessage());
                }
                Map<String, Integer> deviceNameStateMap = r.getResult();
                deviceType.importDeviceState(deviceNameStateMap,value);
            }
        }
        deviceType.filterDevice(deviceName, deviceSerialNumber, state);
        deviceType.countDeviceState();
        List<DeviceVo> deviceList = deviceType.getAllDevices()
                .stream()
                .sorted(Comparator.comparing(DeviceVo::getCreateTime).reversed()
                        .thenComparing(DeviceVo::getId)
                )
                .collect(Collectors.toList());
        ;

        TypeDeviceStateCountVo vo = new TypeDeviceStateCountVo();
        vo.setDeviceTypeId(deviceType.getId());
        vo.setDeviceTypeName(deviceType.getName());

        DeviceStateCount allDeviceStateCount = deviceType.getAllDeviceStateCount();
        DeviceStateCountVo allDeviceStateCountVo = new DeviceStateCountVo();
        BeanUtils.copyProperties(allDeviceStateCount, allDeviceStateCountVo);
        allDeviceStateCountVo.setTotalDocCount(deviceList.size());
        vo.setAllDeviceStateCount(allDeviceStateCountVo);
        vo.setAllDeviceList(deviceList);
        List<DeviceVo> connectDeviceList = deviceList.stream()
                .filter(t -> {
                    return ConnectOtEnum.CONNECT.getValue().equals(t.getConnectOt());
                })
                .collect(Collectors.toList());

        // 统计设备告警数量
        List<Long> deviceIds = connectDeviceList.stream().map(DeviceVo::getId).collect(Collectors.toList());
        if (CollectionUtil.isNotEmpty(deviceIds)) {
            Map<Long, Long> deviceFaultCountMap = alarmRecordService.list(Wrappers.<AlarmRecordEntity>lambdaQuery()
                            .in(AlarmRecordEntity::getDeviceId, deviceIds)
                            .eq(AlarmRecordEntity::getTenantId, tenantId)
                            .eq(AlarmRecordEntity::getIdType, tenantIsolation.getIdType())
                            .eq(AlarmRecordEntity::getAlarmType, MessageTypeEnum.DEVICE_FAULT.getValue())
                            .eq(AlarmRecordEntity::getIsFinish, YesNoEnum.NO.getValue()))
                    .stream().collect(Collectors.groupingBy(AlarmRecordEntity::getDeviceId, Collectors.counting()));
            connectDeviceList.forEach(deviceVo -> {
                deviceVo.setDeviceAlarmCount(deviceFaultCountMap.getOrDefault(deviceVo.getId(), 0L));
            });
        }

        vo.setAllDeviceStateList(connectDeviceList);

        if (deviceList != null) {
            List<Long> tenantIdList = deviceList.stream()
                    .map(DeviceVo::getTenantId)
                    .distinct()
                    .collect(Collectors.toList());
            vo.setTenantIdList(tenantIdList);
        }

        return Result.ok(vo);

    }

    public Result<LocationDeviceStateCountVo> deviceStateCountOfLocation(
        TenantIsolation tenantIsolation,
        Long deviceLocationId, 
        String deviceName, 
        String deviceSerialNumber, 
        DeviceStateEnum state
    ) {
        Long finalTenantId = tenantIsolation.getTenantId();
        CommonFetcher commonFetcher = commonFetcherFactory.buildCommonFetcher(finalTenantId);


        Long pmoTenantId = null;
        Boolean needPmo = PmoTenantEnum.IS_PMO_TENANT.getValue().equals(tenantIsolation.getIsPmoTenant());
        if (needPmo) {
            TenantInfoEntity tenantInfo = tenantInfoService.getById(tenantIsolation.getTenantId(), tenantIsolation.getTenantId()).getResult();
            pmoTenantId = tenantInfo.getPmoTenantId();
        }
        CommonFetcher pmoCommonFetcher = commonFetcherFactory.buildCommonFetcher(pmoTenantId);

        Result<DeviceLocation> deviceLocationResult = DeviceLocation.checkInfo(deviceLocationId, commonFetcher);
        if (!deviceLocationResult.getSignal()) {
            return Result.error(deviceLocationResult.getMessage());
        }
        DeviceLocation deviceLocation = deviceLocationResult.getResult();
        deviceLocation.loadSubs(commonFetcher);
        deviceLocation.loadDevices(commonFetcher,needPmo,pmoCommonFetcher);

        Map<Long, List<DeviceVo>> collect = deviceLocation.getAllSubDevices().stream().collect(Collectors.groupingBy(i -> i.getTenantId()));
        List<String> aliasNameList =Lists.newArrayList();
        for (Entry<Long, List<DeviceVo>> entry : collect.entrySet()) {
            List<DeviceVo> value = entry.getValue();
            List<String> temp = value.stream()
                    .filter(t -> {
                        return ConnectOtEnum.CONNECT.getValue().equals(t.getConnectOt());
                    })
                    .map(DeviceEntity::getDeviceAliasName)
                    .filter(t -> {
                        return t != null && !"".equals(t);
                    })
                    .collect(Collectors.toList());
            aliasNameList.addAll(temp);
        }

        if (aliasNameList != null && aliasNameList.size() > 0) {
            String otHeaders = "{\"tenantId\":" + tenantIsolation.getTenantId() + "}";

            Map<String, Object> params = Maps.newHashMap();
            params.put("deviceNames", aliasNameList);
            params.put("tenantIds", collect.keySet());
            String body = JSONObject.toJSONString(params);
            // 调用ot查出所有设备的当前状态
            Result<Map<String, Integer>> r = otController.propertyOfDeviceByNames("State", otHeaders, body);
            if (r == null) {
                return Result.error("查询状态失败");
            }
            if (r.getSignal() == null) {
                return Result.error("查询状态失败:" + r.getMessage());
            }
            if (!r.getSignal()) {
                return Result.error(r.getMessage());
            }
            Map<String, Integer> deviceNameStateMap = r.getResult();
            deviceLocation.importDeviceState(deviceNameStateMap);
        }
        Integer totalDocCount = deviceLocation.getAllSubDevices().size();
        deviceLocation.filterDevice(deviceName, deviceSerialNumber, state);
        
        deviceLocation.countDeviceState();

        List<DeviceVo> deviceList = deviceLocation.getAllSubDevices();
        if(deviceList != null && deviceList.size() > 0){
            deviceList = deviceList.stream()
            .sorted(Comparator.comparing(DeviceVo::getCreateTime).reversed()
                .thenComparing(DeviceVo::getId)
            )
            .collect(Collectors.toList());
        }

        LocationDeviceStateCountVo vo = new LocationDeviceStateCountVo();
        vo.setLocationId(deviceLocation.getId());
        vo.setLocationName(deviceLocation.getName());

        DeviceStateCount allDeviceStateCount = deviceLocation.getAllDeviceStateCount();
        DeviceStateCountVo allDeviceStateCountVo = new DeviceStateCountVo();
        BeanUtils.copyProperties(allDeviceStateCount, allDeviceStateCountVo);
        allDeviceStateCountVo.setTotalDocCount(totalDocCount);
        vo.setAllDeviceStateCount(allDeviceStateCountVo);

        List<LocationDeviceStateCountVo> subLocationList = new ArrayList<>();
        List<DeviceLocation> subs = deviceLocation.getSubs();
        if (subs != null) {
            for (DeviceLocation sub : subs) {
                LocationDeviceStateCountVo subVo = new LocationDeviceStateCountVo();
                subVo.setLocationId(sub.getId());
                subVo.setLocationName(sub.getName());
                DeviceStateCount subAllDeviceStateCount = sub.getAllDeviceStateCount();
                DeviceStateCountVo subAllDeviceStateCountVo = new DeviceStateCountVo();
                BeanUtils.copyProperties(subAllDeviceStateCount, subAllDeviceStateCountVo);
                subVo.setAllDeviceStateCount(subAllDeviceStateCountVo);
                subLocationList.add(subVo);
            }
        }
        vo.setSubLocationList(subLocationList);
        List<DeviceVo> subDevices = deviceLocation.getSubDevices();
        if (subDevices != null) {
            vo.setSubDeviceStateList(subDevices.stream().filter(t -> {
                return t.getConnectOt() != null && ConnectOtEnum.CONNECT.getValue().equals(t.getConnectOt());
            }).collect(Collectors.toList()));
        }

        List<DeviceVo> subDeviceStateList = vo.getSubDeviceStateList();
        if (subDeviceStateList != null && subDeviceStateList.size() > 0) {
            List<Long> deviceTypeIds = subDeviceStateList.stream().map(DeviceVo::getDeviceTypeId).collect(Collectors.toList());
            // 获取设备的类型
            List<DeviceTypeEntity> deviceTypes = deviceTypeMapper.listByIds(finalTenantId, deviceTypeIds);
            if (needPmo){
                List<DeviceTypeEntity> pmoDeviceTypes = deviceTypeMapper.listByIds(DictConstant.DEFAULT_TENANT_ID, deviceTypeIds);
                deviceTypes.addAll(pmoDeviceTypes);
            }
            if (deviceTypes != null) {
                Map<Long, String> deviceTypeNameMap = new HashMap<>();
                deviceTypes.stream().forEach(t -> {
                    deviceTypeNameMap.put(t.getId(), t.getTypeName());
                });
                subDeviceStateList.stream().forEach(t -> {
                    Long deviceTypeId = t.getDeviceTypeId();
                    String deviceTypeName = deviceTypeNameMap.get(deviceTypeId);
                    t.setDeviceType(deviceTypeName);
                });
            }
        }

        if (deviceList != null && deviceList.size() > 0) {
            List<Long> deviceTypeIds = deviceList.stream().map(DeviceVo::getDeviceTypeId).collect(Collectors.toList());
            // 获取设备的类型
            List<DeviceTypeEntity> deviceTypes = deviceTypeMapper.listByIds(finalTenantId, deviceTypeIds);
            if (needPmo){
                List<DeviceTypeEntity> pmoDeviceTypes = deviceTypeMapper.listByIds(DictConstant.DEFAULT_TENANT_ID, deviceTypeIds);
                deviceTypes.addAll(pmoDeviceTypes);
            }
            if (deviceTypes != null) {
                Map<Long, String> deviceTypeNameMap = new HashMap<>();
                deviceTypes.stream().forEach(t -> {
                    deviceTypeNameMap.put(t.getId(), t.getTypeName());
                });
                deviceList.stream().forEach(t -> {
                    Long deviceTypeId = t.getDeviceTypeId();
                    String deviceTypeName = deviceTypeNameMap.get(deviceTypeId);
                    t.setDeviceType(deviceTypeName);
                });
            }
        }

        vo.setAllDeviceList(deviceList);
        
        List<DeviceVo> connectDeviceList = deviceList.stream()
                .filter(t -> {
                    return ConnectOtEnum.CONNECT.getValue().equals(t.getConnectOt());
                })
                .collect(Collectors.toList());
        vo.setAllDeviceStateList(connectDeviceList);

        if(deviceList != null){
            List<Long> tenantIdList = deviceList.stream()
                .map(DeviceVo::getTenantId)
                .distinct()
                .collect(Collectors.toList());
            vo.setTenantIdList(tenantIdList);
        }
        
        return Result.ok(vo);
    }


    private void getDeviceStateFromOT(Map<Long, List<DeviceVo>> collect,TenantIsolation tenantIsolation){
        
    }


    public Result<CustomerDeviceStateCountVo> deviceStateCountOfCustomer(
            Long tenantId,
            Integer idType,
            String deviceName,
            String deviceSerialNumber,
            DeviceStateEnum state,
            Long customerId
    ) {
        CustomerDeviceStateCountVo vo = new CustomerDeviceStateCountVo();

        if (ObjUtil.isNull(customerId)){
            vo.setAllDeviceList(new ArrayList<>());
            vo.setAllDeviceStateCount(DeviceStateCountVo.init());
            vo.setAllDeviceStateList(new ArrayList<>());
            vo.setTenantIdList(new ArrayList<>());
            return Result.ok(vo);
        }

        TenantInfoEntity tenantInfo = tenantInfoService.getByPmoTenantId( customerId);
        Long pmoSourceTenantId = Optional.ofNullable(tenantInfo).map(BaseEntity::getTenantId).orElse(null);
        // 查出授权的设备
        List<DeviceVo> devices = deviceEmpowerMapper.listEmpowerDevice(pmoSourceTenantId!=null?pmoSourceTenantId:customerId, tenantId);

        Result<Integer> tenantTypeResult = customerRelationService.getTenantType(customerId, tenantId, idType);
        if(!tenantTypeResult.getSignal()){
            return Result.error("获取供应商类型失败" + tenantTypeResult.getMessage());
        }
        Integer tenantType = tenantTypeResult.getResult();
        Boolean isDataRecord = SupplierTypeEnum.SELF_DEFINING.getValue().equals(tenantType);

        if (defaultTenantId.equals(tenantId) && IdTypeEnum.SUPPLIER.getValue().equals(idType) && (isDataRecord||pmoSourceTenantId!=null)){
            List<DeviceVo> pmoDevices = deviceEmpowerMapper.listPmoDevice(customerId);
            pmoDevices.forEach(item->item.setTenantId(defaultTenantId));
            devices.addAll(pmoDevices);
        }
        if(devices == null || devices.size() <= 0){
            vo.setAllDeviceStateCount(DeviceStateCountVo.init());
            return Result.ok(vo);
        }

        // 按租户分组
        // tenantId -> devices
        Map<Long, List<DeviceVo>> group = devices.stream()
            .collect(Collectors.groupingBy(DeviceEntity::getTenantId));

        Multiset<Integer> deviceStateCounter = HashMultiset.create();
        DeviceStateCountVo deviceStateCount = DeviceStateCountVo.init();
       
        for(Entry<Long, List<DeviceVo>> entry:group.entrySet()){
            Long subTenantId = entry.getKey();
            List<DeviceVo> subDevices = entry.getValue();

            // aliasName -> deviceId
            Map<String, Long> aliasIdMap = new HashMap<>();
            List<String> aliasNameList = subDevices.stream()
                    .filter(t -> {
                        return ConnectOtEnum.CONNECT.getValue().equals(t.getConnectOt());
                    })
                    .filter(t -> {
                        String alias = t.getDeviceAliasName();
                        if(alias != null && !"".equals(alias)){
                            aliasIdMap.put(alias, t.getId());
                        }
                        return true;
                    })
                    .map(DeviceEntity::getDeviceAliasName)
                    .filter(t -> {
                        return t != null && !"".equals(t);
                    })
                    .collect(Collectors.toList());
            if (aliasNameList != null && aliasNameList.size() > 0) {
                String otHeaders = "{\"tenantId\":" + subTenantId + "}";
                String body = JSONObject.toJSONString(new HashMap<String, Object>() {{
                    put("deviceNames", aliasNameList);
                }});
                // 调用ot查出所有设备的当前状态
                Result<Map<String, Integer>> r = otController.propertyOfDeviceByNames("State", otHeaders, body);
                if (r == null) {
                    return Result.error("查询状态失败");
                }
                if (r.getSignal() == null) {
                    return Result.error("查询状态失败:" + r.getMessage());
                }
                if (!r.getSignal()) {
                    return Result.error(r.getMessage());
                }
                Map<String, Integer> deviceNameStateMap = r.getResult();


                subDevices.forEach(t -> {
                    String alias = t.getDeviceAliasName();
                    
                    Integer stateValue = deviceNameStateMap.get(alias);
                    if(stateValue == null){
                        if(ConnectOtEnum.CONNECT.getValue().equals(t.getConnectOt())){
                            t.setState(DeviceStateEnum.OFF_LINE);
                        }
                    }else{
                        t.setState(DeviceStateEnum.typeOfValue(stateValue));
                    }
                });
            }
        }

        // 统计设备告警数量
        List<Long> deviceIds = devices.stream().map(DeviceVo::getId).collect(Collectors.toList());
        if (CollectionUtil.isNotEmpty(deviceIds)) {
            Map<Long, Long> deviceFaultCountMap = alarmRecordService.list(Wrappers.<AlarmRecordEntity>lambdaQuery()
                            .in(AlarmRecordEntity::getDeviceId, deviceIds)
                            .eq(AlarmRecordEntity::getTenantId, tenantId)
                            .eq(AlarmRecordEntity::getIdType, idType)
                            .eq(AlarmRecordEntity::getAlarmType, MessageTypeEnum.DEVICE_FAULT.getValue())
                            .eq(AlarmRecordEntity::getIsFinish, YesNoEnum.NO.getValue()))
                    .stream().collect(Collectors.groupingBy(AlarmRecordEntity::getDeviceId, Collectors.counting()));
            devices.forEach(deviceVo -> {
                deviceVo.setDeviceAlarmCount(deviceFaultCountMap.getOrDefault(deviceVo.getId(), 0L));
            });
        }
        
        vo.setAllDeviceList(devices);

        // 过滤
        devices = devices.stream()
        .filter(t -> {
            return t.getConnectOt() != null && ConnectOtEnum.CONNECT.getValue().equals(t.getConnectOt());
        })
        .filter(t -> {
            if(deviceName == null || "".equals(deviceName)){
                return true;
            }
            String name = t.getDeviceName();
            return name.contains(deviceName);
        })
        .filter(t -> {
            if(deviceSerialNumber == null || "".equals(deviceSerialNumber)){
                return true;
            }
            String number = t.getSerialNumber();
            return number.contains(deviceSerialNumber);
        })
        .filter(t -> {
            if(state == null){
                return true;
            }
            DeviceStateEnum state2 = t.getState();
            return state.equals(state2);
        })
        .collect(Collectors.toList());

        // 统计
        devices.forEach(t -> {
            DeviceStateEnum s = t.getState();
            if(s != null){
                deviceStateCounter.add(s.getValue());
            }
        });
        
        Integer totalCount = 0;

        totalCount = devices.size();

        Integer faultCount = deviceStateCounter.count(DeviceStateEnum.FAULT.getValue());
        Integer taskCount = deviceStateCounter.count(DeviceStateEnum.TASK.getValue());
        Integer standByCount = deviceStateCounter.count(DeviceStateEnum.STAND_BY.getValue());
        Integer offlineCount = deviceStateCounter.count(DeviceStateEnum.OFF_LINE.getValue());

        deviceStateCount.setTotalCount(totalCount);
        deviceStateCount.setFaultCount(faultCount);
        deviceStateCount.setTaskCount(taskCount);
        deviceStateCount.setStandByCount(standByCount);
        deviceStateCount.setOnlineCount(taskCount + standByCount + faultCount);
        deviceStateCount.setOfflineCount(totalCount - taskCount - standByCount - faultCount);
        
        vo.setAllDeviceStateCount(deviceStateCount);
        vo.setAllDeviceStateList(devices);

        if(devices != null){
            List<Long> tenantIdList = devices.stream()
                .map(DeviceVo::getTenantId)
                .distinct()
                .collect(Collectors.toList());
            vo.setTenantIdList(tenantIdList);
        }

        return Result.ok(vo);
    }

    public Result<DeviceStateCountVo> deviceStateCountOfAll(Long tenantId, Integer idType) {
        
        Integer totalCount = 0;
        Multiset<Integer> deviceStateCounter = HashMultiset.create();
        
        // 当前租户下所有设备
        // CommonFetcher commonFetcher = commonFetcherFactory.buildCommonFetcher(tenantId);
        // Result<DeviceLocation> deviceLocationResult = DeviceLocation.checkInfo(null, commonFetcher);
        // if (!deviceLocationResult.getSignal()) {
        //     return Result.error(deviceLocationResult.getMessage());
        // }
        // DeviceLocation deviceLocation = deviceLocationResult.getResult();
        // deviceLocation.loadSubs(commonFetcher);
        // deviceLocation.loadDevices(commonFetcher);
        // List<DeviceVo> deviceList = deviceLocation.getAllSubDevices();

        // 调用ot查出当前租户下所有设备的当前状态
        // List<String> aliasNameList = deviceList.stream()
        //         .filter(t -> {
        //             return ConnectOtEnum.CONNECT.getValue().equals(t.getConnectOt());
        //         })
        //         .map(DeviceEntity::getDeviceAliasName)
        //         .filter(t -> {
        //             return t != null && !"".equals(t);
        //         })
        //         .collect(Collectors.toList());
        // if (aliasNameList != null && aliasNameList.size() > 0) {
        //     String aliasNames = String.join(",", aliasNameList);
        //     String otHeaders = "{\"tenantId\":" + tenantId + "}";
        //     Result<Map<String, Integer>> r = otController.propertyOfDeviceByNames("State", otHeaders, aliasNames);
        //     if (r == null || r.getSignal() == null) {
        //         return Result.error("查询状态失败");
        //     }
        //     if (!r.getSignal()) {
        //         return Result.error(r.getMessage());
        //     }
        //     Map<String, Integer> deviceNameStateMap = r.getResult();

        //     // 统计
        //     deviceNameStateMap.values().stream().forEach(t -> {
        //         deviceStateCounter.add(t);
        //     });
        //     totalCount += aliasNameList.size();
        // }

        // 客户授权的设备
        List<DeviceEmpowerFirstVo> customerDeviceList = deviceEmpowerMapper.listBySupplierId(tenantId);
        // 调用ot查出客户授权的设备的当前状态
        List<String> deviceNameList = customerDeviceList.stream()
                .filter(t -> {
                    return ConnectOtEnum.CONNECT.getValue().equals(t.getConnectOt());
                })
                .map(DeviceEmpowerFirstVo::getDeviceAliasName)
                .filter(t -> {
                    return t != null && !"".equals(t);
                })
                .collect(Collectors.toList());
        List<String> tenantIdList = customerDeviceList.stream()
                .filter(t -> {
                    return ConnectOtEnum.CONNECT.getValue().equals(t.getConnectOt());
                })
                .map(DeviceEmpowerFirstVo::getDeviceTenantId)
                .filter(t -> {
                    return t != null;
                })
                .map(t -> {
                    return t.toString();
                })
                .collect(Collectors.toList());
        if (defaultTenantId.equals(tenantId) && IdTypeEnum.SUPPLIER.getValue().equals(idType) ){
            List<DeviceVo> pmoDevices = deviceEmpowerMapper.listPmoDevice(null);
            // 调用ot查出客户授权的设备的当前状态
            List<String> pmoDeviceNameList = pmoDevices.stream()
                    .filter(t -> {
                        return ConnectOtEnum.CONNECT.getValue().equals(t.getConnectOt());
                    })
                    .map(DeviceVo::getDeviceAliasName)
                    .filter(t -> {
                        return t != null && !"".equals(t);
                    })
                    .collect(Collectors.toList());
            deviceNameList.addAll(pmoDeviceNameList);
            List<String> pmoTenantIdList = pmoDevices.stream()
                    .filter(t -> {
                        return ConnectOtEnum.CONNECT.getValue().equals(t.getConnectOt());
                    })
                    .map(DeviceVo::getTenantId)
                    .filter(t -> {
                        return t != null;
                    })
                    .map(t -> {
                        return defaultTenantId.toString();
                    })
                    .collect(Collectors.toList());
            tenantIdList.addAll(pmoTenantIdList);
        }
        if (deviceNameList != null && deviceNameList.size() > 0 && tenantIdList != null && tenantIdList.size() > 0) {
            String body = JSONObject.toJSONString(new HashMap<String, Object>() {{
                put("deviceNames", deviceNameList);
                put("tenantIds", tenantIdList);
            }});
            String otHeaders = "{\"tenantId\":" + tenantId + "}";
            Result<Map<String, Integer>> r = otController.propertyOfDeviceByNamesWithoutTenant("State", otHeaders, body);
            if (r == null) {
                return Result.error("查询状态失败");
            }
            if (r.getSignal() == null) {
                return Result.error("查询状态失败:" + r.getMessage());
            }
            if (!r.getSignal()) {
                return Result.error(r.getMessage());
            }
            Map<String, Integer> tenantIdDeviceNameStateMap = r.getResult();

            // 统计
            tenantIdDeviceNameStateMap.values().stream().forEach(t -> {
                deviceStateCounter.add(t);
            });
            totalCount += deviceNameList.size();
        }
        
        Integer faultCount = deviceStateCounter.count(DeviceStateEnum.FAULT.getValue());
        Integer taskCount = deviceStateCounter.count(DeviceStateEnum.TASK.getValue());
        Integer standByCount = deviceStateCounter.count(DeviceStateEnum.STAND_BY.getValue());
        Integer offlineCount = deviceStateCounter.count(DeviceStateEnum.OFF_LINE.getValue());

        DeviceStateCountVo allDeviceStateCountVo = new DeviceStateCountVo();
        allDeviceStateCountVo.setTotalCount(totalCount);
        allDeviceStateCountVo.setFaultCount(faultCount);
        allDeviceStateCountVo.setTaskCount(taskCount);
        allDeviceStateCountVo.setStandByCount(standByCount);
        allDeviceStateCountVo.setOnlineCount(taskCount + standByCount + faultCount);
        allDeviceStateCountVo.setOfflineCount(totalCount - taskCount - standByCount - faultCount);

        return Result.ok(allDeviceStateCountVo);
    }

    public Result<Map<String, PropertyValueWithTime>> propertiesOfDevice(Long tenantId, Long deviceId, String[] properties) {
        DeviceEntity deviceEntity = deviceMapper.selectById(deviceId);
        if (deviceEntity == null) {
            return Result.error("设备不存在");
        }
        if (properties == null || properties.length <= 0) {
            return Result.error("属性不能为空");
        }
        String otHeaders = "{\"tenantId\":" + (DictConstant.IS_PMO.equals(deviceEntity.getIsPmo()) ? defaultTenantId : deviceEntity.getTenantId()) + "}";
        String propertiesStr = String.join(",", properties);
        Result<Map<String, PropertyValueWithTime>> r = otController.propertiesAndTimesOfDeviceByName(deviceEntity.getDeviceAliasName(), otHeaders, propertiesStr);
        if (r == null) {
            return Result.error("查询设备属性失败");
        }
        if (r.getSignal() == null) {
            return Result.error("查询设备属性失败:" + r.getMessage());
        }
        if (!r.getSignal()) {
            return Result.error(r.getMessage());
        }
        return r;
    }

    public Result<Pair<LocalDateTime, LocalDateTime>> checkBeginEnd(String begin, String end) {
        LocalDateTime beginTime = LocalDateTime.parse(begin, formatter);
        LocalDateTime endTime = LocalDateTime.parse(end, formatter);
        if (beginTime.isAfter(endTime)) {
            return Result.error("查询开始时间不能大于查询结束时间");
        }
        return Result.ok(Pair.of(beginTime, endTime));
    }

    private ITResult<Object> doquery(
            String serviceName, Long tenantId, Long deviceId, String begin, String end
    ) {
        Result<Pair<LocalDateTime, LocalDateTime>> checkResult = checkBeginEnd(begin, end);
        if (!checkResult.getSignal()) {
            return ITResult.fail(checkResult.getMessage());
        }
        return doquery(serviceName, tenantId, deviceId, begin, end, null, null, null);
    }

    private ITResult<Object> doquery(
            String serviceName, Long tenantId, Long deviceId, String begin, String end,
            String every
    ) {
        return doquery(serviceName, tenantId, deviceId, begin, end, every, null, null);
    }

    public ITResult<Object> doquery(
            String serviceName, Long tenantId, Long deviceId, String begin, String end,
            String every, String deviceIds
    ) {
        return doquery(serviceName, tenantId, deviceId, begin, end, every, deviceIds, null);
    }

    private ITResult<Object> doquery(
            String serviceName, Long tenantId, Long deviceId, String begin, String end,
            String every, String deviceIds, Integer n
    ) {
        DeviceEntity deviceEntity = deviceMapper.selectById(deviceId);
        String alias = deviceEntity.getDeviceAliasName();
        String otHeaders = "{\"tenantId\":" + tenantId + "}";
        DoServiceTaskDto dto = new DoServiceTaskDto();
        dto.setDeviceName(alias);
        dto.setServiceName(serviceName);
        dto.setTenantId(tenantId);
        Map<String, Object> input = new HashMap<>();
        input.put("begin", begin);
        input.put("end", end);
        if (every != null && !"".equals(every)) {
            input.put("every", every);
        }
        if (deviceIds != null && !"".equals(deviceIds)) {
            input.put("deviceIds", deviceIds);
        }
        if (n != null) {
            input.put("n", n);
        }
        dto.setInput(input);
        String body = JSONObject.toJSONString(dto);
        ITResult<Object> r = otController.doServiceByNameWithoutContext(otHeaders, body);
        if(r == null){
            return ITResult.fail("执行查询失败：" + serviceName);
        }

        if(!ITResult.SUCCESS_CODE.equals(r.getCode())){
            r.setMessage("执行查询失败，设备名：" + deviceEntity.getDeviceName() + ", " + r.getMessage());
        }

        return r;
    }

    public ITResult<Object> queryOnLineTimeSumOfDevice( Long tenantId, Long deviceId, String begin, String end ) {
        DeviceEntity device = deviceMapper.selectById(deviceId);
        String flux = readFluxStr("queryOnLineTimeSumOfDevice.flux");
        if (flux.isEmpty()) {
            log.warn("无法读取flux文件，使用默认查询方式");
        } else {
            log.debug("成功读取flux文件，内容长度: {}", flux.length());
            // 如果需要替换flux中的占位符，可以在这里进行
            flux = flux.replace("{tenantId}", tenantId.toString());
            flux = flux.replace("{deviceId}", genDeviceCondition(Arrays.asList(deviceId.toString())));
            flux = flux.replace("{begin}", begin);
            flux = flux.replace("{end}", end);
        }

        QueryDeviceRuntimeDataRequest request = buildQueryDeviceRuntimeDataRequest(flux, (DictConstant.IS_PMO.equals(device.getIsPmo()) ? defaultTenantId : device.getTenantId()));

        ITResult<Object> r = executeFlux(request);
        if(r == null){
            return ITResult.fail("执行查询失败：queryOnLineTimeSumOfDevice方法");
        }
        return r;
    }


    private QueryDeviceRuntimeDataRequest buildQueryDeviceRuntimeDataRequest(String flux, Long tenantId){
        QueryDeviceRuntimeDataRequest request = new QueryDeviceRuntimeDataRequest();
        try {
            request.setFluxQuery(URLEncoder.encode(flux, "UTF-8"));
        } catch (UnsupportedEncodingException e) {
           log.error(e.getMessage(), e);
        }
        request.setTenantId(tenantId);
        return request;
    }

    private ITResult<Object> executeFlux(QueryDeviceRuntimeDataRequest request){
        String otHeaders = "{\"tenantId\":" + request.getTenantId() + "}";
        String body =JSONObject.toJSONString(request);
        ITResult<Object> r = otController.queryDeviceRuntimeData(otHeaders, body);
        return r;
    }
 
    private String genDeviceCondition(List<String> deviceIds){
        if(deviceIds == null || deviceIds.isEmpty()){
            log.warn("deviceIds为空，使用默认查询方式");
            return "";
        }else if(deviceIds.size() == 1){
            return deviceIds.get(0).toString();
        }
        return   String.join("|", deviceIds.stream().map(String::valueOf).collect(Collectors.toList()));
    }

    private String readFluxStr(String fluxName) {
        try {
            // 使用类路径资源读取文件，这样在JAR包中也能正常工作
            InputStream inputStream = getClass().getClassLoader().getResourceAsStream("flux/" + fluxName);
            if (inputStream == null) {
                log.error("无法找到flux文件: flux/{}", fluxName);
                return "";
            }
            
            // 使用try-with-resources确保资源正确关闭
            try (BufferedReader reader = new BufferedReader(new InputStreamReader(inputStream, StandardCharsets.UTF_8))) {
                StringBuilder content = new StringBuilder();
                String line;
                while ((line = reader.readLine()) != null) {
                    content.append(line).append(System.lineSeparator());
                }
                return content.toString();
            }
        } catch (IOException e) {
            log.error("读取flux文件失败: flux/{}", fluxName, e);
            return "";
        }
    }


    public ITResult<Object> queryStandByTimeSumOfDevice(
            Long tenantId, Long deviceId, String begin, String end
    ) {
        DeviceEntity device = deviceMapper.selectById(deviceId);
        return doquery("queryStandByTimeSum", (DictConstant.IS_PMO.equals(device.getIsPmo()) ? defaultTenantId : device.getTenantId()), deviceId, begin, end);
    }

    public ITResult<Object> queryTaskTimeSumOfDevice(
            Long tenantId, Long deviceId, String begin, String end
    ) {
        DeviceEntity device = deviceMapper.selectById(deviceId);
        return doquery("queryTaskTimeSum", (DictConstant.IS_PMO.equals(device.getIsPmo()) ? defaultTenantId : device.getTenantId()), deviceId, begin, end);
    }

    public ITResult<Object> queryFaultTimeSumOfDevice(
            Long tenantId, Long deviceId, String begin, String end
    ) {
        DeviceEntity device = deviceMapper.selectById(deviceId);
        return doquery("queryFaultTimeSum", (DictConstant.IS_PMO.equals(device.getIsPmo()) ? defaultTenantId : device.getTenantId()), deviceId, begin, end);
    }

    public ITResult<Object> queryFaultTimeAvgOfDevice(
            Long tenantId, Long deviceId, String begin, String end
    ) {
        DeviceEntity device = deviceMapper.selectById(deviceId);
        return doquery("queryFaultTimeAvg", (DictConstant.IS_PMO.equals(device.getIsPmo()) ? defaultTenantId : device.getTenantId()), deviceId, begin, end);

    }

    public ITResult<Object> queryFaultRateOfDevice(
            Long tenantId, Long deviceId, String begin, String end
    ) {
        DeviceEntity device = deviceMapper.selectById(deviceId);
        // 换成ot的设备id
        String otHeaders = "{\"tenantId\":" + tenantId + "}";
        if(Objects.equals(device.getIsPmo(), DictConstant.IS_PMO)){
            otHeaders = "{\"tenantId\":" + defaultTenantId + "}";
        }
        String deviceAliasName = device.getDeviceAliasName();
        List<String> aliasList = Collections.singletonList(deviceAliasName);
        Map<String, Object> param = new HashMap<>();
        param.put("deviceNames", aliasList);
        if(!tenantId.equals(defaultTenantId) && Objects.equals(device.getIsPmo(), DictConstant.IS_PMO)){
            param.put("customerIds", defaultTenantId);
        }
        String body = JSONObject.toJSONString(param);
        log.info(">>>>>>>> aliasList:{}", JSONObject.toJSONString(aliasList));
        Result<List<String>> deviceIdsResult = otController.deviceNamesToIds(otHeaders, body);

        if (deviceIdsResult == null) {
            return ITResult.fail("设备别名没有对应数据");
        }
        log.info(">>>>>>>> deviceIdsResult:{}",JSONObject.toJSONString(deviceIdsResult));
        if (deviceIdsResult.getSignal() == null) {
            return ITResult.fail("设备别名没有对应数据：" + deviceIdsResult.getMessage());
        }
        if (!deviceIdsResult.getSignal()) {
            return ITResult.fail(deviceIdsResult.getMessage());
        }
        List<String> deviceIds = deviceIdsResult.getResult();

        if(deviceIds == null){
            return ITResult.fail("设备别名没有对应数据");
        }
        String deviceIdsStr = String.join(",", deviceIds);
        return doquery("queryDeviceListRunningInfo", (DictConstant.IS_PMO.equals(device.getIsPmo()) ? defaultTenantId : device.getTenantId()), deviceId, begin, end,null,deviceIdsStr,null);
    }

    public ITResult<Object> queryUseRateOfDevice(
            Long tenantId, Long deviceId, String begin, String end
    ) {
        DeviceEntity device = deviceMapper.selectById(deviceId);
        return doquery("queryUseRate", (DictConstant.IS_PMO.equals(device.getIsPmo()) ? defaultTenantId : device.getTenantId()), deviceId, begin, end);
    }

    public ITResult<Object> queryHealthDegreeOfDevice(
            Long tenantId, Long deviceId, String begin, String end
    ) {
        DeviceEntity device = deviceMapper.selectById(deviceId);
        return doquery("queryHealthDegree", (DictConstant.IS_PMO.equals(device.getIsPmo()) ? defaultTenantId : device.getTenantId()), deviceId, begin, end);
    }

    private static final DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm:ss");

    public Result<String> checkAndParseEvery(String begin, String end) {
        Result<Pair<LocalDateTime, LocalDateTime>> checkResult = checkBeginEnd(begin, end);
        if (!checkResult.getSignal()) {
            return Result.error(checkResult.getMessage());
        }
        Pair<LocalDateTime, LocalDateTime> pair = checkResult.getResult();
        LocalDateTime beginTime = pair.getLeft();
        LocalDateTime endTime = pair.getRight();
        return Result.ok(parseEvery(beginTime, endTime));
    }

    private String parseEvery(LocalDateTime beginTime, LocalDateTime endTime) {
        if (beginTime.plusHours(24).plusSeconds(1).isAfter(endTime)) {
            // 查询范围小于24小时，按小时查询
            return "1h";
        } else if (beginTime.plusDays(30).isAfter(endTime)) {
            // 查询范围小于30天，按天查询
            return "1d";
        } else if (beginTime.plusDays(30 * 5).isAfter(endTime)) {
            // 查询范围在1-5个月，按周查询
            return "1w";
        } else {
            // 查询范围在5个月以上，按月查询
            return "1mo";
        }
    }

    public ITResult<Object> queryFaultCountByWindowOfDevice(
            Long tenantId, Long deviceId, String begin, String end, String every
    ) {
        Result<Pair<LocalDateTime, LocalDateTime>> checkResult = checkBeginEnd(begin, end);
        if (!checkResult.getSignal()) {
            return ITResult.fail(checkResult.getMessage());
        }
        if (every == null || "".equals(every)) {
            Pair<LocalDateTime, LocalDateTime> pair = checkResult.getResult();
            LocalDateTime beginTime = pair.getLeft();
            LocalDateTime endTime = pair.getRight();
            every = parseEvery(beginTime, endTime);
        }
        DeviceEntity device = deviceMapper.selectById(deviceId);
        return doquery("queryFaultCountByWindow", (DictConstant.IS_PMO.equals(device.getIsPmo()) ? defaultTenantId : device.getTenantId()), deviceId, begin, end, every);
    }


    public ITResult<Object> queryUseRateByWindowOfDevice(
            Long tenantId, Long deviceId, String begin, String end, String every
    ) {
        Result<Pair<LocalDateTime, LocalDateTime>> checkResult = checkBeginEnd(begin, end);
        if (!checkResult.getSignal()) {
            return ITResult.fail(checkResult.getMessage());
        }
        if (every == null || "".equals(every)) {
            Pair<LocalDateTime, LocalDateTime> pair = checkResult.getResult();
            LocalDateTime beginTime = pair.getLeft();
            LocalDateTime endTime = pair.getRight();
            every = parseEvery(beginTime, endTime);
        }
        DeviceEntity device = deviceMapper.selectById(deviceId);
        return doquery("queryUseRateByWindow", (DictConstant.IS_PMO.equals(device.getIsPmo()) ? defaultTenantId : device.getTenantId()), deviceId, begin, end, every);
    }

    public ITResult<Object> queryHealthDegreeByWindowOfDevice(
            Long tenantId, Long deviceId, String begin, String end, String every
    ) {
        Result<Pair<LocalDateTime, LocalDateTime>> checkResult = checkBeginEnd(begin, end);
        if (!checkResult.getSignal()) {
            return ITResult.fail(checkResult.getMessage());
        }
        if (every == null || "".equals(every)) {
            Pair<LocalDateTime, LocalDateTime> pair = checkResult.getResult();
            LocalDateTime beginTime = pair.getLeft();
            LocalDateTime endTime = pair.getRight();
            every = parseEvery(beginTime, endTime);
        }
        DeviceEntity device = deviceMapper.selectById(deviceId);
        return doquery("queryHealthDegreeByWindow", (DictConstant.IS_PMO.equals(device.getIsPmo()) ? defaultTenantId : device.getTenantId()), deviceId, begin, end, every);
    }

    private Result<String> sameTypeDeviceIds(Long deviceId, Long deviceTenantId, TenantIsolation tenant) {
        Result<List<String>> deviceIdsResult = sameTypeDeviceIdList(deviceId, deviceTenantId, tenant);
        if (!deviceIdsResult.getSignal()) {
            return Result.error(deviceIdsResult.getMessage());
        }
        List<String> deviceIds = deviceIdsResult.getResult();
        String deviceIdsStr = String.join(",", deviceIds);
        return Result.ok(deviceIdsStr);
    }

    /**
     * 获取同类设备的设备ID列表
     */
    private Result<List<String>> sameTypeDeviceIdList(Long deviceId, Long deviceTenantId, TenantIsolation tenant) {

        List<Long> tenantIds=Lists.newArrayList();
        boolean isNtiCustomer = isNtiCustomer(tenant);
        if (isNtiCustomer) {
            //如果当前租户是今天国际的客户
            if (!deviceTenantId.equals(tenant.getTenantId())) {
                //设备的租户id是pmoCustomerId
                tenantIds.add(deviceTenantId);   //查看同样是今天国际为当前客户创建的设备作为同类设备，不包含自己创建但挂载今天国际的设备分类下
            } else {
                //设备是租户自行创建的，那么设备的租户id就是当前租户id
                tenantIds.add(tenant.getTenantId());  //查看自己创建,设备分类与改设备相同。可以是今天国际的设备分类，也可以是自己创建的
            }
        }else{
            //如果当前租户不是今天国际的客户
            if(!deviceTenantId.equals(tenant.getTenantId())){
                //设备是授权设备,或者当前租户是今天国际，查看客户的设备
                tenantIds.add(deviceTenantId); //查看授权租户下
            }else{
                tenantIds.add(tenant.getTenantId());
            }
        }


        // 获取同类设备的别名列表
        Result<List<String>> aliasListResult = getSameTypeDeviceAliasList(deviceId,  tenantIds);
        if (!aliasListResult.getSignal()) {
            return Result.error(aliasListResult.getMessage());
        }
        
        List<String> aliasList = aliasListResult.getResult();
        if (aliasList == null || aliasList.isEmpty()) {
            return Result.ok(new ArrayList<>());
        }

        // 通过别名获取设备ID
        return getDeviceIdsByAliasList(aliasList, tenant.getTenantId());
    }

    /**
     * 获取同类设备的别名列表
     * @param deviceId 设备ID
     * @param tenantId 租户ID
     * @return 同类设备别名列表
     */
    private Result<List<String>> getSameTypeDeviceAliasList(Long deviceId, List<Long> tenantIds) {
        DeviceEntity deviceEntity = deviceMapper.selectById(deviceId);
        if (deviceEntity == null) {
            return Result.error("设备不存在");
        }
        
        Long deviceTypeId = deviceEntity.getDeviceTypeId();
        List<DeviceEntity> sameTypeDeviceEntityList = deviceMapper.listDeviceByDeviceTypes(tenantIds.get(0), Arrays.asList(deviceTypeId));
        if (sameTypeDeviceEntityList == null || sameTypeDeviceEntityList.size() <= 0) {
            return Result.error("没有同类设备");
        }
        
        List<String> aliasList = sameTypeDeviceEntityList.stream()
                .filter(t -> ConnectOtEnum.CONNECT.getValue().equals(t.getConnectOt()))
                .map(DeviceEntity::getDeviceAliasName)
                .filter(t -> t != null && !"".equals(t))
                .collect(Collectors.toList());
                
        if (aliasList == null || aliasList.size() <= 0) {
            return Result.error("同类设备别名为空");
        }
        
        return Result.ok(aliasList);
    }

    /**
     * 通过设备别名列表获取设备ID列表
     * @param aliasList 设备别名列表
     * @param tenantId 租户ID
     * @return 设备ID列表
     */
    private Result<List<String>> getDeviceIdsByAliasList(List<String> aliasList, Long tenantId) {
        if (aliasList == null || aliasList.isEmpty()) {
            return Result.ok(new ArrayList<>());
        }

        // 换成ot的设备id
        String otHeaders = "{\"tenantId\":" + tenantId + "}";
        String body = JSONObject.toJSONString(new HashMap<String, Object>() {{
            put("deviceNames", aliasList);
        }});
        Result<List<String>> deviceIdsResult = otController.deviceNamesToIds(otHeaders, body);

        if (deviceIdsResult == null) {
            return Result.error("设备别名没有对应数据");
        }
        if (deviceIdsResult.getSignal() == null) {
            return Result.error("设备别名没有对应数据：" + deviceIdsResult.getMessage());
        }
        if (!deviceIdsResult.getSignal()) {
            return Result.error(deviceIdsResult.getMessage());
        }
        List<String> deviceIds = deviceIdsResult.getResult();

        if(deviceIds == null){
            return Result.ok(new ArrayList<>());
        }
        return Result.ok(deviceIds);
    }

    /**
     * 获取租户下所有设备的别名列表
     * @param tenantId 租户ID
     * @return 设备别名列表
     */
    private Result<List<String>> getAllDeviceAliasList(Long tenantId) {
        // 获取所有设备的别名
        List<DeviceEntity> allDeviceEntityList = deviceMapper.listAllDevice(tenantId);
        List<String> aliasList = allDeviceEntityList.stream()
                .filter(t -> ConnectOtEnum.CONNECT.getValue().equals(t.getConnectOt()))
                .map(DeviceEntity::getDeviceAliasName)
                .filter(t -> t != null && !"".equals(t))
                .collect(Collectors.toList());
                
        if (aliasList == null || aliasList.size() <= 0) {
            return Result.ok(new ArrayList<>());
        }
        
        return Result.ok(aliasList);
    }

    private Result<List<String>> allDeviceIds(Long tenantId) {
        // 获取所有设备的别名列表
        Result<List<String>> aliasListResult = getAllDeviceAliasList(tenantId);
        if (!aliasListResult.getSignal()) {
            return Result.error(aliasListResult.getMessage());
        }
        
        List<String> aliasList = aliasListResult.getResult();
        if (aliasList.isEmpty()) {
            return Result.ok(new ArrayList<>());
        }

        // 通过别名获取设备ID
        return getDeviceIdsByAliasList(aliasList, tenantId);
    }

    /**
     * 获取所有设备的id，如果当前的租户是今天国际的客户，并且拥有自己创建的设备，那么需要根据 tenantId、pmoCustomerId查询设备
     * 从OT获取设备id时，需要根据defaultTenantId、tenantId 查询设备，今天国际集成商创建的数据属于defaultTenantId，客户自己创建的设备属于tenantId
     * @param defaultTenantId
     * @param tenantId
     * @param pmoCustomerId
     * @return
     */
    private Result<List<String>> allDeviceIdsByCustomerId(Long defaultTenantId,Long tenantId,Long pmoCustomerId) {
        // 获取所有设备的别名
        Set<Long> tenantIdset=Sets.newHashSet();
        tenantIdset.add(tenantId);
        tenantIdset.add(pmoCustomerId);
        // 获取所有设备的别名
        List<DeviceEntity> allDeviceEntityList = deviceMapper.listAllDeviceByTenantIds(tenantIdset);
        log.info(">>>>>>>> customerId:{},allDeviceIdsByCustomerId:{}",pmoCustomerId,allDeviceEntityList.size());
        List<String> aliasList = allDeviceEntityList.stream()
                .filter(t -> {
                    return ConnectOtEnum.CONNECT.getValue().equals(t.getConnectOt());
                })
                .map(DeviceEntity::getDeviceAliasName)
                .filter(t -> {
                    return t != null && !"".equals(t);
                })
                .collect(Collectors.toList());
        if (aliasList == null || aliasList.size() <= 0) {
            return Result.error("同类设备别名为空");
        }

        // 换成ot的设备id
        String otHeaders = "{\"tenantId\":" + tenantId + "}";
        if(!tenantId.equals(pmoCustomerId)){
            otHeaders = "{\"tenantId\":" + defaultTenantId + "}";
        }
       
        Map<String, Object> param = new HashMap<>();
        param.put("deviceNames", aliasList);
        if(tenantId!=null&&!tenantId.equals(defaultTenantId)){
            param.put("customerIds", tenantId);
        }
        String body = JSONObject.toJSONString(param);
        log.info(">>>>>>>> aliasList:{}", JSONObject.toJSONString(aliasList));
        Result<List<String>> deviceIdsResult = otController.deviceNamesToIds(otHeaders, body);

        if (deviceIdsResult == null) {
            return Result.error("设备别名没有对应数据");
        }
        log.info(">>>>>>>> deviceIdsResult:{}",JSONObject.toJSONString(deviceIdsResult));
        if (deviceIdsResult.getSignal() == null) {
            return Result.error("设备别名没有对应数据：" + deviceIdsResult.getMessage());
        }
        if (!deviceIdsResult.getSignal()) {
            return Result.error(deviceIdsResult.getMessage());
        }
        return deviceIdsResult;
    }

    public ITResult<Object> queryDevicesUseRateByWindowOfDevice(
        TenantIsolation tenant, Long deviceId, String begin, String end, String every
    ) {

        Result<Pair<LocalDateTime, LocalDateTime>> checkResult = checkBeginEnd(begin, end);
        if (!checkResult.getSignal()) {
            return ITResult.fail(checkResult.getMessage());
        }
        if (every == null || "".equals(every)) {
            Pair<LocalDateTime, LocalDateTime> pair = checkResult.getResult();
            LocalDateTime beginTime = pair.getLeft();
            LocalDateTime endTime = pair.getRight();
            every = parseEvery(beginTime, endTime);
        }
        DeviceEntity device = deviceMapper.selectById(deviceId);
        Result<String> deviceIdsResult = sameTypeDeviceIds(deviceId, device.getTenantId(),tenant);
        if (!deviceIdsResult.getSignal()) {
            return ITResult.succeed(deviceIdsResult.getMessage());
        }
        String deviceIdsStr = deviceIdsResult.getResult();

        return doquery("queryDevicesUseRateByWindow", (DictConstant.IS_PMO.equals(device.getIsPmo()) ? defaultTenantId : device.getTenantId()), deviceId, begin, end, every, deviceIdsStr);
    }

    public ITResult<Object> queryOnLineTimeByWindowOfDevice( Long tenantId, Long deviceId, String begin, String end, String every ) {
        Result<Pair<LocalDateTime, LocalDateTime>> checkResult = checkBeginEnd(begin, end);
        if (!checkResult.getSignal()) {
            return ITResult.fail(checkResult.getMessage());
        }
        
        DeviceEntity device = deviceMapper.selectById(deviceId);
        Result<List<String>> otDeviceIdList=getDeviceIdsByAliasList(Arrays.asList(device.getDeviceAliasName()), tenantId);
        String flux = readFluxStr("queryDevicesOnLineTimeByWindow.flux");
        if (flux.isEmpty()) {
            log.warn("无法读取flux文件，使用默认查询方式");
        } else {
            log.debug("成功读取flux文件，内容长度: {}", flux.length());
            // 如果需要替换flux中的占位符，可以在这里进行
            Long finalTenantId = DictConstant.IS_PMO.equals(device.getIsPmo()) ? defaultTenantId : device.getTenantId();
            flux = flux.replace("{tenantId}", finalTenantId.toString());
            flux = flux.replace("{deviceIds}", genDeviceCondition(otDeviceIdList.getResult()));
            flux = flux.replace("{begin}", begin);
            flux = flux.replace("{end}", end);
        }
        QueryDeviceRuntimeDataRequest request = buildQueryDeviceRuntimeDataRequest(flux, tenantId);

        ITResult<Object> r = executeFlux(request);
        if(r == null){
            return ITResult.fail("执行查询失败：queryOnLineTimeByWindowOfDevice方法");
        }
        return r;
    }


    public ITResult<Object> queryDevicesOnLineTimeByWindowOfDevice( TenantIsolation tenant, Long deviceId, String begin, String end, String every ) {
        Result<Pair<LocalDateTime, LocalDateTime>> checkResult = checkBeginEnd(begin, end);
        if (!checkResult.getSignal()) {
            return ITResult.fail(checkResult.getMessage());
        }

        DeviceEntity device = deviceMapper.selectById(deviceId);
        Result<List<String>> deviceIdsResult = sameTypeDeviceIdList(deviceId, device.getTenantId(),tenant);
        if (!deviceIdsResult.getSignal()) {
            return ITResult.succeed(deviceIdsResult.getMessage());
        }
        List<String> deviceIds = deviceIdsResult.getResult();

        String flux = readFluxStr("queryDevicesOnLineTimeByWindow.flux");
        if (flux.isEmpty()) {
            log.warn("无法读取flux文件，使用默认查询方式");
        } else {
            log.debug("成功读取flux文件，内容长度: {}", flux.length());
            // 如果需要替换flux中的占位符，可以在这里进行
            Long finalTenantId = DictConstant.IS_PMO.equals(device.getIsPmo()) ? defaultTenantId : device.getTenantId();
            flux = flux.replace("{tenantId}", finalTenantId.toString());
            flux = flux.replace("{deviceIds}", genDeviceCondition(deviceIds));
            flux = flux.replace("{begin}", begin);
            flux = flux.replace("{end}", end);
        }
        QueryDeviceRuntimeDataRequest request = buildQueryDeviceRuntimeDataRequest(flux, tenant.getTenantId());

        ITResult<Object> r = executeFlux(request);
        if(r == null){
            return ITResult.fail("执行查询失败：queryDevicesOnLineTimeByWindowOfDevice方法");
        }
        return r;
    }

    private ITResult<DeviceOnLineTimePointDTO> transforToDeviceOnLineTimePointDTO(ITResult<Object> r) {
        if (r == null) {
            return ITResult.fail("转换失败");
        }
        if (!r.getCode().equals(ITResult.SUCCESS_CODE)) {
            return ITResult.fail(r.getMessage());
        }
        Object data = r.getData();
        if (data == null) {
            return ITResult.fail("转换失败");
        }
        if (data instanceof DeviceOnLineTimePointDTO) {
            return ITResult.succeed((DeviceOnLineTimePointDTO) data);
        }
    }


    public ITResult<Object> queryTaskTimeByWindowOfDevice(
            Long tenantId, Long deviceId, String begin, String end, String every
    ) {
        Result<Pair<LocalDateTime, LocalDateTime>> checkResult = checkBeginEnd(begin, end);
        if (!checkResult.getSignal()) {
            return ITResult.fail(checkResult.getMessage());
        }
        if (every == null || "".equals(every)) {
            Pair<LocalDateTime, LocalDateTime> pair = checkResult.getResult();
            LocalDateTime beginTime = pair.getLeft();
            LocalDateTime endTime = pair.getRight();
            every = parseEvery(beginTime, endTime);
        }

        DeviceEntity device = deviceMapper.selectById(deviceId);
        return doquery("queryTaskTimeByWindow",  (DictConstant.IS_PMO.equals(device.getIsPmo()) ? tenantId : device.getTenantId()), deviceId, begin, end, every);
    }


    public ITResult<Object> queryDevicesTaskTimeByWindowOfDevice(
        TenantIsolation tenant, Long deviceId, String begin, String end, String every
    ) {
        Result<Pair<LocalDateTime, LocalDateTime>> checkResult = checkBeginEnd(begin, end);
        if (!checkResult.getSignal()) {
            return ITResult.fail(checkResult.getMessage());
        }
        if (every == null || "".equals(every)) {
            Pair<LocalDateTime, LocalDateTime> pair = checkResult.getResult();
            LocalDateTime beginTime = pair.getLeft();
            LocalDateTime endTime = pair.getRight();
            every = parseEvery(beginTime, endTime);
        }
        DeviceEntity device = deviceMapper.selectById(deviceId);

        Result<String> deviceIdsResult = sameTypeDeviceIds(deviceId, device.getTenantId(),tenant);
        if (!deviceIdsResult.getSignal()) {
            return ITResult.succeed(deviceIdsResult.getMessage());
        }
        String deviceIdsStr = deviceIdsResult.getResult();
        return doquery("queryDevicesTaskTimeByWindow",  (DictConstant.IS_PMO.equals(device.getIsPmo()) ? tenant.getTenantId() : device.getTenantId()), deviceId, begin, end, every, deviceIdsStr);
    }


    public ITResult<Object> queryStandByTimeByWindowOfDevice(
            Long tenantId, Long deviceId, String begin, String end, String every
    ) {
        Result<Pair<LocalDateTime, LocalDateTime>> checkResult = checkBeginEnd(begin, end);
        if (!checkResult.getSignal()) {
            return ITResult.fail(checkResult.getMessage());
        }
        if (every == null || "".equals(every)) {
            Pair<LocalDateTime, LocalDateTime> pair = checkResult.getResult();
            LocalDateTime beginTime = pair.getLeft();
            LocalDateTime endTime = pair.getRight();
            every = parseEvery(beginTime, endTime);
        }
        DeviceEntity device = deviceMapper.selectById(deviceId);

        return doquery("queryStandByTimeByWindow",  (DictConstant.IS_PMO.equals(device.getIsPmo()) ? tenantId : device.getTenantId()), deviceId, begin, end, every);
    }

    public ITResult<Object> queryDevicesStandByTimeByWindowOfDevice(
            TenantIsolation tenant, Long deviceId, String begin, String end, String every
    ) {
        Result<Pair<LocalDateTime, LocalDateTime>> checkResult = checkBeginEnd(begin, end);
        if (!checkResult.getSignal()) {
            return ITResult.fail(checkResult.getMessage());
        }
        if (every == null || "".equals(every)) {
            Pair<LocalDateTime, LocalDateTime> pair = checkResult.getResult();
            LocalDateTime beginTime = pair.getLeft();
            LocalDateTime endTime = pair.getRight();
            every = parseEvery(beginTime, endTime);
        }
        DeviceEntity device = deviceMapper.selectById(deviceId);
        Result<String> deviceIdsResult = sameTypeDeviceIds(deviceId, device.getTenantId(),tenant);
        if (!deviceIdsResult.getSignal()) {
            return ITResult.succeed(deviceIdsResult.getMessage());
        }
        String deviceIdsStr = deviceIdsResult.getResult();
        return doquery("queryDevicesStandByTimeByWindow",  (DictConstant.IS_PMO.equals(device.getIsPmo()) ? tenant.getTenantId() : device.getTenantId()), deviceId, begin, end, every, deviceIdsStr);
    }


    public ITResult<Object> queryFaultTimeByWindowOfDevice(
            Long tenantId, Long deviceId, String begin, String end, String every
    ) {
        Result<Pair<LocalDateTime, LocalDateTime>> checkResult = checkBeginEnd(begin, end);
        if (!checkResult.getSignal()) {
            return ITResult.fail(checkResult.getMessage());
        }
        if (every == null || "".equals(every)) {
            Pair<LocalDateTime, LocalDateTime> pair = checkResult.getResult();
            LocalDateTime beginTime = pair.getLeft();
            LocalDateTime endTime = pair.getRight();
            every = parseEvery(beginTime, endTime);
        }
        DeviceEntity device = deviceMapper.selectById(deviceId);
        return doquery("queryFaultTimeByWindow",  (DictConstant.IS_PMO.equals(device.getIsPmo()) ? tenantId : device.getTenantId()), deviceId, begin, end, every);
    }

    public ITResult<Object> queryDevicesFaultTimeByWindowOfDevice(
            TenantIsolation tenant, Long deviceId, String begin, String end, String every
    ) {
        Result<Pair<LocalDateTime, LocalDateTime>> checkResult = checkBeginEnd(begin, end);
        if (!checkResult.getSignal()) {
            return ITResult.fail(checkResult.getMessage());
        }
        if (every == null || "".equals(every)) {
            Pair<LocalDateTime, LocalDateTime> pair = checkResult.getResult();
            LocalDateTime beginTime = pair.getLeft();
            LocalDateTime endTime = pair.getRight();
            every = parseEvery(beginTime, endTime);
        }
        DeviceEntity device = deviceMapper.selectById(deviceId);

        Result<String> deviceIdsResult = sameTypeDeviceIds(deviceId, device.getTenantId(),tenant);
        if (!deviceIdsResult.getSignal()) {
            return ITResult.succeed(deviceIdsResult.getMessage());
        }
        String deviceIdsStr = deviceIdsResult.getResult();
        return doquery("queryDevicesFaultTimeByWindow",  (DictConstant.IS_PMO.equals(device.getIsPmo()) ? tenant.getTenantId() : device.getTenantId()), deviceId, begin, end, every, deviceIdsStr);
    }

    public ITResult<Object> queryFaultTimeByWindowOfAllDevice(
            Long tenantId, String begin, String end, String every
    ) {
        Result<Pair<LocalDateTime, LocalDateTime>> checkResult = checkBeginEnd(begin, end);
        if (!checkResult.getSignal()) {
            return ITResult.fail(checkResult.getMessage());
        }
        if (every == null || "".equals(every)) {
            Pair<LocalDateTime, LocalDateTime> pair = checkResult.getResult();
            LocalDateTime beginTime = pair.getLeft();
            LocalDateTime endTime = pair.getRight();
            every = parseEvery(beginTime, endTime);
        }
        DeviceEntity firstDevice = deviceMapper.getFirstDevice(tenantId);
        if (firstDevice == null) {
            return ITResult.succeed("没有设备");
        }
        Long deviceId = firstDevice.getId();
        Result<List<String>> deviceIdsResult = allDeviceIds(tenantId);
        if (!deviceIdsResult.getSignal()) {
            return ITResult.fail(deviceIdsResult.getMessage());
        }
        List<String> deviceIds = deviceIdsResult.getResult();

        String deviceIdsStr = String.join(",", deviceIds);
        return doquery("queryDevicesFaultTimeByWindow", tenantId, deviceId, begin, end, every, deviceIdsStr);
    }


    public Result<Page<DeviceStateVo>> pageStateOfDevice(
            Long tenantId, Long deviceId,
            String begin, String end,
            Integer size, Integer current
    ) {
        DeviceEntity deviceEntity = deviceMapper.selectById(deviceId);
        if (deviceEntity == null) {
            return Result.error("设备不存在");
        }
        String alias = deviceEntity.getDeviceAliasName();
        String otHeaders = "{\"tenantId\":" + (DictConstant.IS_PMO.equals(deviceEntity.getIsPmo()) ? defaultTenantId : deviceEntity.getTenantId()) + "}";
        Result<Page<DeviceStateVo>> r = otController.pageStateOfDevice(otHeaders, alias, size, current, begin, end);
        if (r == null) {
            return Result.error("查询设备状态失败");
        }
        if (r.getSignal() == null) {
            return Result.error("查询设备状态失败:" + r.getMessage());
        }
        if (!r.getSignal()) {
            return Result.error(r.getMessage());
        }
        return r;
    }

    public ITResult<Object> queryStandByRateOfAllDevice(
            Long tenantId, String begin, String end
    ) {
        Result<Pair<LocalDateTime, LocalDateTime>> checkResult = checkBeginEnd(begin, end);
        if (!checkResult.getSignal()) {
            return ITResult.fail(checkResult.getMessage());
        }
        DeviceEntity firstDevice = deviceMapper.getFirstDevice(tenantId);
        if (firstDevice == null) {
            return ITResult.succeed("没有设备");
        }
        Long deviceId = firstDevice.getId();
        Result<List<String>> deviceIdsResult = allDeviceIds(tenantId);
        if (!deviceIdsResult.getSignal()) {
            return ITResult.fail(deviceIdsResult.getMessage());
        }
        List<String> deviceIds = deviceIdsResult.getResult();
        String deviceIdsStr = String.join(",", deviceIds);
        return doquery("queryDevicesStandByRate", tenantId, deviceId, begin, end, null, deviceIdsStr);
    }


    public ITResult<Object> queryUseRateOfAllDevice(
            TenantIsolation tenant, String begin, String end
    ) {
        log.info(">>>>>>> queryUseRateOfAllDevice start");
        Result<Pair<LocalDateTime, LocalDateTime>> checkResult = checkBeginEnd(begin, end);
        if (!checkResult.getSignal()) {
            return ITResult.fail(checkResult.getMessage());
        }
        if(isNtiCustomer(tenant)) {
            Result<TenantInfoEntity> tenantInfoEntityResult = tenantInfoService.getById(tenant.getTenantId(), tenant.getTenantId());
            if (!tenantInfoEntityResult.getSignal()|| tenantInfoEntityResult.getResult()==null) {
                log.warn("通过tenantId:{},找不到TenantInfo",tenant.getTenantId());
                return ITResult.fail(tenantInfoEntityResult.getMessage());
            }
            Long customerPmoTenantId = tenantInfoEntityResult.getResult().getPmoTenantId();
            DeviceEntity firstDevice = deviceMapper.getFirstDevice(customerPmoTenantId);
            if (firstDevice == null) {
                return ITResult.succeed("没有设备");
            }
            Long deviceId = firstDevice.getId();
            Result<List<String>> deviceIdsResult = allDeviceIdsByCustomerId(defaultTenantId,tenant.getTenantId(),customerPmoTenantId);
            if (!deviceIdsResult.getSignal()&& deviceIdsResult.getResult()==null) {
                return ITResult.fail(deviceIdsResult.getMessage());
            }
            List<String> deviceIds = deviceIdsResult.getResult();
            String deviceIdsStr = String.join(",", deviceIds);
            log.info(">>>>>>> tenant.getTenantId():{},defaultTenantId:{}, deviceId:{},deviceIdsStr:{},isPMOTenant:{}",tenant.getTenantId(),defaultTenantId, deviceId, deviceIdsStr,PmoTenantEnum.IS_PMO_TENANT.getValue().equals(tenantInfoService.getTenantIsPmoTenant(tenant)));
            return doquery("queryDevicesUseRate_new", defaultTenantId, deviceId, begin, end, null, deviceIdsStr);
        }else{
            DeviceEntity firstDevice = deviceMapper.getFirstDeviceNoPmo(tenant.getTenantId());
            if (firstDevice == null) {
                return ITResult.succeed("没有设备");
            }
            Long deviceId = firstDevice.getId();
            Result<List<String>> deviceIdsResult = allDeviceIds(tenant.getTenantId());
            if (!deviceIdsResult.getSignal()) {
                return ITResult.fail(deviceIdsResult.getMessage());
            }
            List<String> deviceIds = deviceIdsResult.getResult();
            if(CollectionUtils.isEmpty(deviceIds)) {
                return ITResult.succeed("没有设备");
            }
            String deviceIdsStr = String.join(",", deviceIds);
            return doquery("queryDevicesUseRate_new", tenant.getTenantId(), deviceId, begin, end, null, deviceIdsStr);
        }
    }

    public ITResult<Object> queryFaultRateOfAllDevice(
            Long tenantId, String begin, String end
    ) {
        Result<Pair<LocalDateTime, LocalDateTime>> checkResult = checkBeginEnd(begin, end);
        if (!checkResult.getSignal()) {
            return ITResult.fail(checkResult.getMessage());
        }
        DeviceEntity firstDevice = deviceMapper.getFirstDevice(tenantId);
        if (firstDevice == null) {
            return ITResult.succeed("没有设备");
        }
        Long deviceId = firstDevice.getId();
        Result<List<String>> deviceIdsResult = allDeviceIds(tenantId);
        if (!deviceIdsResult.getSignal()) {
            return ITResult.fail(deviceIdsResult.getMessage());
        }
        List<String> deviceIds = deviceIdsResult.getResult();
        String deviceIdsStr = String.join(",", deviceIds);
        return doquery("queryDevicesFaultRate", tenantId, deviceId, begin, end, null, deviceIdsStr);
    }


    public ITResult<Object> queryDevicesTaskTimeByWindowOfDeviceType(
            TenantIsolation tenant, Long deviceTypeId, String begin, String end, String every
    ) {
        Long tenantId = tenant.getTenantId();
        Result<Pair<LocalDateTime, LocalDateTime>> checkResult = checkBeginEnd(begin, end);
        if (!checkResult.getSignal()) {
            return ITResult.fail(checkResult.getMessage());
        }
        if (every == null || "".equals(every)) {
            Pair<LocalDateTime, LocalDateTime> pair = checkResult.getResult();
            LocalDateTime beginTime = pair.getLeft();
            LocalDateTime endTime = pair.getRight();
            every = parseEvery(beginTime, endTime);
        }
        Result<List<DeviceTypeTreeDto>> typeTreeResult = deviceTypeService.getTypeTreeById(tenant, deviceTypeId.toString());
        if (!typeTreeResult.getSignal()) {
            return ITResult.fail(typeTreeResult.getMessage());
        }
        List<DeviceTypeTreeDto> deviceTypeTree = typeTreeResult.getResult();
        if (deviceTypeTree == null) {
            deviceTypeTree = new ArrayList<>();
        }
        List<Long> deviceTypeIds = deviceTypeTree.stream().map(DeviceTypeTreeDto::getId).collect(Collectors.toList());
        deviceTypeIds.add(deviceTypeId);
        List<DeviceEntity> devices = deviceMapper.listDeviceByDeviceTypes(tenantId, deviceTypeIds);
        if (devices == null || devices.size() <= 0) {
            return ITResult.succeed("没有设备");
        }

        List<String> aliasList = devices.stream()
                .filter(t -> {
                    return ConnectOtEnum.CONNECT.getValue().equals(t.getConnectOt());
                })
                .map(DeviceEntity::getDeviceAliasName)
                .filter(t -> {
                    return t != null && !"".equals(t);
                })
                .collect(Collectors.toList());
        if (aliasList == null || aliasList.size() <= 0) {
            return ITResult.succeed("没有设备");
        }

        // 换成ot的设备id
        String otHeaders = "{\"tenantId\":" + tenantId + "}";
        String body = JSONObject.toJSONString(new HashMap<String, Object>() {{
            put("deviceNames", aliasList);
        }});
        Result<List<String>> deviceIdsResult = otController.deviceNamesToIds(otHeaders, body);
        if (deviceIdsResult == null) {
            return ITResult.fail("设备别名没有对应数据");
        }
        if (deviceIdsResult.getSignal() == null) {
            return ITResult.fail("设备别名没有对应数据：" + deviceIdsResult.getMessage());
        }
        if (!deviceIdsResult.getSignal()) {
            return ITResult.fail(deviceIdsResult.getMessage());
        }
        List<String> deviceIds = deviceIdsResult.getResult();
        String deviceIdsStr = String.join(",", deviceIds);

        DeviceEntity firstDevice = deviceMapper.getFirstDevice(tenantId);
        if (firstDevice == null) {
            return ITResult.succeed("没有设备");
        }
        Long deviceId = firstDevice.getId();

        return doquery("queryDevicesTaskTimeByWindow", tenantId, deviceId, begin, end, every, deviceIdsStr);
    }


    public ITResult<Object> queryFaultTimeSumOfAllDevice(
            Long tenantId, String begin, String end
    ) {
        Result<Pair<LocalDateTime, LocalDateTime>> checkResult = checkBeginEnd(begin, end);
        if (!checkResult.getSignal()) {
            return ITResult.fail(checkResult.getMessage());
        }
        DeviceEntity firstDevice = deviceMapper.getFirstDevice(tenantId);
        if (firstDevice == null) {
            return ITResult.succeed("没有设备");
        }
        Long deviceId = firstDevice.getId();
        Result<List<String>> deviceIdsResult = allDeviceIds(tenantId);
        if (!deviceIdsResult.getSignal()) {
            return ITResult.fail(deviceIdsResult.getMessage());
        }
        List<String> deviceIds = deviceIdsResult.getResult();
        String deviceIdsStr = String.join(",", deviceIds);
        return doquery("queryDevicesFaultTimeSum", tenantId, deviceId, begin, end, null, deviceIdsStr);
    }

    public ITResult<Object> queryTaskTimeSumOfAllDevice(
            Long tenantId, String begin, String end
    ) {
        Result<Pair<LocalDateTime, LocalDateTime>> checkResult = checkBeginEnd(begin, end);
        if (!checkResult.getSignal()) {
            return ITResult.fail(checkResult.getMessage());
        }
        DeviceEntity firstDevice = deviceMapper.getFirstDevice(tenantId);
        if (firstDevice == null) {
            return ITResult.succeed("没有设备");
        }
        Long deviceId = firstDevice.getId();
        Result<List<String>> deviceIdsResult = allDeviceIds(tenantId);
        if (!deviceIdsResult.getSignal()) {
            return ITResult.fail(deviceIdsResult.getMessage());
        }
        List<String> deviceIds = deviceIdsResult.getResult();
        String deviceIdsStr = String.join(",", deviceIds);
        return doquery("queryDevicesTaskTimeSum", tenantId, deviceId, begin, end, null, deviceIdsStr);
    }

    public ITResult<Object> queryOnLineTimeSumOfAllDevice(
            Long tenantId, String begin, String end
    ) {
        Result<Pair<LocalDateTime, LocalDateTime>> checkResult = checkBeginEnd(begin, end);
        if (!checkResult.getSignal()) {
            return ITResult.fail(checkResult.getMessage());
        }
        DeviceEntity firstDevice = deviceMapper.getFirstDevice(tenantId);
        if (firstDevice == null) {
            return ITResult.succeed("没有设备");
        }
        Long deviceId = firstDevice.getId();
        Result<List<String>> deviceIdsResult = allDeviceIds(tenantId);
        if (!deviceIdsResult.getSignal()) {
            return ITResult.fail(deviceIdsResult.getMessage());
        }
        List<String> deviceIds = deviceIdsResult.getResult();
        String deviceIdsStr = String.join(",", deviceIds);
        return doquery("queryDevicesOnLineTimeSum", tenantId, deviceId, begin, end, null, deviceIdsStr);
    }

    public ITResult<Object> queryFaultCountOfAllDevice(
            Long tenantId, String begin, String end
    ) {
        Result<Pair<LocalDateTime, LocalDateTime>> checkResult = checkBeginEnd(begin, end);
        if (!checkResult.getSignal()) {
            return ITResult.fail(checkResult.getMessage());
        }
        DeviceEntity firstDevice = deviceMapper.getFirstDevice(tenantId);
        if (firstDevice == null) {
            return ITResult.succeed("没有设备");
        }
        Long deviceId = firstDevice.getId();
        Result<List<String>> deviceIdsResult = allDeviceIds(tenantId);
        if (!deviceIdsResult.getSignal()) {
            return ITResult.fail(deviceIdsResult.getMessage());
        }
        List<String> deviceIds = deviceIdsResult.getResult();
        String deviceIdsStr = String.join(",", deviceIds);
        return doquery("queryDevicesFaultCount", tenantId, deviceId, begin, end, null, deviceIdsStr);
    }

    public ITResult<Object> queryFaultCountByWindowOfAllDevice(
            Long tenantId, String begin, String end, String every
    ) {
        Result<Pair<LocalDateTime, LocalDateTime>> checkResult = checkBeginEnd(begin, end);
        if (!checkResult.getSignal()) {
            return ITResult.fail(checkResult.getMessage());
        }
        if (every == null || "".equals(every)) {
            Pair<LocalDateTime, LocalDateTime> pair = checkResult.getResult();
            LocalDateTime beginTime = pair.getLeft();
            LocalDateTime endTime = pair.getRight();
            every = parseEvery(beginTime, endTime);
        }
        DeviceEntity firstDevice = deviceMapper.getFirstDevice(tenantId);
        if (firstDevice == null) {
            return ITResult.succeed("没有设备");
        }
        Long deviceId = firstDevice.getId();
        Result<List<String>> deviceIdsResult = allDeviceIds(tenantId);
        if (!deviceIdsResult.getSignal()) {
            return ITResult.fail(deviceIdsResult.getMessage());
        }
        List<String> deviceIds = deviceIdsResult.getResult();
        String deviceIdsStr = String.join(",", deviceIds);
        return doquery("queryDevicesFaultCountByWindow", tenantId, deviceId, begin, end, every, deviceIdsStr);
    }

    public ITResult<Object> queryFaultDevicesNumberOfAllDevice(
            Long tenantId, String begin, String end
    ) {
        Result<Pair<LocalDateTime, LocalDateTime>> checkResult = checkBeginEnd(begin, end);
        if (!checkResult.getSignal()) {
            return ITResult.fail(checkResult.getMessage());
        }
        DeviceEntity firstDevice = deviceMapper.getFirstDevice(tenantId);
        if (firstDevice == null) {
            return ITResult.succeed("没有设备");
        }
        Long deviceId = firstDevice.getId();
        Result<List<String>> deviceIdsResult = allDeviceIds(tenantId);
        if (!deviceIdsResult.getSignal()) {
            return ITResult.fail(deviceIdsResult.getMessage());
        }
        List<String> deviceIds = deviceIdsResult.getResult();
        String deviceIdsStr = String.join(",", deviceIds);
        return doquery("queryFaultDevicesNumber", tenantId, deviceId, begin, end, null, deviceIdsStr);
    }

    public ITResult<Object> queryDevicesFaultTimeSumTopnOfAllDevice(
            TenantIsolation tenant, String begin, String end, Integer n
    ) {

        Result<Pair<LocalDateTime, LocalDateTime>> checkResult = checkBeginEnd(begin, end);
        if (!checkResult.getSignal()) {
            return ITResult.fail(checkResult.getMessage());
        }

        if(isNtiCustomer(tenant)) {
            Result<TenantInfoEntity> tenantInfoEntityResult = tenantInfoService.getById(tenant.getTenantId(), tenant.getTenantId());
            if (!tenantInfoEntityResult.getSignal()|| tenantInfoEntityResult.getResult()==null) {
                log.warn("通过tenantId:{},找不到TenantInfo",tenant.getTenantId());
                return ITResult.fail(tenantInfoEntityResult.getMessage());
            }
            DeviceEntity firstDevice = deviceMapper.getFirstDevice(tenantInfoEntityResult.getResult().getPmoTenantId());
            if (firstDevice == null) {
                return ITResult.succeed("没有设备");
            }
            Long deviceId = firstDevice.getId();
            Long customerPmoTenantIds=tenantInfoEntityResult.getResult().getPmoTenantId();
            Result<List<String>> deviceIdsResult = allDeviceIdsByCustomerId(defaultTenantId,tenant.getTenantId(),customerPmoTenantIds);
            if (!deviceIdsResult.getSignal()&& deviceIdsResult.getResult()!=null) {
                return ITResult.fail(deviceIdsResult.getMessage());
            }
            List<String> deviceIds = deviceIdsResult.getResult();
            String deviceIdsStr = String.join(",", deviceIds);
            log.info(">>>>>>> tenant.getTenantId():{},defaultTenantId:{}, deviceId:{},deviceIdsStr:{},isPMOTenant:{}",tenant.getTenantId(),defaultTenantId, deviceId, deviceIdsStr,PmoTenantEnum.IS_PMO_TENANT.getValue().equals(tenantInfoService.getTenantIsPmoTenant(tenant)));
            return doquery("queryDevicesFaultTimeSumTopn", defaultTenantId, deviceId, begin, end, null, deviceIdsStr, n);
        }else{
            DeviceEntity firstDevice = deviceMapper.getFirstDeviceNoPmo(tenant.getTenantId());
            if (firstDevice == null) {
                return ITResult.succeed("没有设备");
            }
            Long deviceId = firstDevice.getId();
            Result<List<String>> deviceIdsResult = allDeviceIds(tenant.getTenantId());
            if (!deviceIdsResult.getSignal()) {
                return ITResult.fail(deviceIdsResult.getMessage());
            }
            List<String> deviceIds = deviceIdsResult.getResult();
            if(CollectionUtils.isEmpty(deviceIds)) {
                return ITResult.succeed("没有设备");
            }
            String deviceIdsStr = String.join(",", deviceIds);
            return doquery("queryDevicesFaultTimeSumTopn", tenant.getTenantId(), deviceId, begin, end, null, deviceIdsStr, n);
        }
    }

    public ITResult<Object> queryDevicesFaultCountTopnOfAllDevice(
            TenantIsolation tenant, String begin, String end, Integer n
    ) {
        Result<Pair<LocalDateTime, LocalDateTime>> checkResult = checkBeginEnd(begin, end);
        if (!checkResult.getSignal()) {
            return ITResult.fail(checkResult.getMessage());
        }
        if(isNtiCustomer(tenant)) {
            Result<TenantInfoEntity> tenantInfoEntityResult = tenantInfoService.getById(tenant.getTenantId(), tenant.getTenantId());
            if (!tenantInfoEntityResult.getSignal()|| tenantInfoEntityResult.getResult()==null) {
                log.warn("通过tenantId:{},找不到TenantInfo",tenant.getTenantId());
                return ITResult.fail(tenantInfoEntityResult.getMessage());
            }
            DeviceEntity firstDevice = deviceMapper.getFirstDevice(tenantInfoEntityResult.getResult().getPmoTenantId());
            if (firstDevice == null) {
                return ITResult.succeed("没有设备");
            }
            Long deviceId = firstDevice.getId();
            Result<List<String>> deviceIdsResult = allDeviceIdsByCustomerId(defaultTenantId,tenant.getTenantId(),tenantInfoEntityResult.getResult().getPmoTenantId());
            if (!deviceIdsResult.getSignal()&& deviceIdsResult.getResult()!=null) {
                return ITResult.fail(deviceIdsResult.getMessage());
            }
            List<String> deviceIds = deviceIdsResult.getResult();
            String deviceIdsStr = String.join(",", deviceIds);
            log.info(">>>>>>> tenant.getTenantId():{},defaultTenantId:{}, deviceId:{},deviceIdsStr:{},isPMOTenant:{}",tenant.getTenantId(),defaultTenantId, deviceId, deviceIdsStr,PmoTenantEnum.IS_PMO_TENANT.getValue().equals(tenantInfoService.getTenantIsPmoTenant(tenant)));
            return doquery("queryDevicesFaultCountTopn", defaultTenantId, deviceId, begin, end, null, deviceIdsStr, n);
        }else{
            DeviceEntity firstDevice = deviceMapper.getFirstDeviceNoPmo(tenant.getTenantId());
            if (firstDevice == null) {
                return ITResult.succeed("没有设备");
            }
            Long deviceId = firstDevice.getId();
            Result<List<String>> deviceIdsResult = allDeviceIds(tenant.getTenantId());
            if (!deviceIdsResult.getSignal()) {
                return ITResult.fail(deviceIdsResult.getMessage());
            }
            List<String> deviceIds = deviceIdsResult.getResult();
            if(CollectionUtils.isEmpty(deviceIds)) {
                return ITResult.succeed("没有设备");
            }
            String deviceIdsStr = String.join(",", deviceIds);
            return doquery("queryDevicesFaultCountTopn", tenant.getTenantId(), deviceId, begin, end, null, deviceIdsStr, n);
        }
    }

    public Result<DeviceBlockChartVo> queryDeviceDistribution(TenantIsolation tenantIsolation) {
        DeviceBlockChartVo deviceBlockChartVo = new DeviceBlockChartVo();
        List<String> categories = new ArrayList<>();
        Result<List<DeviceLocationTreeDto>> tree = deviceLocationService.getTree(tenantIsolation, new DeviceLocationDto());
        if (!tree.getSignal()) {
            throw new BizException("获取存放空间失败！");
        }
        List<DeviceLocationTreeDto> deviceLocationTreeDtoList = tree.getResult();
        Map<Long, String> locationMap = new HashMap<>();
        // 获取二级空间
        List<DeviceLocationTreeDto> secondLocationList = deviceLocationTreeDtoList.stream().map(i -> CollectionUtil.isNotEmpty(i.getChildren()) ? i.getChildren() : new ArrayList<DeviceLocationTreeDto>()).flatMap(Collection::stream).collect(Collectors.toList());
        secondLocationList.forEach(item -> {
            List<Long> idList = new ArrayList<>();
            List<Long> locationChildrenId = getLocationChildrenId(idList, item);
            for (Long childId : locationChildrenId) {
                locationMap.put(childId, item.getLocation());
            }
        });

        Result<List<DeviceTypeGroupTreeDto>> typeTreeRes = deviceTypeGroupService.getTypeGroupTree(null, tenantIsolation);
        if (!typeTreeRes.getSignal()) {
            throw new BizException("获取设备类型失败！");
        }
        List<DeviceTypeGroupTreeDto> deviceTypeTreeDtoList =  typeTreeRes.getResult();
        Map<Long, String> typeMap = new HashMap<>();
        // 获取二级设备类型
        List<DeviceTypeGroupTreeDto> secondTypeList = deviceTypeTreeDtoList.stream().map(i -> CollectionUtil.isNotEmpty(i.getChildrenGroup()) ? i.getChildrenGroup() : new ArrayList<DeviceTypeGroupTreeDto>()).flatMap(Collection::stream).collect(Collectors.toList());
        secondTypeList.forEach(item -> {
            categories.add(item.getGroupName());
            List<Long> typeChildrenId = getTypeChildrenId(new ArrayList<>(), item);
            for (Long childId : typeChildrenId) {
                typeMap.put(childId, item.getGroupName());
            }
        });
        List<DeviceEntity> deviceEntities = deviceMapper.listAllDevice(tenantIsolation.getTenantId());
        List<DeviceDto> deviceDtoList = BeanUtil.copyToList(deviceEntities, DeviceDto.class, CopyOptions.create().ignoreError().ignoreNullValue());
        deviceDtoList.forEach(item -> {
            if (StrUtil.isNotBlank(item.getDeviceLocationId())) {
                item.setLocation(locationMap.get(Long.parseLong(item.getDeviceLocationId())));
            }
            if (StrUtil.isNotBlank(item.getDeviceTypeId())) {
                item.setTypeName(typeMap.get(Long.parseLong(item.getDeviceTypeId())));
            }
        });

        List<DeviceBlockChartSeriesVo> seriesList = new ArrayList<>();
        Map<String, List<DeviceDto>> groupLocationMap = deviceDtoList.stream().filter(i -> StrUtil.isNotBlank(i.getTypeName()) && StrUtil.isNotBlank(i.getLocation())).collect(Collectors.groupingBy(i -> i.getLocation()));
        for (Map.Entry<String, List<DeviceDto>> groupLocationEntry : groupLocationMap.entrySet()) {
            DeviceBlockChartSeriesVo series = new DeviceBlockChartSeriesVo();
            series.setName(groupLocationEntry.getKey());
            List<DeviceDto> value = groupLocationEntry.getValue();
            Map<String, List<DeviceDto>> groupTypeMap = value.stream().collect(Collectors.groupingBy(i -> i.getTypeName()));
            List<Integer> countList = new ArrayList<>();
            for (String category : categories) {
                countList.add(Optional.ofNullable(groupTypeMap.get(category)).map(List::size).orElse(0));
            }
            series.setData(countList);
            seriesList.add(series);
        }
        deviceBlockChartVo.setCategories(categories);
        deviceBlockChartVo.setSeries(seriesList);
        return Result.ok(deviceBlockChartVo);
    }

    private List<Long> getTypeChildrenId(List<Long> idList, DeviceTypeGroupTreeDto treeDto) {
        if (CollUtil.isNotEmpty(treeDto.getChildrenType())) {
            idList.addAll(treeDto.getChildrenType().stream().map(i -> i.getId()).collect(Collectors.toList()));
        }
        if (CollectionUtil.isNotEmpty(treeDto.getChildrenGroup())) {
            for (DeviceTypeGroupTreeDto child : treeDto.getChildrenGroup()) {
                getTypeChildrenId(idList, child);
            }
        }
        return idList;
    }

    private List<Long> getLocationChildrenId(List<Long> idList, DeviceLocationTreeDto treeDto) {
        idList.add(treeDto.getId());
        if (CollectionUtil.isNotEmpty(treeDto.getChildren())) {
            for (DeviceLocationTreeDto child : treeDto.getChildren()) {
                getLocationChildrenId(idList, child);
            }
        }
        return idList;
    }

    public Result<Map<Long, String>> otDeviceIdDcmNameMap(TenantIsolation tenant) {
        boolean isNtiCustomer = isNtiCustomer(tenant);
        List<DeviceEntity> allDeviceEntityList = Lists.newArrayList();
        if(isNtiCustomer){
            Result<TenantInfoEntity> tenantInfoEntityResult = tenantInfoService.getById(tenant.getTenantId(), tenant.getTenantId());
            if (!tenantInfoEntityResult.getSignal()|| tenantInfoEntityResult.getResult()==null) {
                log.warn("通过tenantId:{},找不到TenantInfo",tenant.getTenantId());
                return Result.error(tenantInfoEntityResult.getMessage());
            }
            allDeviceEntityList = deviceMapper.listAllDevice(tenantInfoEntityResult.getResult().getPmoTenantId());
        }else{
            allDeviceEntityList= deviceMapper.listAllDevice(tenant.getTenantId());
        }
        if (allDeviceEntityList == null || allDeviceEntityList.size() <= 0) {
            return Result.ok(new HashMap<>());
        }
        List<String> aliasList = allDeviceEntityList.stream()
                .filter(t -> {
                    return ConnectOtEnum.CONNECT.getValue().equals(t.getConnectOt());
                })
                .map(DeviceEntity::getDeviceAliasName)
                .filter(t -> {
                    return t != null && !"".equals(t);
                })
                .collect(Collectors.toList());

        if (aliasList == null || aliasList.size() <= 0) {
            return Result.ok(new HashMap<>());
        }

        String otHeaders = "{\"tenantId\":" + tenant.getTenantId() + "}";
        if(isNtiCustomer){
            otHeaders="{\"tenantId\":" + defaultTenantId + "}";
        }
        String body = JSONObject.toJSONString(new HashMap<String, Object>() {{
            put("deviceNames", aliasList);
        }});
        Result<Map<Long, String>> result = otController.deviceIdNameMapByNames(otHeaders, body);
        if (result == null) {
            return Result.error("查询设备名失败");
        }
        if (result.getSignal() == null) {
            return Result.error("查询设备名失败:" + result.getMessage());
        }
        if (!result.getSignal()) {
            return Result.error(result.getMessage());
        }
        // otDeviceId -> alias
        Map<Long, String> m = result.getResult();
        Map<Long, String> r = new HashMap<>();
        if (m != null) {
            // alias -> dcmDeviceName
            Map<String, String> aliasNameMap = new HashMap<>();
            allDeviceEntityList.stream().forEach(t -> {
                if (t.getDeviceAliasName() != null) {
                    aliasNameMap.put(t.getDeviceAliasName(), t.getDeviceName());
                }
            });
            m.entrySet().forEach(entry -> {
                String dcmDeviceName = aliasNameMap.get(entry.getValue());
                r.put(entry.getKey(), dcmDeviceName);
            });
        }
        return Result.ok(r);
    }

    @Autowired
    private  PmoDeviceMapper pmoDeviceMapper;

    public Result<Integer> querySupplierDeviceCount(TenantIsolation tenantIsolation) {
        Integer count = deviceEmpowerMapper.countSupplierDeviceTotal(tenantIsolation.getTenantId());
        if (defaultTenantId.equals(tenantIsolation.getTenantId()) && IdTypeEnum.SUPPLIER.getValue().equals(tenantIsolation.getIdType())){
            Integer allDeviceCount = pmoDeviceMapper.getAllDeviceCount();
            count+=allDeviceCount;
        }
        return Result.ok(count);
    }

    /**
     * 判断是否为今天国际的客户，并且是业主身份
     * @param tenant
     * @return
     */
    public boolean isNtiCustomer(TenantIsolation tenant){
        return !defaultTenantId.equals(tenant.getTenantId()) && PmoTenantEnum.IS_PMO_TENANT.getValue().equals(tenantInfoService.getTenantIsPmoTenant(tenant));
    }

}
