package com.nti56.dcm.server.schedule;

import com.nti56.dcm.server.service.IFaultLibraryService;
import com.nti56.dcm.server.service.StockAlarmService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

/**
 * 报表定时任务
 */
@Slf4j
@Component
public class OtEventSchedule {

    @Autowired
    private IFaultLibraryService faultLibraryService;

    /**
     * 每天凌晨2点执行
     */
    @Scheduled(cron = "0 0 2 * * ?")
    public void dailyReportTask() {
        log.info("开始同步OT故障事件数据任务");

        faultLibraryService.syncOTEvent();

        log.info("结束同步OT故障事件数据任务");
    }

}
