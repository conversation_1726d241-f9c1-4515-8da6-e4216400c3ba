package com.nti56.dcm.server.domain.enums;

import lombok.Getter;

@Getter
public enum DeviceMaintenanceOperationTypeEnum {
    REPAIR(2, "维修", ""),
    MAINTENANCE(3, "保养", ""),
    INSPECT(1, "巡检", ""),
    ;

    private Integer value;

    private String name;

    private String nameDesc;

    DeviceMaintenanceOperationTypeEnum(Integer value, String name, String nameDesc) {
        this.value = value;
        this.name = name;
        this.nameDesc = nameDesc;
    }

    public static DeviceMaintenanceOperationTypeEnum typeOfValue(Integer value){
        DeviceMaintenanceOperationTypeEnum[] values = DeviceMaintenanceOperationTypeEnum.values();
        for (DeviceMaintenanceOperationTypeEnum v : values) {
            if (v.value.equals(value)) {
                return v;
            }
        }
        return null;
    }

    public static DeviceMaintenanceOperationTypeEnum typeOfName(String name){
        DeviceMaintenanceOperationTypeEnum[] values = DeviceMaintenanceOperationTypeEnum.values();
        for (DeviceMaintenanceOperationTypeEnum v : values) {
            if (v.name.equals(name)) {
                return v;
            }
        }
        return null;
    }

    public static DeviceMaintenanceOperationTypeEnum typeOfNameDesc(String nameDesc){
        DeviceMaintenanceOperationTypeEnum[] values = DeviceMaintenanceOperationTypeEnum.values();
        for (DeviceMaintenanceOperationTypeEnum v : values) {
            if (v.nameDesc.equals(nameDesc)) {
                return v;
            }
        }
        return null;
    }
}
