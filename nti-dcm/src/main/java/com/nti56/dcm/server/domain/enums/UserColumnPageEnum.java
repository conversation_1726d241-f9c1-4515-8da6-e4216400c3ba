package com.nti56.dcm.server.domain.enums;

import lombok.Getter;

public enum UserColumnPageEnum {
    DEVICE(1, "设备台账", "设备台账"),
    REPAIR(2, "维修工单", "维修工单"),
    REPAIR_WITH_ME(3, "我经手的维修工单", "我经手的维修工单"),
    REPAIR_OUTSOURCE(4, "委外维修工单", "委外维修工单"),
    REPAIR_OUTSOURCE_WITH_ME(5, "我经手的委外维修工单", "我经手的委外维修工单"),
    MAINTENANCE_STANDARD(6, "保养标准", "保养标准"),
    MAINTENANCE_PLAN(7, "保养计划", "保养计划"),
    MAINTENANCE(8, "保养工单", "保养工单"),
    MAINTENANCE_WITH_ME(9, "我经手的保养工单", "我经手的保养工单"),
    MAINTENANCE_OUTSOURCE(10, "委外保养工单", "委外保养工单"),
    MAINTENANCE_OUTSOURCE_WITH_ME(11, "我经手的委外保养工单", "我经手的委外保养工单"),
    INSPECT_STANDARD(12, "巡检标准", "巡检标准"),
    INSPECT_PLAN(13, "巡检计划", "巡检计划"),
    INSPECT(14, "巡检工单", "巡检工单"),
    INSPECT_WITH_ME(15, "我经手的巡检工单", "我经手的巡检工单"),
    INSPECT_OUTSOURCE(16, "委外巡检工单", "委外巡检工单"),
    INSPECT_OUTSOURCE_WITH_ME(17, "我经手的委外巡检工单", "我经手的委外巡检工单"),
    REPAIR_EXP(18, "维修经验库", "维修经验库"),
    DEVICE_FILE(19, "设备资料", "设备资料"),
    CUSTOMER(20, "客户列表", "客户列表"),
    SUPPLIER(21, "供应商列表", "供应商列表"),
    ;

    @Getter
    private Integer value;

    @Getter
    private String name;

    @Getter
    private String nameDesc;

    UserColumnPageEnum(Integer value, String name, String nameDesc) {
        this.value = value;
        this.name = name;
        this.nameDesc = nameDesc;
    }

    public static UserColumnPageEnum typeOfValue(Integer value){
        UserColumnPageEnum[] values = UserColumnPageEnum.values();
        for (UserColumnPageEnum v : values) {
            if (v.value.equals(value)) {
                return v;
            }
        }
        return null;
    }

    public static UserColumnPageEnum typeOfName(String name){
        UserColumnPageEnum[] values = UserColumnPageEnum.values();
        for (UserColumnPageEnum v : values) {
            if (v.name.equals(name)) {
                return v;
            }
        }
        return null;
    }

    public static UserColumnPageEnum typeOfNameDesc(String nameDesc){
        UserColumnPageEnum[] values = UserColumnPageEnum.values();
        for (UserColumnPageEnum v : values) {
            if (v.nameDesc.equals(nameDesc)) {
                return v;
            }
        }
        return null;
    }
}
