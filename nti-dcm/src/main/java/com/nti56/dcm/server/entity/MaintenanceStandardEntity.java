package com.nti56.dcm.server.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 保养标准 实体类
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @Ddate 2024-02-28 15:37:22
 * @since JDK 1.8
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@TableName("maintenance_standard")
public class MaintenanceStandardEntity implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
    * 保养标准ID
    */
    private Long id;

    /**
    * 保养标准编号
    */
    private String standardNumber;

    /**
    * 保养标准名称
    */
    private String standardName;

    /**
    * 要求描述
    */
    private String requireDesc;

    /**
    * 创建人
    */
    @TableField(fill = FieldFill.INSERT)
    private String creator;

    /**
    * 创建时间
    */
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
    * 修改人
    */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private String updator;

    /**
    * 修改时间
    */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    /**
    * 租户ID
    */
    @TableField(fill = FieldFill.INSERT)
    private Long tenantId;

    /**
    * 工程ID
    */
    @TableField(fill = FieldFill.INSERT)
    private Long engineeringId;

    /**
    * 模块ID
    */
    @TableField(fill = FieldFill.INSERT)
    private Long moduleId;

    /**
    * 空间ID
    */
    @TableField(fill = FieldFill.INSERT)
    private Long spaceId;

    /**
    * 版本号
    */
    @Version
    private Integer version;

    /**
    * 删除
    */
    @TableLogic
    private Integer deleted;

    /**
     * 身份，1-供应商，2-客户
     */
    private Integer idType;

    /**
     * 类型，1-通用，2-专用设备
     */
    private Integer type;

    /**
     * 设备类型ID
     */
    private Long deviceTypeId;



}
