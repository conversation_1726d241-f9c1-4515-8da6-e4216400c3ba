package com.nti56.dcm.server.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.nti56.dcm.server.model.dto.*;
import com.nti56.nlink.common.dto.TenantIsolation;
import com.nti56.nlink.common.util.PageParam;
import com.nti56.common.util.JwtUserInfoUtils;
import com.nti56.common.util.R;
import com.nti56.nlink.common.util.Result;
import com.nti56.dcm.server.service.InspectOrderService;
import com.nti56.dcm.server.service.UserCenterService;
import com.nti56.dcm.server.entity.InspectOrderEntity;
import com.nti56.dcm.server.model.vo.DeviceInspectResultVo;
import com.nti56.dcm.server.model.vo.InspectOrderCountVo;
import com.nti56.dcm.server.model.vo.InspectOrderPage;
import com.nti56.dcm.server.model.vo.InspectOrderSummaryVo;
import com.nti56.dcm.server.model.vo.InspectOrderVo;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Map;
import lombok.extern.slf4j.Slf4j;


/**
 * 点巡检工单表 控制器
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2024-02-26 16:54:02
 * @since JDK 1.8
 */
@RestController
@RequestMapping("/inspectOrder")
@Slf4j
public class InspectOrderController {

    @Autowired
    private InspectOrderService service;

    @Autowired
    private UserCenterService userCenterService;

    /**
     * 统计点巡检工单概况
     */
    @GetMapping("/summary")
    public R<InspectOrderSummaryVo> summary(@RequestHeader("dcm_headers") TenantIsolation tenant){
        Result<InspectOrderSummaryVo> result = service.summary(tenant.getTenantId(), tenant.getIdType());
        return R.result(result);
    }

    /**
     * 查询全部点巡检工单分页
     * @param pageParam 分页参数
     */
    @GetMapping("/page")
    public R<InspectOrderPage> page(
        @RequestHeader("dcm_headers") TenantIsolation tenant, 
        PageParam pageParam, 
        InspectOrderParams params
    ){
        Page<InspectOrderEntity> page = pageParam.toPage(InspectOrderEntity.class);
        Result<InspectOrderPage> result = service.pageByParams(tenant.getTenantId(), tenant.getIdType(), params, page);
        return R.result(result);
    }

    /**
     * 查询我的已处理点巡检工单分页
     * @param pageParam 分页参数
     */
    @GetMapping("/pageMyAlreadyProcessOrder")
    public R<Page<InspectOrderDto>> pageMyAlreadyProcessOrder(
        @RequestHeader("dcm_headers") TenantIsolation tenant, 
        PageParam pageParam, 
        InspectOrderParams params
    ){
        Page<InspectOrderEntity> page = pageParam.toPage(InspectOrderEntity.class);
        Result<Page<InspectOrderDto>> result = service.pageMyAlreadyProcessOrder(tenant.getTenantId(), tenant.getIdType(), params, page);
        return R.result(result);
    }

    /**
     * 查询我的待处理点巡检工单分页
     * @param pageParam 分页参数
     */
    @GetMapping("/pageMyWaitProcessOrder")
    public R<Page<InspectOrderDto>> pageMyWaitProcessOrder(
        @RequestHeader("dcm_headers") TenantIsolation tenant, 
        PageParam pageParam, 
        InspectOrderParams params
    ){
        Page<InspectOrderEntity> page = pageParam.toPage(InspectOrderEntity.class);
        Result<Page<InspectOrderDto>> result = service.pageMyWaitProcessOrder(tenant.getTenantId(), tenant.getIdType(), params, page);
        return R.result(result);
    }
    
    /**
     * 手动创建点巡检工单
     */
    @PostMapping("/create")
    public R<InspectOrderCreateDto> create(@RequestHeader("dcm_headers") TenantIsolation tenant, @RequestBody InspectOrderCreateDto dto){

        Result<String> tenantNameResult = userCenterService.getTenantNameById(tenant.getTenantId());
        if(!tenantNameResult.getSignal()){
            return R.error(tenantNameResult.getMessage());
        }
        String tenantName = tenantNameResult.getResult();

        Long currentUserId = JwtUserInfoUtils.getUserId();
        String currentUserName = JwtUserInfoUtils.getUserName();

        Result<InspectOrderCreateDto> result = service.createInspectOrder(
            tenant.getTenantId(), tenantName, tenant.getIdType(), dto,
            currentUserId, currentUserName
        );
        return R.result(result);
    }
    
    /**
     * 快捷创建点巡检工单
     * @param copyOrderId 快捷复制的工单id
     */
    @PostMapping("/quickCreate")
    public R<InspectOrderCreateDto> quickCreate(@RequestHeader("dcm_headers") TenantIsolation tenant, @RequestBody InspectOrderQuickCreateDto dto){
        Result<String> tenantNameResult = userCenterService.getTenantNameById(tenant.getTenantId());
        if(!tenantNameResult.getSignal()){
            return R.error(tenantNameResult.getMessage());
        }
        String tenantName = tenantNameResult.getResult();

        Result<InspectOrderCreateDto> result = service.quickCreate(tenant.getTenantId(), tenantName, tenant.getIdType(), dto);
        return R.result(result);
    }

    /**
     * 点巡检工单撤销
     */
    @PostMapping("revokeOrder")
    public R<Void> revokeOrder(@RequestHeader("dcm_headers") TenantIsolation tenant, @RequestBody InspectOrderRevokeDto dto){
        Result<Void> result = service.revokeOrder(tenant.getTenantId(), tenant.getIdType(), dto);
        return R.result(result);
    }
    
    /**
     * 点巡检工单撤回派工
     */
    @PostMapping("undoDispatchOrder")
    public R<Void> undoDispatchOrder(@RequestHeader("dcm_headers") TenantIsolation tenant, @RequestBody InspectOrderUndoDto dto){
        Result<Void> result = service.undoDispatchOrder(tenant.getTenantId(), tenant.getIdType(), dto);
        return R.result(result);
    }
    
    
    /**
     * 点巡检工单撤回执行
     */
    @PostMapping("undoExecuteOrder")
    public R<Void> undoExecuteOrder(@RequestHeader("dcm_headers") TenantIsolation tenant, @RequestBody InspectOrderUndoDto dto){
        Result<Void> result = service.undoExecuteOrder(tenant.getTenantId(), tenant.getIdType(), dto);
        return R.result(result);
    }
    

    /**
     * 点巡检工单派工
     */
    @PostMapping("/dispatchOrder")
    public R<Void> dispatchOrder(@RequestHeader("dcm_headers") TenantIsolation tenant, @RequestBody InspectOrderDispatchDto dto){
        Result<Void> result = service.dispatchOrder(tenant.getTenantId(), tenant.getIdType(), dto);
        return R.result(result);
    }

    /**
     * 点巡检工单接收
     */
    @PostMapping("/receiveOrder")
    public R<Void> receiveOrder(@RequestHeader("dcm_headers") TenantIsolation tenant, @RequestBody OrderReceiveDto dto){
        Result<Void> result = service.receiveOrder(tenant.getTenantId(), tenant.getIdType(), dto);
        return R.result(result);
    }

    /**
     * 点巡检工单转交
     */
    @PostMapping("/forwardOrder")
    public R<Void> forwardOrder(@RequestHeader("dcm_headers") TenantIsolation tenant, @RequestBody InspectOrderForwardDto dto){
        Result<Void> result = service.forwardOrder(tenant.getTenantId(), tenant.getIdType(), dto);
        return R.result(result);
    }
    
//    /**
//     * 点巡检工单接收
//     */
//    // @PostMapping("/receiveOrder")
//    public R<Void> receiveOrder(@RequestHeader("dcm_headers") TenantIsolation tenant, @RequestBody InspectOrderReceiveDto dto){
//        Result<Void> result = service.receiveOrder(tenant.getTenantId(), tenant.getIdType(), dto);
//        return R.result(result);
//    }

    /**
     * 查询设备点巡检检查项及检查结果
     * @param inspectOrderId 工单id
     * @param deviceId 设备id
     */
    @GetMapping("/getDeviceInspectResult")
    public R<DeviceInspectResultVo> getDeviceInspectResult(
        @RequestHeader("dcm_headers") TenantIsolation tenant, 
        @RequestParam(name = "inspectOrderId")Long inspectOrderId, 
        @RequestParam(name = "deviceId")Long deviceId
    ){
        Result<DeviceInspectResultVo> result = service.getDeviceInspectResult(
            tenant.getTenantId(), tenant.getIdType(), inspectOrderId, deviceId
        );
        return R.result(result);
    }

    /**
     * 统计设备点巡检工单记录数
     * @param deviceId 设备id
     */
    @GetMapping("/countDeviceInspectOrder")
    public R<DeviceInspectCountDto> countDeviceInspectOrder(
        @RequestHeader("dcm_headers") TenantIsolation tenant, 
        @RequestParam(name = "deviceId")Long deviceId
    ){
        Result<DeviceInspectCountDto> result = service.countDeviceInspectOrder(
            tenant.getTenantId(), tenant.getIdType(), deviceId
        );
        return R.result(result);
    }
    
    /**
     * 上传巡检结果设备文件、图片
     */
    @PostMapping("/submitResultFile")
    public R<Void> submitResultFile(@RequestHeader("dcm_headers") TenantIsolation tenant, @RequestBody InspectResultFileDto dto){
        Result<Void> result = service.submitResultFile(tenant.getTenantId(), tenant.getIdType(), dto);
        return R.result(result);
    }
    
    
    /**
     * 上传巡检工单文件、图片
     */
    @PostMapping("/submitOrderFile")
    public R<Void> submitOrderFile(@RequestHeader("dcm_headers") TenantIsolation tenant, @RequestBody InspectOrderFileDto dto){
        Result<Void> result = service.submitOrderFile(tenant.getTenantId(), tenant.getIdType(), dto);
        return R.result(result);
    }
    
    /**
     * 点巡检工单提交设备检查结果
     * 每个设备提交一次
     */
    @PostMapping("/submitDeviceResult")
    public R<Void> submitDeviceResult(@RequestHeader("dcm_headers") TenantIsolation tenant, @RequestBody InspectResultDto dto){
        Result<Void> result = service.submitDeviceResult(tenant.getTenantId(), tenant.getIdType(), dto);
        return R.result(result);
    }
    
    /**
     * 点巡检工单执行
     */
    @PostMapping("/executeOrder")
    public R<Void> executeOrder(@RequestHeader("dcm_headers") TenantIsolation tenant, @RequestBody InspectOrderExecuteDto dto){
        Result<Void> result = service.executeOrder(tenant.getTenantId(), tenant.getIdType(), dto);
        return R.result(result);
    }
    
    
    /**
     * 点巡检工单驳回
     */
    @PostMapping("/withdrawOrder")
    public R<Void> withdrawOrder(@RequestHeader("dcm_headers") TenantIsolation tenant, @RequestBody InspectOrderWithdrawDto dto){
        Result<Void> result = service.withdrawOrder(tenant.getTenantId(), tenant.getIdType(), dto);
        return R.result(result);
    }
    
    /**
     * 点巡检工单执行暂存
     */
    @PostMapping("/tempExecuteOrder")
    public R<Void> tempExecuteOrder(@RequestHeader("dcm_headers") TenantIsolation tenant, @RequestBody InspectOrderExecuteDto dto){
        Result<Void> result = service.tempExecuteOrder(tenant.getTenantId(), tenant.getIdType(), dto);
        return R.result(result);
    }

    /**
     * 获取点巡检工单详情
     * @param entityId 对象id
     */
    @GetMapping("/{entityId}")
    public R<InspectOrderVo> getOrderDetailById(@RequestHeader("dcm_headers") TenantIsolation tenant, @PathVariable Long entityId){
        Result<InspectOrderVo> result = service.getOrderDetailById(tenant.getTenantId(), tenant.getIdType(), entityId);
        return R.result(result);
    }

    /**
     * 点巡检工单验收
     */
    @PostMapping("/acceptOrder")
    public R<Void> acceptOrder(@RequestHeader("dcm_headers") TenantIsolation tenant, @RequestBody InspectOrderAcceptDto dto){
        Result<Void> result = service.acceptOrder(tenant.getTenantId(), tenant.getIdType(), dto);
        return R.result(result);
    }

    /**
     * 按日期统计点巡检工单量
     * @param begin 查询开始日期
     * @param end 查询结束日期
     */
    @GetMapping("/countOrderByDate")
    public R<Map<String, Long>> countOrderByDate(
        @RequestHeader("dcm_headers") TenantIsolation tenant, 
        @RequestParam("begin") @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate begin, 
        @RequestParam("end") @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate end
    ){
        Result<Map<String, Long>> result = service.countOrderByDate(tenant.getTenantId(), tenant.getIdType(), begin, end);
        return R.result(result);
    }

    /**
     * 按状态分组统计点巡检工单量
     * @param begin 查询开始日期
     * @param end 查询结束日期
     */
    @GetMapping("/countOrderByStatus")
    public R<InspectOrderCountVo> countOrderByStatus(
        @RequestHeader("dcm_headers") TenantIsolation tenant, 
        @RequestParam("begin") @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") LocalDateTime begin, 
        @RequestParam("end") @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") LocalDateTime end
    ){
        Result<InspectOrderCountVo> result = service.countOrderByStatus(tenant.getTenantId(), tenant.getIdType(), begin, end);
        return R.result(result);
    }

    /**
     * 提交点巡检备件领用出库单
     */
    @PostMapping("/submit-inspect-outbound-order")
    public R<Void> submitInspectOutboundOrder(
        @RequestHeader("dcm_headers") TenantIsolation tenant, 
        @RequestBody InspectOutboundOrderDto dto
    ){
        Result<Void> result = service.submitInspectOutboundOrder(
            tenant.getTenantId(), tenant.getIdType(), dto
        );
        return R.result(result);
    }

}
