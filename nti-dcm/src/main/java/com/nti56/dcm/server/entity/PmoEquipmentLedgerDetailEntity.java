package com.nti56.dcm.server.entity;

import java.math.BigDecimal;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import java.time.LocalDate;
import com.baomidou.mybatisplus.annotation.TableId;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import java.io.Serializable;
import java.util.Date;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 设备台账明细
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-16
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("pmo_equipment_ledger_detail")
public class PmoEquipmentLedgerDetailEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 导入批次号
     */
    private String batchNo;

    private Long id;

    /**
     * 租户id
     */
    private String clientId;

    /**
     * 客户名称
     */
    private String customerName;

    /**
     * 客户id
     */
    private Long customerId;

    /**
     * 客户行业
     */
    private String industry;

    /**
     * 终端客户名称（合同-乙方名称）
     */
    private String secondParty;

    /**
     * 项目id
     */
    private Long projectId;

    /**
     * 项目名称
     */
    private String projectName;

    /**
     * 金蝶项目编码
     */
    private String easProjectCode;

    /**
     * 金蝶项目名称
     */
    private String easProjectName;

    /**
     * 合同id
     */
    private Long contractId;

    /**
     * 合同编码
     */
    private String contractCode;

    /**
     * 合同名称
     */
    private String contractName;

    /**
     * 设备区分（设备类型名称)
     */
    private String category;

    /**
     * 二级分类
     */
    private String subCategory;

    /**
     * 设备名称
     */
    private String deviceName;

    /**
     * 设备型号
     */
    private String deviceSpec;

    /**
     * 设备数量
     */
    private BigDecimal deviceNum;

    /**
     * 单位名称
     */
    private String unit;

    /**
     * 客户验收日期
     */
    private Date acceptanceDate;

    /**
     * 质保到期日期
     */
    private Date warrantyExpirationDate;

    /**
     * 供应商名称
     */
    private String supplierName;

    /**
     * 存放空间
     */
    private String storageSpace;

    /**
     * 删除状态; 0:未删除，1：已删除
     */
    private Integer deleted;

    /**
     * 创建人
     */
    private String createBy;

    /**
     * 创建人姓名
     */
    private String createByName;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 修改人
     */
    private String modifyBy;

    /**
     * 创建人姓名
     */
    private String modifyByName;

    /**
     * 更新时间
     */
    private LocalDateTime modifyTime;

    /**
     * 版本号
     */
    private Long version;


}
