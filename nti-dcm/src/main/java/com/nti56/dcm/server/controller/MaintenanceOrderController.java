package com.nti56.dcm.server.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.nti56.dcm.server.model.dto.*;
import com.nti56.dcm.server.model.vo.DeviceMaintenanceResultVo;
import com.nti56.dcm.server.model.vo.MaintenanceOrderPage;
import com.nti56.dcm.server.model.vo.MaintenanceOrderVo;
import com.nti56.dcm.server.service.MaintenanceOrderService;
import com.nti56.nlink.common.dto.TenantIsolation;
import com.nti56.nlink.common.util.*;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.apache.ibatis.annotations.Param;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDate;

/**
 * <p>
 * 保养工单表 控制器
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @Ddate 2024-03-01 16:28:19
 * @since JDK 1.8
 */
@RestController
@RequestMapping("maintenanceOrder")
@Tag(name = "保养工单表模块")
public class MaintenanceOrderController {

    @Autowired
    private MaintenanceOrderService service;

    @PostMapping("")
    @Operation(summary = "创建保养工单")
    public R create(@RequestHeader("dcm_headers") TenantIsolation tenantIsolation,
                    @Validated @RequestBody MaintenanceOrderDto dto){
        return R.result(service.save(tenantIsolation, dto));
    }

    @GetMapping("page")
    @Operation(summary = "全部保养工单分页")
    public R page(@RequestHeader("dcm_headers") TenantIsolation tenantIsolation, PageParam pageParam,
                  QueryMaintenanceOrderDto dto){
        Page<MaintenanceOrderVo> page = pageParam.toPage(MaintenanceOrderVo.class);
        Result<MaintenanceOrderPage> result = service.getPage(tenantIsolation, dto, page);
        return R.result(result);
    }

    @GetMapping("pageWaitProcessOrder")
    @Operation(summary = "待处理保养工单分页")
    public R pageWaitProcessOrder(@RequestHeader("dcm_headers") TenantIsolation tenantIsolation, PageParam pageParam,
                                  QueryMaintenanceOrderDto dto){
        Page<MaintenanceOrderVo> page = pageParam.toPage(MaintenanceOrderVo.class);
        Result<Page<MaintenanceOrderVo>> result = service.pageWaitProcessOrder(tenantIsolation, dto, page);
        return R.result(result);
    }

    @GetMapping("pageProcessOrder")
    @Operation(summary = "已处理保养工单分页")
    public R pageProcessOrder(@RequestHeader("dcm_headers") TenantIsolation tenantIsolation, PageParam pageParam,
                                  QueryMaintenanceOrderDto dto){
        Page<MaintenanceOrderVo> page = pageParam.toPage(MaintenanceOrderVo.class);
        Result<Page<MaintenanceOrderVo>> result = service.pageProcessOrder(tenantIsolation, dto, page);
        return R.result(result);
    }

    @GetMapping("pageSupplierReceiveOrder")
    @Operation(summary = "供应商身份接收的客户保养工单分页")
    public R pageSupplierReceiveOrder(@RequestHeader("dcm_headers") TenantIsolation tenantIsolation, PageParam pageParam,
                  QueryMaintenanceOrderDto dto){
        Page<MaintenanceOrderVo> page = pageParam.toPage(MaintenanceOrderVo.class);
        Result<Page<MaintenanceOrderVo>> result = service.pageSupplierReceiveOrder(tenantIsolation, dto, page);
        return R.result(result);
    }

    @PostMapping("/dispatchOrder")
    @Operation(summary = "保养工单派工")
    public R dispatchOrder(@RequestHeader("dcm_headers")TenantIsolation tenantIsolation,
                           @Validated @RequestBody MaintenanceOrderDispatchDto dto) {
        return R.result(service.dispatchOrder(tenantIsolation, dto));
    }


    @PostMapping("/receiveOrder")
    @Operation(summary = "保养工单派工")
    public R dispatchOrder(@RequestHeader("dcm_headers")TenantIsolation tenantIsolation,
                           @Validated @RequestBody OrderReceiveDto dto) {
        return R.result(service.receiveOrder(tenantIsolation, dto));
    }

    @PostMapping("/withdrawDispatch/{id}")
    @Operation(summary = "撤回派工")
    public R withdrawDispatch(@RequestHeader("dcm_headers")TenantIsolation tenantIsolation,
                              @PathVariable Long id) {
        return R.result(service.withdrawDispatch(tenantIsolation, id));
    }

//    @PostMapping("/receiveOrder/{id}")
//    @Operation(summary = "保养工单接收")
//    public R receiveOrder(@RequestHeader("dcm_headers")TenantIsolation tenantIsolation,
//                           @PathVariable Long id) {
//        return R.result(service.receiveOrder(tenantIsolation, id));
//    }

    @PostMapping("/executeOrder")
    @Operation(summary = "保养工单执行")
    public R executeOrder(@RequestHeader("dcm_headers")TenantIsolation tenantIsolation,
                          @Validated @RequestBody MaintenanceOrderExecuteDto dto) {
        return R.result(service.executeOrder(tenantIsolation, dto));
    }

    @PostMapping("/withdrawExecute/{id}")
    @Operation(summary = "撤回执行")
    public R withdrawExecute(@RequestHeader("dcm_headers")TenantIsolation tenantIsolation,
                             @PathVariable Long id) {
        return R.result(service.withdrawExecute(tenantIsolation, id));
    }

    @PostMapping("/tempExecuteOrder")
    @Operation(summary = "保养工单执行暂存")
    public R tempExecuteOrder(@RequestHeader("dcm_headers")TenantIsolation tenantIsolation,
                          @Validated @RequestBody MaintenanceOrderExecuteDto dto) {
        return R.result(service.tempExecuteOrder(tenantIsolation, dto));
    }

    @PostMapping("/withdrawOrder")
    @Operation(summary = "保养工单驳回")
    public R withdrawOrder(@RequestHeader("dcm_headers")TenantIsolation tenantIsolation,
                           @Validated @RequestBody MaintenanceOrderWithdrawDto dto) {
        return R.result(service.withdrawOrder(tenantIsolation, dto));
    }

    @PostMapping("/acceptOrder")
    @Operation(summary = "保养工单验收")
    public R acceptOrder(@RequestHeader("dcm_headers")TenantIsolation tenantIsolation,
                          @Validated @RequestBody MaintenanceOrderAcceptDto dto) {
        return R.result(service.acceptOrder(tenantIsolation, dto));
    }

    @PostMapping("/revokeOrder/{id}")
    @Operation(summary = "保养工单撤销")
    public R revokeOrder(@RequestHeader("dcm_headers")TenantIsolation tenantIsolation,
                         @PathVariable Long id) {
        return R.result(service.revokeOrder(tenantIsolation, id));
    }

    @PostMapping("/forwardOrder")
    @Operation(summary = "保养工单转交")
    public R forwardOrder(@RequestHeader("dcm_headers")TenantIsolation tenantIsolation,
                         @RequestBody MaintenanceOrderForwardDto dto) {
        return R.result(service.forwardOrder(tenantIsolation, dto));
    }

    @DeleteMapping("maintenance_order/{entityId}")
    @Operation(summary = "删除对象")
    public R delete(@PathVariable Long entityId){
        Result<Void> result = service.deleteById(entityId);
        return R.result(result);
    }

    @GetMapping("/{id}")
    @Operation(summary = "获取保养工单对象")
    public R get(@RequestHeader("dcm_headers") TenantIsolation tenantIsolation, @PathVariable Long id){
        return R.result(service.getOrderDetailById(tenantIsolation, id));
    }

    @PostMapping("quikOutsourceOrder")
    @Operation(summary = "快捷外委工单")
    public R quikOutsourceOrder(@RequestHeader("dcm_headers") TenantIsolation tenantIsolation,
                    @Validated @RequestBody QuickMaintenanceOrderDto dto){
        return R.result(service.quikOutsourceOrder(tenantIsolation, dto));
    }

    @GetMapping("countDeviceMaintenanceOrder")
    @Operation(summary = "查询设备保养记录")
    public R countDeviceMaintenanceOrder(@RequestHeader("dcm_headers") TenantIsolation tenantIsolation, Long deviceId) {
        return R.result(service.countDeviceMaintenanceOrder(tenantIsolation, deviceId));
    }

    @GetMapping("summary")
    @Operation(summary = "设备保养工单概况")
    public R summary(@RequestHeader("dcm_headers") TenantIsolation tenantIsolation) {
        return R.result(service.summary(tenantIsolation));
    }

    @GetMapping("/countOrderByDate")
    @Operation(summary = "按日期统计保养工单量")
    public R countOrderByDate(@RequestHeader("dcm_headers") TenantIsolation tenantIsolation,
                              @RequestParam("begin") @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate begin,
                              @RequestParam("end") @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate end){
        return R.result(service.countOrderByDate(tenantIsolation, begin, end));
    }

    @GetMapping("pageOrderByDevice")
    @Operation(summary = "根据设备查询保养工单")
    public R pageOrderByDevice(@RequestHeader("dcm_headers") TenantIsolation tenantIsolation, PageParam pageParam,
                  QueryMaintenanceOrderDto dto){
        Page<MaintenanceOrderVo> page = pageParam.toPage(MaintenanceOrderVo.class);
        Result<Page<MaintenanceOrderVo>> result = service.pageOrderByDevice(tenantIsolation, dto, page);
        return R.result(result);
    }

    @PostMapping("uploadItemFile")
    @Operation(summary = "上传保养明细文件")
    public R uploadItemFile(@RequestHeader("dcm_headers") TenantIsolation tenantIsolation,
                        @RequestBody @Validated MaintenanceItemDto dto) {
        return R.result(service.uploadItemFile(tenantIsolation, dto));
    }

    @GetMapping("getMyCustomerAndSupplierCount")
    @Operation(summary = "获取我的客户和供应商数量")
    public R getMyCustomerAndSupplierCount(@RequestHeader("dcm_headers") TenantIsolation tenantIsolation){
        return R.result(service.getMyCustomerAndSupplierCount(tenantIsolation));
    }

    @GetMapping("summaryCustomerOrder")
    @Operation(summary = "客户保养工单概况")
    public R summaryCustomerOrder(@RequestHeader("dcm_headers") TenantIsolation tenantIsolation, @Param("type") Integer type) {
        return R.result(service.summaryCustomerOrder(tenantIsolation, type));
    }

    @GetMapping("/getDeviceMaintenanceResult")
    @Operation(summary = "查询设备保养项及结果")
    public R getDeviceMaintenanceResult(
            @RequestHeader("dcm_headers") TenantIsolation tenant,
            @RequestParam(name = "maintenanceOrderId") Long maintenanceOrderId,
            @RequestParam(name = "deviceId") Long deviceId){
        Result<DeviceMaintenanceResultVo> result = service.getDeviceMaintenanceResult(tenant, maintenanceOrderId, deviceId);
        return R.result(result);
    }

    @PostMapping("/submitDeviceResult")
    @Operation(summary = "保养工单提交设备保养结果-每个设备提交一次")
    public R submitDeviceResult(@RequestHeader("dcm_headers") TenantIsolation tenant, @RequestBody MaintenanceResultDto dto){
        return R.result(service.submitDeviceResult(tenant, dto));
    }

    @PostMapping("/submitResultFile")
    @Operation(summary = "上传保养结果设备文件、图片")
    public R submitResultFile(@RequestHeader("dcm_headers") TenantIsolation tenant, @RequestBody MaintenanceResultFileDto dto){
        return R.result(service.submitResultFile(tenant, dto));
    }

    @PostMapping("/submitOrderFile")
    @Operation(summary = "上传保养工单文件、图片")
    public R submitOrderFile(@RequestHeader("dcm_headers") TenantIsolation tenant, @RequestBody MaintenanceOrderFileDto dto){
        return R.result(service.submitOrderFile(tenant, dto));
    }

    @PostMapping("/submit-maintenance-outbound-order")
    @Operation(summary = "提交保养备件领用出库单")
    public R submitMaintenanceOutboundOrder(@RequestHeader("dcm_headers") TenantIsolation tenant, @RequestBody MaintenanceOutboundOrderDto dto) {
        return R.result(service.submitMaintenanceOutboundOrder(tenant, dto));
    }

    @GetMapping("orderOverdue")
    public R orderOverdueMonitor(){
        return R.result(service.orderOverdueMonitor());
    }
    
}
