package com.nti56.dcm.server.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.nti56.nlink.common.dto.TenantIsolation;
import com.nti56.nlink.common.util.BeanUtilsIntensifier;
import com.nti56.nlink.common.util.PageParam;
import com.nti56.common.util.R;
import com.nti56.nlink.common.util.Result;
import com.nti56.nlink.common.util.ServiceCodeEnum;
import com.nti56.dcm.server.service.WorkloadService;
import com.nti56.dcm.server.entity.WorkloadEntity;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Optional;
import java.util.function.Function;

import lombok.extern.slf4j.Slf4j;


/**
 * 工单人员工作量表 控制器
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2024-06-17 09:27:30
 * @since JDK 1.8
 */
@RestController
@RequestMapping("/workload")
@Slf4j
public class WorkloadController {

    @Autowired
    private WorkloadService service;

    /**
     * 获取列表
     */
    @GetMapping("/list")
    public R<List<WorkloadEntity>> list(@RequestHeader("dcm_headers") TenantIsolation tenant, WorkloadEntity entity){
        Result<List<WorkloadEntity>> result = service.list(tenant.getTenantId(), entity);
        return R.result(result);
    }

    /**
     * 创建对象
     */
    @PostMapping("/create")
    public R<WorkloadEntity> create(@RequestHeader("dcm_headers") TenantIsolation tenant, @RequestBody WorkloadEntity entity){
        Result<WorkloadEntity> result = service.save(tenant.getTenantId(), entity);
        return R.result(result);
    }

    /**
     * 删除对象
     * @param entityId 对象id
     */
    @DeleteMapping("/{entityId}")
    public R<Void> delete(@RequestHeader("dcm_headers") TenantIsolation tenant, @PathVariable Long entityId){
        Result<Void> result = service.deleteById(tenant.getTenantId(), entityId);
        return R.result(result);
    }
    
    // /**
    //  * 获取分页
    //  * @param pageParam 分页参数
    //  */
    // @GetMapping("/page")
    // public R<Page<WorkloadEntity>> page(@RequestHeader("dcm_headers") TenantIsolation tenant, PageParam pageParam,WorkloadEntity entity){
    //     Page<WorkloadEntity> page = pageParam.toPage(WorkloadEntity.class);
    //     Result<Page<WorkloadEntity>> result = service.getPage(tenant.getTenantId(), entity,page);
    //     return R.result(result);
    // }

    // /**
    //  * 更新对象
    //  */
    // @PutMapping("/update")
    // public R<Void> update(@RequestHeader("dcm_headers") TenantIsolation tenant, @RequestBody WorkloadEntity entity){
    //     if (BeanUtilsIntensifier.checkBeanAndProperties(entity, WorkloadEntity::getId)) {
    //         return R.error(ServiceCodeEnum.CODE_PARAM_ERROR.getCode());
    //     }
    //     Result<Void> result = service.update(tenant.getTenantId(), entity);
    //     return R.result(result);
    // }


    // /**
    //  * 获取对象
    //  * @param entityId 对象id
    //  */
    // @GetMapping("/{entityId}")
    // public R<WorkloadEntity> get(@RequestHeader("dcm_headers") TenantIsolation tenant, @PathVariable Long entityId){
    //     Result<WorkloadEntity> result = service.getById(tenant.getTenantId(), entityId);
    //     return R.result(result);
    // }
    
}
