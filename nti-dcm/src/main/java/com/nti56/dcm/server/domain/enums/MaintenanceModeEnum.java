package com.nti56.dcm.server.domain.enums;

import lombok.Getter;

public enum MaintenanceModeEnum {
    ONCE(1, "once", "单次保养"),
    PERIOD(2, "period", "周期性保养"),
    MULTI(3, "multi", "多次检查"),
    ;

    @Getter
    private Integer value;

    @Getter
    private String name;

    @Getter
    private String nameDesc;

    MaintenanceModeEnum(Integer value, String name, String nameDesc) {
        this.value = value;
        this.name = name;
        this.nameDesc = nameDesc;
    }

    public static MaintenanceModeEnum typeOfValue(Integer value){
        MaintenanceModeEnum[] values = MaintenanceModeEnum.values();
        for (MaintenanceModeEnum v : values) {
            if (v.value.equals(value)) {
                return v;
            }
        }
        return null;
    }

    public static MaintenanceModeEnum typeOfName(String name){
        MaintenanceModeEnum[] values = MaintenanceModeEnum.values();
        for (MaintenanceModeEnum v : values) {
            if (v.name.equals(name)) {
                return v;
            }
        }
        return null;
    }

    public static MaintenanceModeEnum typeOfNameDesc(String nameDesc){
        MaintenanceModeEnum[] values = MaintenanceModeEnum.values();
        for (MaintenanceModeEnum v : values) {
            if (v.nameDesc.equals(nameDesc)) {
                return v;
            }
        }
        return null;
    }
}
