package com.nti56.dcm.server.controller;


import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.nti56.common.util.R;
import com.nti56.dcm.server.entity.DeviceEntity;
import com.nti56.dcm.server.model.dto.DeviceExpImportErrorDto;
import com.nti56.dcm.server.model.dto.DeviceRepairExpDto;
import com.nti56.dcm.server.model.dto.DeviceRepairExpExportDto;
import com.nti56.dcm.server.service.IDeviceRepairExpService;
import com.nti56.nlink.common.dto.TenantIsolation;
import com.nti56.nlink.common.util.Result;
import io.swagger.v3.oas.annotations.Operation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.UnsupportedEncodingException;
import java.util.List;

/**
 * <p>
 * 设备维修经验表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-02-29
 */
@RestController
@RequestMapping("/deviceRepairExp")
@Slf4j
public class DeviceRepairExpController {

    @Autowired
    private IDeviceRepairExpService deviceRepairExpService;

    @PostMapping("/page")
    @Operation(summary = "获取设备维修经验分页")
    public R<Page<DeviceRepairExpDto>> page(@RequestHeader("dcm_headers") TenantIsolation tenantIsolation, @RequestBody DeviceRepairExpDto dto) {
        Page<DeviceRepairExpDto> page = dto.toPage(DeviceRepairExpDto.class);
        Result<Page<DeviceRepairExpDto>> result = deviceRepairExpService.page(dto, page, tenantIsolation);
        return R.result(result);
    }

    @PutMapping("/{id}")
    @Operation(summary = "修改设备维修经验")
    public R edit(@RequestHeader("dcm_headers") TenantIsolation tenantIsolation,
                  @RequestBody @Validated DeviceRepairExpDto dto) {
        return R.result(deviceRepairExpService.edit(dto, tenantIsolation));
    }

    @PostMapping("")
    @Operation(summary = "新增设备维修经验")
    public R<DeviceEntity> create(@RequestHeader("dcm_headers") TenantIsolation tenantIsolation,
                                  @RequestBody @Validated DeviceRepairExpDto dto) {
        return R.result(deviceRepairExpService.saveDeviceRepairExp(dto, tenantIsolation));
    }

    @DeleteMapping("/{id}")
    @Operation(summary = "根据id删除设备维修经验")
    public R<DeviceEntity> deleteById(@RequestHeader("dcm_headers") TenantIsolation tenantIsolation,
                                      @PathVariable Long id) {
        return R.result(deviceRepairExpService.deleteById(id, tenantIsolation));
    }

    @GetMapping("/{id}")
    @Operation(summary = "根据id查询设备维修经验")
    public R<DeviceRepairExpDto> getById(@RequestHeader("dcm_headers") TenantIsolation tenantIsolation,
                                         @PathVariable Long id) {
        return R.result(deviceRepairExpService.getById(id, tenantIsolation));
    }

    @PutMapping("/batchDelete")
    @Operation(summary = "批量删除设备维修经验")
    public R batchDelete(@RequestHeader("dcm_headers") TenantIsolation tenantIsolation, @RequestParam("ids") List<Long> ids) {
        return R.result(deviceRepairExpService.batchDelete(ids));
    }


    @GetMapping("/template")
    @Operation(summary = "下载经验库导入模板")
    public void getDeviceTemplate( HttpServletResponse response) throws UnsupportedEncodingException {
        deviceRepairExpService.downloadTemplate( response);

    }

    @PostMapping("/export")
    @Operation(summary = "导出经验库")
    public void export(@RequestHeader("dcm_headers") TenantIsolation tenantIsolation, HttpServletResponse response, @RequestBody DeviceRepairExpExportDto dto) throws UnsupportedEncodingException {
        deviceRepairExpService.exportData(tenantIsolation, response,dto);

    }

    @Operation(summary = "导入经验库")
    @PostMapping(value = "/upload", name = "导入经验库")
    public R<List<DeviceExpImportErrorDto>> uploadDataExcel(@RequestHeader("dcm_headers") TenantIsolation tenantIsolation, @RequestParam("file") MultipartFile file) {
        try {
            return R.result(deviceRepairExpService.uploadData(file, tenantIsolation));
        } catch (Exception ex) {
            log.error("导入创建设备上传文件异常，{}", ex);
            return R.error("导入失败！失败原因：" + ex.toString());
        }
    }
}
