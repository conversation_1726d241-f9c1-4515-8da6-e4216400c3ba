package com.nti56.dcm.server.domain.enums;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.nti56.dcm.server.model.dto.SparesExcelDto;
import com.nti56.dcm.server.model.dto.SparesImportErrorDto;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.function.BiConsumer;
import java.util.function.BiFunction;


public enum SparesImportEnum {
    sparesType("sparesType", "备件类型", (obj, importList) -> {
        String deviceName = (String) obj;
        if (StrUtil.isNotBlank(deviceName)) {
            if (deviceName.length() > 255) {
                return new SparesImportErrorDto("备件类型长度大于255，无法插入！");
            }
        }
        return null;
    }),
    sparesName("sparesName", "备件名称", (obj, importList) -> {
        String sparesName = (String) obj;
        if (StrUtil.isBlank(sparesName)) {
            return null;
        }
        if (sparesName.length() > 64) {
            return new SparesImportErrorDto("备件名称长度大于64，无法插入！");
        }
        if (importList.stream().filter(i -> ObjectUtil.equals(sparesName, i.getSparesName())).count() > 1) {
            return new SparesImportErrorDto("导入数据中存在重复的备件名称！");
        }
        return null;
    }),
    sparesForShort("sparesForShort", "备件简称", (obj, importList) -> {
        String sparesForShort = (String) obj;
        if (StrUtil.isBlank(sparesForShort)) {
            return null;
        }
        if (sparesForShort.length() > 32) {
            return new SparesImportErrorDto("备件简称长度大于32，无法插入！");
        }
        return null;
    }),
    supplier("supplier", "供应商", (obj, importList) -> {
        String validField = (String) obj;
        if (StrUtil.isNotBlank(validField) && validField.length() > 64) {
            return new SparesImportErrorDto("供应商长度大于64，无法插入！");
        }
        return null;
    }),
    deviceType("deviceType", "设备类型", (obj, importList) -> {
        return null;
    }),
    spec("spec", "规格型号", (obj, importList) -> {
        String validField = (String) obj;
        if (StrUtil.isNotBlank(validField) && validField.length() > 64) {
            return new SparesImportErrorDto("规格型号长度大于64，无法插入！");
        }
        return null;
    }),
    unit("unit", "单位", (obj, importList) -> {
        String validField = (String) obj;
        if (StrUtil.isNotBlank(validField) && validField.length() > 16) {
            return new SparesImportErrorDto("单位长度大于16，无法插入！");
        }
        return null;
    }),
    price("price", "价格", (obj, importList) -> {
        String stringPrice = (String) obj;
        BigDecimal validField;
        try {
            validField = new BigDecimal(stringPrice);
        } catch (Exception e) {
            return new SparesImportErrorDto("价格格式有误，无法转换为数字，无法插入！");
        }
        String[] split = stringPrice.toString().split("\\.");
        if (split.length == 2 && split[1].length() > 4) {
            return new SparesImportErrorDto("价格最多保留四位有效数字，无法插入！");
        }
        int allLength = 0;
        for (String s : split) {
            allLength += s.length();
        }
        if (allLength > 16) {
            return new SparesImportErrorDto("价格最长为16位数(包含小数点)，数据超长，无法插入！");
        }
        return null;
    }),
    stockUpperLimit("stockUpperLimit", "库存上限", (obj, importList) -> {
        return null;
    }),
    stockLowerLimit("stockLowerLimit", "库存下限", (obj, importList) -> {
        return null;
    }),
    purchaseCycleNum("purchaseCycleNum", "采购周期", (obj, importList) -> {
        return null;
    }),
    purchaseUnit("purchaseCycleUnit", "采购周期单位", (obj, importList) -> {
        String validField = (String) obj;
        if (!Arrays.asList("天", "月", "年").contains(validField)) {
            return new SparesImportErrorDto("采购周期单位必须是天、月、年中的一种!");
        }
        return null;
    }),
    changeCycleNum("changeCycleNum", "更换周期", (obj, importList) -> {
        return null;
    }),
    changeUnit("changeUnit", "更换周期单位", (obj, importList) -> {
        String validField = (String) obj;
        if (!Arrays.asList("天", "月", "年").contains(validField)) {
            return new SparesImportErrorDto("更换周期单位必须是天、月、年中的一种!");
        }
        return null;
    }),

    ;
    /**
     * 数据库的列名
     * 类中的变量名
     */
    private String objColName;

    /**
     * 对应的列名（excel）
     */
    private String excelColTitle;

    /**
     * 校验列数据的方法
     */
    private BiFunction<Object, List<SparesExcelDto>, SparesImportErrorDto> function;

    SparesImportEnum(String objColName, String excelColTitle, BiFunction<Object, List<SparesExcelDto>, SparesImportErrorDto> function) {
        this.objColName = objColName;
        this.excelColTitle = excelColTitle;
        this.function = function;
    }

    BiConsumer<Object, List<SparesImportErrorDto>> test = (str, num) -> {
        // 在这里执行操作
    };

    public String getObjColName() {
        return objColName;
    }

    public void setObjColName(String objColName) {
        this.objColName = objColName;
    }

    public String getExcelColTitle() {
        return excelColTitle;
    }

    public void setExcelColTitle(String excelColTitle) {
        this.excelColTitle = excelColTitle;
    }

    public static List<String> getObjColNames() {
        List<String> list = new ArrayList<>();
        SparesImportEnum[] values = SparesImportEnum.values();
        for (SparesImportEnum current : values) {
            list.add(current.getObjColName());
        }
        return list;
    }

    public static List<String> getExcelColTitles() {
        List<String> list = new ArrayList<>();
        SparesImportEnum[] values = SparesImportEnum.values();
        for (SparesImportEnum current : values) {
            list.add(current.getExcelColTitle());
        }
        return list;
    }


    /**
     * excel列名为key, 对象属性为 value
     *
     * @return
     */
    public static Map<String, String> getObjNameMap() {
        Map<String, String> colMap = new HashMap<>();
        for (SparesImportEnum obj : SparesImportEnum.values()) {
            colMap.put(obj.getExcelColTitle(), obj.getObjColName());
        }
        return colMap;
    }

    public static SparesImportErrorDto executeValidFunctionByColName(String objColName, Object value, List<SparesExcelDto> importList) {
        if (StrUtil.isBlank(objColName)) {
            return null;
        }
        for (SparesImportEnum obj : SparesImportEnum.values()) {
            if (obj.objColName.equals(objColName)) {
                return obj.function.apply(value, importList);
            }
        }
        return null;
    }

    public static String getValueStringTemplate() {
        StringBuilder sb = new StringBuilder();
        sb.append("[");
        SparesImportEnum[] values = SparesImportEnum.values();
        for (int i = 0; i < values.length; i++) {
            SparesImportEnum exportEnums = values[i];
            if (i == 0) {
                sb.append(exportEnums.getExcelColTitle());
            } else {
                sb.append(", " + exportEnums.getExcelColTitle());
            }
        }
        sb.append("]");
        return sb.toString();
    }
}
