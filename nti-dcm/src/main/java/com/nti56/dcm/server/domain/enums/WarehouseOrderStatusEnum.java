package com.nti56.dcm.server.domain.enums;

import lombok.Getter;

@Getter
public enum WarehouseOrderStatusEnum {
    EXECUTION(1, "执行中", "执行中"),
    DONE(2, "已完成", "已完成"),
    DRAFT(0, "待执行", "待执行"),
    ;

    private Integer value;

    private String name;

    private String nameDesc;

    WarehouseOrderStatusEnum(Integer value, String name, String nameDesc) {
        this.value = value;
        this.name = name;
        this.nameDesc = nameDesc;
    }

    public static WarehouseOrderStatusEnum typeOfValue(Integer value){
        WarehouseOrderStatusEnum[] values = WarehouseOrderStatusEnum.values();
        for (WarehouseOrderStatusEnum v : values) {
            if (v.value.equals(value)) {
                return v;
            }
        }
        return null;
    }

    public static WarehouseOrderStatusEnum typeOfName(String name){
        WarehouseOrderStatusEnum[] values = WarehouseOrderStatusEnum.values();
        for (WarehouseOrderStatusEnum v : values) {
            if (v.name.equals(name)) {
                return v;
            }
        }
        return null;
    }

    public static WarehouseOrderStatusEnum typeOfNameDesc(String nameDesc){
        WarehouseOrderStatusEnum[] values = WarehouseOrderStatusEnum.values();
        for (WarehouseOrderStatusEnum v : values) {
            if (v.nameDesc.equals(nameDesc)) {
                return v;
            }
        }
        return null;
    }
}
