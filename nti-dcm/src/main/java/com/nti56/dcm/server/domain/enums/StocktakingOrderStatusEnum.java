package com.nti56.dcm.server.domain.enums;

import lombok.Getter;

public enum StocktakingOrderStatusEnum {
    WAIT_PROCESS(1, "waitProcess", "未开始"),
    PROCESSING(2, "processing", "执行中"),
    FINISH(3, "finish", "已完成"),
    ;

    @Getter
    private Integer value;

    @Getter
    private String name;

    @Getter
    private String nameDesc;

    StocktakingOrderStatusEnum(Integer value, String name, String nameDesc) {
        this.value = value;
        this.name = name;
        this.nameDesc = nameDesc;
    }

    public static StocktakingOrderStatusEnum typeOfValue(Integer value){
        StocktakingOrderStatusEnum[] values = StocktakingOrderStatusEnum.values();
        for (StocktakingOrderStatusEnum v : values) {
            if (v.value.equals(value)) {
                return v;
            }
        }
        return null;
    }

    public static StocktakingOrderStatusEnum typeOfName(String name){
        StocktakingOrderStatusEnum[] values = StocktakingOrderStatusEnum.values();
        for (StocktakingOrderStatusEnum v : values) {
            if (v.name.equals(name)) {
                return v;
            }
        }
        return null;
    }

    public static StocktakingOrderStatusEnum typeOfNameDesc(String nameDesc){
        StocktakingOrderStatusEnum[] values = StocktakingOrderStatusEnum.values();
        for (StocktakingOrderStatusEnum v : values) {
            if (v.nameDesc.equals(nameDesc)) {
                return v;
            }
        }
        return null;
    }
}
