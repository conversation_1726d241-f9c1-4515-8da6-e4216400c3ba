package com.nti56.dcm.server.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * <p>
 * 项目进度表 实体类
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @Ddate 2024-12-09 09:53:42
 * @since JDK 1.8
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@TableName("pmo_project_milestone")
public class PmoProjectMilestoneEntity implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
    * 主键
    */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
    * 租户id
    */
    @TableField(fill = FieldFill.INSERT)
    private Long clientId;

    /**
    * 里程碑类型  1销售咨询阶段、2集成设计阶段、3工程实施阶段  4质保阶段
    */
    private String milestoneType;

    /**
    * 项目名称
    */
    private String projectName;

    /**
    * 项目id
    */
    private Long projectId;

    /**
    * 里程碑父级节点ID
    */
    private String pId;

    /**
    * 里程碑父级节点ID
    */
    private String pid;

    /**
    * 里程碑编码
    */
    private String milestoneCode;

    /**
    * 里程碑名称
    */
    private String milestoneName;

    /**
    * 计划开始时间
    */
    private LocalDate planDate;

    /**
    * 实际开始时间
    */
    private LocalDate realDate;

    /**
    * 预计完成时间
    */
    private LocalDate expectFinishDate;

    /**
    * 实际完成时间
    */
    private LocalDate realFinishDate;

    /**
    * 排序
    */
    private Integer sortNo;

    /**
    * 备注
    */
    private String remark;

    /**
    * 删除状态;;0:未删除，1：已删除
    */
    @TableLogic
    private Integer deleted;

    /**
    * 创建人
    */
    @TableField(fill = FieldFill.INSERT)
    private String createBy;

    /**
    * 创建人姓名
    */
    private String createByName;

    /**
    * 创建时间
    */
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
    * 修改人
    */
    private String modifyBy;

    /**
    * 创建人姓名
    */
    private String modifyByName;

    /**
    * 更新时间
    */
    private LocalDateTime modifyTime;

    /**
    * 版本号
    */
    @Version
    private Long version;



}
