package com.nti56.common.util;

import java.util.HashMap;

import com.nti56.dcm.server.model.result.ITResult;
import com.nti56.nlink.common.util.Result;
import com.nti56.nlink.common.util.ServiceCodeEnum;

import lombok.Data;

@Data
public class R<T>{

    private Boolean ok;
    private T data;
    private Integer code;
    private String message;

    /**
     * 正确结果，无结果对象
     * @param <T>
     * @return
     */
    public static <T> R<T> result(Result<T> result) {
        if(result.getSignal()){
            return ok(result.getResult());
        }else{
            return error(result.getServiceCode(), result.getMessage());
        }
    }

    public static R<Object> result(ITResult<Object> result) {
        if(result.getCode() == 0){
            return ok(result.getData());
        }else{
            return error(result.getCode(), result.getMessage());
        }
    }

    /**
     * 正确结果，无结果对象
     * @param <T>
     * @return
     */
    public static <T> R<T> ok() {
        R<T> r = new R<>();
        r.ok = true;
        r.code = ServiceCodeEnum.OK.getCode();
        r.message = ServiceCodeEnum.OK.getMessage();
        return r;
    }

    /**
     * 正确结果，有结果对象
     * @param <T>
     * @return
     */
    public static <T> R<T> ok(T result) {
        
        R<T> r = new R<>();
        r.ok = true;
        r.code = ServiceCodeEnum.OK.getCode();
        r.message = ServiceCodeEnum.OK.getMessage();
        r.data = result;
        return r;
    }

    /**
     * 正确结果，有结果对象
     * @param <T>
     * @return
     */
    public static <T> R<T> ok(T result, String message) {
        R<T> r = new R<>();
        r.ok = true;
        r.code = ServiceCodeEnum.OK.getCode();
        r.message = message;
        r.data = result;
        return r;
    }

    /**
     * 错误结果，使用默认错误code和message
     * @param <T>
     * @return
     */
    public static <T> R<T> error() {
        R<T> r = new R<>();
        r.ok = false;
        r.code = ServiceCodeEnum.CODE_UNKNOWN_ERROR.getCode();
        r.message = ServiceCodeEnum.CODE_UNKNOWN_ERROR.getMessage();
        return r;
    }

    /**
     * 错误结果，使用枚举的code和message
     * @param <T>
     * @return
     */
    public static <T> R<T> error(Integer code) {
        R<T> r = new R<>();
        r.ok = false;
        r.code = code;
        return r;
    }

    /**
     * 错误结果，使用默认错误code，并指定message
     * @param <T>
     * @return
     */
    public static <T> R<T> error(String message) {
        R<T> r = new R<>();
        r.ok = false;
        r.code = ServiceCodeEnum.CODE_UNKNOWN_ERROR.getCode();
        r.message = message;
        return r;
    }

    /**
     * 错误结果，使用指定code和指定message
     * @param <T>
     * @return
     */
    public static <T> R<T> error(Integer code, String message) {
        R<T> r = new R<>();
        r.ok = false;
        r.code = code;
        r.message = message;
        return r;
    }


    private R() {
    }

    private R(Boolean ok, Integer code) {
        this.ok = ok;
        this.code = code;
    }

    private R(Boolean ok, Integer code, T result) {
        this.ok = ok;
        this.code = code;
        this.data = result;
    }



}