package com.nti56.dcm.server.domain.enums;

import lombok.Getter;

public enum InspectModeEnum {
    ONCE(1, "once", "单次检查"),
    PERIOD(2, "period", "周期性检查"),
    MULTI(3, "multi", "多次检查"),
    ;

    @Getter
    private Integer value;

    @Getter
    private String name;

    @Getter
    private String nameDesc;

    InspectModeEnum(Integer value, String name, String nameDesc) {
        this.value = value;
        this.name = name;
        this.nameDesc = nameDesc;
    }

    public static InspectModeEnum typeOfValue(Integer value){
        InspectModeEnum[] values = InspectModeEnum.values();
        for (InspectModeEnum v : values) {
            if (v.value.equals(value)) {
                return v;
            }
        }
        return null;
    }

    public static InspectModeEnum typeOfName(String name){
        InspectModeEnum[] values = InspectModeEnum.values();
        for (InspectModeEnum v : values) {
            if (v.name.equals(name)) {
                return v;
            }
        }
        return null;
    }

    public static InspectModeEnum typeOfNameDesc(String nameDesc){
        InspectModeEnum[] values = InspectModeEnum.values();
        for (InspectModeEnum v : values) {
            if (v.nameDesc.equals(nameDesc)) {
                return v;
            }
        }
        return null;
    }
}
