package com.nti56.dcm.server.controller;


import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.nti56.dcm.server.model.dto.DataPermissionDto;
import com.nti56.dcm.server.service.IDataPermissionService;
import com.nti56.nlink.common.dto.TenantIsolation;
import com.nti56.nlink.common.util.R;

import io.swagger.annotations.ApiOperation;
import io.swagger.v3.oas.annotations.tags.Tag;

/**
 * <p>
 * 数据权限配置表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-31
 */
@RestController
@RequestMapping("/dataPermission")
@Tag(name = "数据权限配置")
public class DataPermissionController {

    @Autowired
    private IDataPermissionService dataPermissionService;


    @PostMapping("/overrideDevice")
    @ApiOperation("覆盖设备")
    public R overrideDevice(@RequestHeader("dcm_headers") TenantIsolation tenantIsolation, @RequestBody DataPermissionDto dto) {
        return R.result(dataPermissionService.edit( dto,tenantIsolation));
    }

    @GetMapping("/getDetail")
    @ApiOperation("获取详情")
    public R getDetail(@RequestHeader("dcm_headers") TenantIsolation tenantIsolation, Long roleId) {
        return R.result(dataPermissionService.getDetailByRoleId(roleId,tenantIsolation));
    }
}
