package com.nti56.dcm.server.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import lombok.Data;

/**
 * <p>
 * 点巡检结果标准表 实体类
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @Ddate 2024-03-13 16:56:29
 * @since JDK 1.8
 */
@Data
@TableName("inspect_result_standard")
public class InspectResultStandardEntity extends BaseEntity{
        /**
        * 点巡检结果id
        */
        private Long inspectResultId;

        /**
        * 点巡检标准id
        */
        private Long inspectStandardId;



}
