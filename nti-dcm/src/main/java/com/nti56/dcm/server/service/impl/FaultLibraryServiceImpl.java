package com.nti56.dcm.server.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.nti56.dcm.server.constant.DictConstant;
import com.nti56.dcm.server.domain.enums.ConnectOtEnum;
import com.nti56.dcm.server.domain.enums.IdTypeEnum;
import com.nti56.dcm.server.domain.enums.RelationStatusEnum;
import com.nti56.dcm.server.entity.CustomerRelationEntity;
import com.nti56.dcm.server.entity.DeviceEntity;
import com.nti56.dcm.server.entity.TenantInfoEntity;
import com.nti56.dcm.server.feign.OtController;
import com.nti56.dcm.server.mapper.CustomerRelationMapper;
import com.nti56.dcm.server.mapper.DeviceMapper;
import com.nti56.dcm.server.mapper.TenantInfoMapper;
import com.nti56.dcm.server.model.dto.FaultLibraryDto;
import com.nti56.dcm.server.model.result.ITResult;
import com.nti56.dcm.server.model.vo.FaultLibraryVo;
import com.nti56.dcm.server.model.vo.ThingEventVo;
import com.nti56.dcm.server.model.vo.ThingModelVo;
import com.nti56.dcm.server.service.CustomerRelationService;
import com.nti56.dcm.server.service.DeviceService;
import com.nti56.nlink.common.dto.TenantIsolation;
import com.nti56.nlink.common.util.Result;
import com.nti56.nlink.common.util.ServiceCodeEnum;
import com.nti56.dcm.server.entity.FaultLibraryEntity;
import com.nti56.dcm.server.mapper.FaultLibraryMapper;
import com.nti56.dcm.server.service.IFaultLibraryService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.validation.constraints.NotNull;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <p>
 * 故障库表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @Ddate 2025-06-04 14:42:27
 * @since JDK 1.8
 */
@Slf4j
@Service
public class FaultLibraryServiceImpl extends ServiceImpl<FaultLibraryMapper, FaultLibraryEntity>
        implements IFaultLibraryService {

    @Autowired
    private FaultLibraryMapper mapper;

    @Autowired
    private TenantInfoMapper tenantInfoMapper;

    @Autowired
    private OtController otController;

    @Autowired
    private DeviceService deviceService;

    @Autowired
    private CustomerRelationService customerRelationService;

    @Autowired
    private CustomerRelationMapper customerRelationMapper;

    @Autowired
    private DeviceMapper deviceMapper;

    @Override
    public Result<FaultLibraryEntity> save(TenantIsolation tenant, FaultLibraryEntity entity) {
        // 检查故障代码在同个设备类型下是否唯一
        Result<Void> uniqueResult = checkFaultCodeUnique(tenant, entity.getDeviceTypeId(), entity.getFaultCode(), null);
        if (!uniqueResult.getSignal()) {
            return Result.error(uniqueResult.getServiceCode(), uniqueResult.getMessage());
        }
        
        // 检查设备类型+故障描述是否唯一
        Result<Void> descUniqueResult = checkDeviceTypeAndDescUnique(tenant, entity.getDeviceTypeId(), entity.getFaultDesc(), null);
        if (!descUniqueResult.getSignal()) {
            return Result.error(descUniqueResult.getServiceCode(), descUniqueResult.getMessage());
        }

        entity.setIdType(tenant.getIdType());
        if (mapper.insert(entity) == 1) {
            return Result.ok(entity);
        }
        return Result.error(ServiceCodeEnum.CODE_CREATE_FAIL);
    }

    @Override
    public Result<Page<FaultLibraryVo>> getPage(TenantIsolation tenant, FaultLibraryDto dto, Page<FaultLibraryVo> page) {
        List<FaultLibraryVo> allFaultLibrary = new ArrayList<>();

        // 查询pmo设备的故障库
        TenantInfoEntity tenantInfo = tenantInfoMapper.selectById(tenant.getTenantId());
        if (DictConstant.IS_PMO.equals(tenantInfo.getIsPmoTenant())) {
            // 查询PMO租户设备
            Set<Long> deviceTypeIds = deviceService.list(Wrappers.<DeviceEntity>lambdaQuery()
                    .eq(DeviceEntity::getTenantId, tenantInfo.getPmoTenantId()))
                    .stream().map(DeviceEntity::getDeviceTypeId).collect(Collectors.toSet());
            if (CollectionUtil.isNotEmpty(deviceTypeIds)) {
                dto.setDeviceTypeIds(deviceTypeIds);
                dto.setCustomerId(tenantInfo.getTenantId());
                List<FaultLibraryVo> pmoFaultLibrary = mapper.listFaultLibraryByPmo(dto);
                allFaultLibrary.addAll(pmoFaultLibrary);
            }
        }

        // 查询自身设备的故障库
        dto.setTenantId(tenant.getTenantId());
        dto.setIdType(tenant.getIdType());
        List<FaultLibraryVo> selfDevice = mapper.listFaultLibrary(dto);
        allFaultLibrary.addAll(selfDevice);

        //拼接分页信息
        int total = Optional.ofNullable(allFaultLibrary).map(List::size).orElse(0);
        long pages = total % page.getSize() == 0 ? (total / page.getSize()) : (total / page.getSize() + 1);
        page.setPages(pages);
        page.setTotal(total);
        if (CollectionUtil.isNotEmpty(allFaultLibrary)) {
            page.setRecords(allFaultLibrary.stream().skip((page.getCurrent() - 1) * page.getSize()).limit(page.getSize()).collect(Collectors.toList()));
        } else {
            page.setRecords(new ArrayList<>());
        }
        return Result.ok(page);
    }

    @Override
    public Result<List<FaultLibraryEntity>> list(TenantIsolation tenant, FaultLibraryEntity entity) {
        List<FaultLibraryEntity> list = mapper.selectList(new QueryWrapper<>(entity));
        return Result.ok(list);
    }

    @Override
    public Result<Void> update(TenantIsolation tenant, FaultLibraryEntity entity) {
        // 检查故障代码在同个设备类型下是否唯一（排除当前记录）
        Result<Void> uniqueResult = checkFaultCodeUnique(tenant, entity.getDeviceTypeId(), entity.getFaultCode(), entity.getId());
        if (!uniqueResult.getSignal()) {
            return uniqueResult;
        }
        
        // 检查设备类型+故障描述是否唯一（排除当前记录）
        Result<Void> descUniqueResult = checkDeviceTypeAndDescUnique(tenant, entity.getDeviceTypeId(), entity.getFaultDesc(), entity.getId());
        if (!descUniqueResult.getSignal()) {
            return descUniqueResult;
        }

        if (mapper.updateById(entity) > 0) {
            return Result.ok();
        }
        return Result.error(ServiceCodeEnum.CODE_UPDATE_FAIL);
    }

    @Override
    public Result<Void> deleteById(@NotNull Long entityId) {
        if (mapper.deleteById(entityId) > 0) {
            return Result.ok();
        }
        return Result.error(ServiceCodeEnum.CODE_DELETE_FAIL);
    }

    @Override
    public Result<Void> batchDelete(List<Long> entityIds) {
        if (CollectionUtil.isEmpty(entityIds)) {
            return Result.error("故障库ID不能为空！");
        }
        mapper.deleteBatchIds(entityIds);
        return Result.ok();
    }

    @Override
    public Result<FaultLibraryEntity> getById(@NotNull Long entityId) {
        FaultLibraryEntity entity = mapper.selectById(entityId);
        return Result.ok(entity);
    }

    /**
     * 检查故障代码在同个设备类型下是否唯一
     *
     * @param deviceTypeId 设备类型ID
     * @param faultCode    故障代码
     * @param excludeId    排除的记录ID（用于更新时排除自身）
     * @return 检查结果
     */
    private Result<Void> checkFaultCodeUnique(TenantIsolation tenant, Long deviceTypeId, String faultCode, Long excludeId) {
        if (deviceTypeId == null || StrUtil.isBlank(faultCode)) {
            return Result.ok();
        }

        QueryWrapper<FaultLibraryEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("device_type_id", deviceTypeId)
                .eq("fault_code", faultCode)
                .eq("tenant_id", tenant.getTenantId())
                .eq("id_type", tenant.getIdType());

        // 更新时排除当前记录
        if (excludeId != null) {
            queryWrapper.ne("id", excludeId);
        }

        List<FaultLibraryEntity> existingList = mapper.selectList(queryWrapper);
        if (!existingList.isEmpty()) {
            return Result.error("该设备类型下故障代码已存在，请使用其他故障代码");
        }

        return Result.ok();
    }
    
    /**
     * 检查设备类型+故障描述是否唯一
     *
     * @param tenant       租户信息
     * @param deviceTypeId 设备类型ID
     * @param faultDesc    故障描述
     * @param excludeId    排除的记录ID（用于更新时排除自身）
     * @return 检查结果
     */
    private Result<Void> checkDeviceTypeAndDescUnique(TenantIsolation tenant, Long deviceTypeId, String faultDesc, Long excludeId) {
        if (deviceTypeId == null || StrUtil.isBlank(faultDesc)) {
            return Result.ok();
        }

        QueryWrapper<FaultLibraryEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("device_type_id", deviceTypeId)
                .eq("fault_desc", faultDesc)
                .eq("tenant_id", tenant.getTenantId())
                .eq("id_type", tenant.getIdType());

        // 更新时排除当前记录
        if (excludeId != null) {
            queryWrapper.ne("id", excludeId);
        }

        List<FaultLibraryEntity> existingList = mapper.selectList(queryWrapper);
        if (!existingList.isEmpty()) {
            return Result.error("该设备类型下已存在相同故障描述的记录，请修改故障描述");
        }

        return Result.ok();
    }

    @Override
    public void syncOTEvent() {
        // 获取所有租户
        List<TenantInfoEntity> tenantList = tenantInfoMapper.listAllTenant();
        if (CollectionUtil.isEmpty(tenantList)) {
            return;
        }

        for (TenantInfoEntity tenant : tenantList) {
            // 处理客户身份的设备事件
            processCustomerDeviceEvent(tenant);
            // 处理供应商身份的设备事件
            processSupplierDeviceEvent(tenant);
        }
    }

    private void processCustomerDeviceEvent(TenantInfoEntity tenant) {
        // 查询租户的所有设备
        List<DeviceEntity> deviceList = deviceService.list(Wrappers.<DeviceEntity>lambdaQuery()
                .select(DeviceEntity::getId, DeviceEntity::getDeviceTypeId, DeviceEntity::getDeviceName,
                        DeviceEntity::getDeviceAliasName, DeviceEntity::getConnectOt, DeviceEntity::getIsPmo)
                .eq(DeviceEntity::getTenantId, tenant.getId())
                .eq(DeviceEntity::getConnectOt, ConnectOtEnum.CONNECT.getValue())
        );

        if (CollectionUtil.isEmpty(deviceList)) {
            log.info("租户[{}]没有设备数据，跳过同步", tenant.getId());
            return;
        }

        processDevice(tenant.getId(), IdTypeEnum.CUSTOMER.getValue(), deviceList);
    }

    private void processSupplierDeviceEvent(TenantInfoEntity tenant) {
        List<CustomerRelationEntity> customerList = customerRelationMapper.listCustomerByEntity(CustomerRelationEntity.builder()
                .supplierId(tenant.getTenantId())
                .idType(IdTypeEnum.SUPPLIER.getValue())
                .status(RelationStatusEnum.APPROVE.getValue())
                .build());
        Set<Long> customerIds = customerList.stream().map(CustomerRelationEntity::getCustomerId).collect(Collectors.toSet());
        if (CollectionUtil.isEmpty(customerIds)) {
            log.info("供应商[{}]没有客户数据，跳过同步", tenant.getId());
            return;
        }

        // 查询供应商的所有设备
        List<DeviceEntity> deviceList = deviceMapper.listPmoDeviceByTenantIds(customerIds);

        if (CollectionUtil.isEmpty(deviceList)) {
            log.info("供应商[{}]没有设备数据，跳过同步", tenant.getId());
            return;
        }

        processDevice(tenant.getId(), IdTypeEnum.SUPPLIER.getValue(), deviceList);
    }

    private void processDevice(Long tenantId, Integer idType, List<DeviceEntity> deviceList) {
        // 按设备类型分组
        Map<Long, List<DeviceEntity>> deviceTypeGroupMap = deviceList.stream()
                .collect(Collectors.groupingBy(DeviceEntity::getDeviceTypeId));

        // 遍历每个设备类型组
        for (Map.Entry<Long, List<DeviceEntity>> entry : deviceTypeGroupMap.entrySet()) {
            Long deviceTypeId = entry.getKey();
            List<DeviceEntity> devices = entry.getValue();

            // 获取该类型下所有设备ID
            List<String> deviceNames = devices.stream()
                    .map(DeviceEntity::getDeviceAliasName)
                    .collect(Collectors.toList());

            if (CollectionUtil.isEmpty(deviceNames)) {
                continue;
            }

            // 获取OT物模型事件数据
            List<ThingEventVo> eventList = getOtThingModel(tenantId, deviceNames);

            if (CollectionUtil.isEmpty(eventList)) {
                log.info("设备类型[{}]没有故障事件数据，跳过处理", deviceTypeId);
                continue;
            }

            // 处理故障事件数据
            processThingEvents(tenantId, idType, deviceTypeId, eventList);
        }
    }

    /**
     * 处理物模型事件数据，转换为故障库记录
     *
     * @param tenantId     租户ID
     * @param deviceTypeId 设备类型ID
     * @param eventList    事件列表
     */
    private void processThingEvents(Long tenantId, Integer idType, Long deviceTypeId, List<ThingEventVo> eventList) {
        List<FaultLibraryEntity> faultList = mapper.selectList(Wrappers.<FaultLibraryEntity>lambdaQuery()
               .eq(FaultLibraryEntity::getTenantId, tenantId)
               .eq(FaultLibraryEntity::getIdType, idType)
               .eq(FaultLibraryEntity::getDeviceTypeId, deviceTypeId));

        for (ThingEventVo event : eventList) {
            // 第一步：检查是否存在【设备类型】+【事件描述】一致的数据
            List<FaultLibraryEntity> existingByDesc = faultList.stream()
                    .filter(f -> f.getFaultDesc().equals(event.getDescript()))
                    .collect(Collectors.toList());

            if (CollectionUtil.isEmpty(existingByDesc)) {
                // 不存在【设备类型】+【事件描述】一致的数据，直接新增
                FaultLibraryEntity faultEntity = new FaultLibraryEntity();
                faultEntity.setTenantId(tenantId);
                faultEntity.setIdType(idType);
                faultEntity.setDeviceTypeId(deviceTypeId);
                faultEntity.setFaultEvent(event.getName());
                faultEntity.setFaultDesc(event.getDescript());
                faultEntity.setCreator("同步任务");

                mapper.insert(faultEntity);

                faultList.add(faultEntity);
            } else {
                List<FaultLibraryEntity> existingByDescAndEvent = faultList.stream()
                        .filter(f -> f.getFaultDesc().equals(event.getDescript()) && f.getFaultEvent().equals(event.getName()))
                        .collect(Collectors.toList());
                if (CollectionUtil.isNotEmpty(existingByDescAndEvent)) {
                    continue;
                }

                // 第二步：存在【设备类型】+【事件描述】一致的数据，继续检查是否存在【设备类型】+【事件描述】+【故障事件名称】一致的数据
                List<FaultLibraryEntity> existingByDescAndName = faultList.stream()
                        .filter(f -> f.getFaultDesc().equals(event.getName() + "-" + event.getDescript()) && f.getFaultEvent().equals(event.getName()))
                        .collect(Collectors.toList());

                if (CollectionUtil.isEmpty(existingByDescAndName)) {
                    // 不存在【设备类型】+【事件描述】+【故障事件名称】一致的数据，新增
                    FaultLibraryEntity faultEntity = new FaultLibraryEntity();
                    faultEntity.setTenantId(tenantId);
                    faultEntity.setIdType(idType);
                    faultEntity.setDeviceTypeId(deviceTypeId);
                    faultEntity.setFaultDesc(event.getName() + "-" + event.getDescript());
                    faultEntity.setFaultEvent(event.getName());
                    faultEntity.setCreator("同步任务");

                    mapper.insert(faultEntity);

                    faultList.add(faultEntity);
                }
            }
        }
    } 

    public List<ThingEventVo> getOtThingModel(Long tenantId, List<String> deviceNames) {
        List<ThingEventVo> list = new ArrayList();

        ITResult<Object> result = otController.listModelByDeviceName(tenantId, deviceNames);

        if (!ITResult.INFO_CODE.equals(result.getCode())) {
            return list;
        }

        JSONArray dataArray = JSONArray.parseArray(JSONObject.toJSONString(result.getData()));
        if (dataArray != null && !dataArray.isEmpty()) {
            List<ThingModelVo> modelList = dataArray.toJavaList(ThingModelVo.class);
            for (ThingModelVo model : modelList) {
                list.addAll(model.getEvents()
                        .stream()
                        .filter(e -> StringUtils.isNotEmpty(e.getType()) && "fault".equals(e.getType()))
                        .collect(Collectors.toList()));
            }
        }
        return list;
    }

}
