package com.nti56.common.enums;

public enum ErrorEnum {

    NO_AUTH(80001,"登录超时,请重新登录！"),
    BACKEND_COMEXCEPTION(40001,"网关与鉴权服务通讯异常"),
    SIGNATURE_ERROR(40002,"签名错误"),
    NO_WHITE_LIST(40003,"远程地址不在白名单中"),
    KEY_EXPIRED(40004,"密钥已过期"),
    ABNORMAL_PERMISSION(49999,"权限异常"),
    UNKNOW_TENANT(49998,"未知租户"),

            ;


    private final int  code;
    private final String message;

    ErrorEnum(int code, String message) {
        this.code= code;
        this.message = message;
    }

    public int getCode() {
        return code;
    }

    public String getMessage() {
        return message;
    }
}
