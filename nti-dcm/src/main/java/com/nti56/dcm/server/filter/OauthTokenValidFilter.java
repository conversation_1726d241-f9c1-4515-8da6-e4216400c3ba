package com.nti56.dcm.server.filter;

import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.nti56.common.constant.Constant;
import com.nti56.common.enums.ErrorEnum;
import com.nti56.dcm.server.config.IgnoreUrlsConfig;
import com.nti56.nlink.common.dto.TenantIsolation;
import com.nti56.common.util.R;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.annotation.Order;
import org.springframework.http.*;
import org.springframework.stereotype.Component;
import org.springframework.util.AntPathMatcher;
import org.springframework.util.PathMatcher;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.util.UriComponentsBuilder;

import javax.servlet.*;
import javax.servlet.annotation.WebFilter;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;
import java.util.Objects;

@WebFilter(urlPatterns = "/**",filterName = "oauthTokenValidFilter")
@Slf4j
@Order(0)
@Component
public class OauthTokenValidFilter implements Filter {

    @Autowired
    private IgnoreUrlsConfig ignoreUrlsConfig;

    @Value("${user-center.base-url}")
    private String baseUrl;

    @Value("${user-center.check-token}")
    private String checkToken;

    @Autowired
    @Qualifier("restTemplate")
    private RestTemplate restHttpTemplate;

    @Override
    public void doFilter(ServletRequest servletRequest, ServletResponse servletResponse, FilterChain chain) throws IOException, ServletException {
        HttpServletRequest request = (HttpServletRequest) servletRequest;
        HttpServletResponse response = (HttpServletResponse) servletResponse;
        if (Constant.AUTH_FAILED_PATH.equals(request.getRequestURI())) {
            chain.doFilter(servletRequest,servletResponse);
            return;
        }
        if (!authRequest(request,response)) {
            response.setStatus(HttpStatus.UNAUTHORIZED.value());
            ErrorEnum errorEnum = (ErrorEnum) request.getAttribute(Constant.AUTH_ERROR_ATTR_NAME);
            R error;
            if (Objects.isNull(errorEnum)) {
                error = R.error();
            }else {
                error = R.error(errorEnum.getCode(), errorEnum.getMessage());
            }
            response.setContentType("application/json; charset=UTF-8");
            response.getWriter().write(JSONObject.toJSONString(error));
            log.warn("token校验失败,msg:{}",error);
            return;
        }
        chain.doFilter(servletRequest,servletResponse);
    }

   /* @Autowired
    private ResourceServerTokenServices resourceServerTokenServices;*/

    /**
     * 请求认证、授权
     *
     * @return 验证失败返回false, 成功true
     */
    private boolean authRequest(HttpServletRequest request, HttpServletResponse response) {
        PathMatcher pathMatcher = new AntPathMatcher();
        List<String> ignoreUrls = ignoreUrlsConfig.getAnon();
        String requestURI = request.getRequestURI();
        for (String ignoreUrl : ignoreUrls) {
            if (pathMatcher.match(ignoreUrl, requestURI)) {
                return true;
            }
        }

        List<String> noDcm = ignoreUrlsConfig.getNoDcm();
        boolean checkDcm = true;
        for (String noDcmUrl : noDcm) {
            if (pathMatcher.match(noDcmUrl, requestURI)) {
                checkDcm = false;
                break;
            }
        }
        if (checkDcm){
            String tenantStr = request.getHeader(Constant.TENANT_HEADER);
            if(StrUtil.isBlank(tenantStr)){
                request.setAttribute(Constant.AUTH_ERROR_ATTR_NAME, ErrorEnum.UNKNOW_TENANT);
                return false;
            }
            Long tenantId = JSONUtil.toBean(tenantStr, TenantIsolation.class).getTenantId();
            if (Objects.isNull(tenantId)) {
                log.warn("Filter check fail no auth , unknow tenant");
                request.setAttribute(Constant.AUTH_ERROR_ATTR_NAME, ErrorEnum.UNKNOW_TENANT);
                return false;
            }
        }
        String token = request.getHeader(Constant.LOGIN_TOKEN);
        if (StringUtils.isNotEmpty(token)) {
            /*if (token.startsWith(Constant.TOKEN_PREFIX)) {
                token = token.substring(7);
                OAuth2AccessToken auth2AccessToken = resourceServerTokenServices.readAccessToken(token);
                return !Objects.isNull(auth2AccessToken) && !auth2AccessToken.isExpired();
            }else {
                return this.checkToken(request,response);
            }*/
            return this.checkToken(request,response);
        }

        request.setAttribute(Constant.AUTH_ERROR_ATTR_NAME, ErrorEnum.NO_AUTH);
        return false;
    }

    private Boolean checkToken(HttpServletRequest request, HttpServletResponse response){
        String token = request.getHeader(Constant.LOGIN_TOKEN);
        try {
            String realToken = token.replace(Constant.TOKEN_PREFIX, "");
            //表单提交数据
            HttpHeaders headers = new HttpHeaders();
            MediaType type = MediaType.parseMediaType("application/json; charset=UTF-8");
            headers.setContentType(type);
            headers.add("Accept", MediaType.APPLICATION_JSON.toString());
            headers.add(Constant.LOGIN_TOKEN,  realToken);
            headers.add(Constant.EQUIP_ID, request.getHeader(Constant.EQUIP_ID));
            headers.add(Constant.EQUIP_TYPE, request.getHeader(Constant.EQUIP_TYPE));
            headers.add(Constant.REQUEST_SOURCE, request.getHeader(Constant.REQUEST_SOURCE));
            HttpEntity httpEntity = new HttpEntity(headers);
            String pathUrl = baseUrl + checkToken;
            UriComponentsBuilder builder = UriComponentsBuilder.fromHttpUrl(pathUrl);
//            builder.queryParam("token",realToken);
            String result = restHttpTemplate.exchange(builder.build().toString(), HttpMethod.POST, httpEntity, String.class).getBody();
            if(StringUtils.isNotEmpty(result)){
                JSONObject jsonObject = JSONObject.parseObject(result);
                Integer code = jsonObject.getInteger("code");
                if(code == 0 || code == 200){
                    return true;
                }else{
                    log.info("from it source and check fail no auth");
                    request.setAttribute(Constant.AUTH_ERROR_ATTR_NAME, ErrorEnum.NO_AUTH);
                    return false;
                }
            }else{
                log.info("from it source and check fail backend com exception");
                request.setAttribute(Constant.AUTH_ERROR_ATTR_NAME,ErrorEnum.BACKEND_COMEXCEPTION);
                return false;
            }
        }catch (Exception e){
            log.error("from it source backend com exception,errmsg:{}",e.getMessage(),e);
            request.setAttribute(Constant.AUTH_ERROR_ATTR_NAME,ErrorEnum.BACKEND_COMEXCEPTION);
            return false;
        }


    }


}
