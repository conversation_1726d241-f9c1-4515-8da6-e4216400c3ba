package com.nti56.dcm.server.domain;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.bean.copier.CopyOptions;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.nti56.dcm.server.domain.enums.FileBizTypeEnum;
import com.nti56.dcm.server.entity.DeviceEntity;
import com.nti56.dcm.server.entity.DeviceTypeEntity;
import com.nti56.dcm.server.entity.DeviceTypeMonitorEntity;
import com.nti56.dcm.server.entity.FileEntity;
import com.nti56.dcm.server.model.dto.DeviceDto;
import com.nti56.dcm.server.model.dto.DeviceTypeDto;
import com.nti56.dcm.server.model.vo.FileVo;
import com.nti56.nlink.common.base.FieldValue;
import com.nti56.nlink.common.base.UniqueConstraint;
import com.nti56.nlink.common.fetcher.CommonFetcher;
import com.nti56.nlink.common.util.Result;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 类说明: 设备维修工单领域对象
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2024-3-1 14:18:44
 * @since JDK 1.8
 */
@Slf4j
public class DeviceRepairExp {
    
    public static final SerialNumber SERIALNUMBER = new SerialNumber("WXJY");

    private DeviceRepairExp(){}

}
