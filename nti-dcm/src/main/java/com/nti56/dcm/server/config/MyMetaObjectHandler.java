package com.nti56.dcm.server.config;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.handlers.MetaObjectHandler;
import com.nti56.common.tenant.TenantIsolationThreadLocal;
import com.nti56.common.util.JwtUserInfoUtils;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.ibatis.reflection.MetaObject;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.Date;

/**
 * 类说明: <br/>
 *
 * <AUTHOR> <br/>
 * @version 1.0
 * @date 2021/1/11 10:27 <br/>
 * @since JDK1.8
 */

@Slf4j
@Component
public class MyMetaObjectHandler implements MetaObjectHandler {

    @Override
    public void insertFill(MetaObject metaObject) {

        String userJson = JwtUserInfoUtils.userJson();
        if (StringUtils.isNotBlank(userJson)) {
            JSONObject jsonObject = JSON.parseObject(userJson);
            String userInfo = jsonObject.getString("userInfo");
            JSONObject userInfoObject = JSON.parseObject(userInfo);
            Long userId = userInfoObject.getLong("id");
            String userName = userInfoObject.getString("empName");
            this.strictInsertFill(metaObject, "creatorId", Long.class, userId)
            .strictInsertFill(metaObject, "creator", String.class, userName)
            .strictInsertFill(metaObject, "updatorId", Long.class, userId)
            .strictInsertFill(metaObject, "updator", String.class, userName)

            .strictInsertFill(metaObject, "createBy", Long.class, userId)
            .strictInsertFill(metaObject, "createByName", String.class, userName)
            .strictInsertFill(metaObject, "modifyBy", Long.class, userId)
            .strictInsertFill(metaObject, "modifyByName", String.class, userName)
                    ;
        }

        if (metaObject.getValue("createTime") == null) {
            this.strictInsertFill(metaObject, "createTime", Date.class, new Date());
            this.strictInsertFill(metaObject, "createTime", LocalDateTime.class, LocalDateTime.now());
        }
        try {
            if (metaObject.getValue("updateTime") == null) {
                this.strictInsertFill(metaObject, "updateTime", LocalDateTime.class, LocalDateTime.now());
            }
        } catch (Exception e) {
            this.strictInsertFill(metaObject, "modifyTime", Date.class, new Date());
        }
        this.strictInsertFill(metaObject, "clientId", Long.class, TenantIsolationThreadLocal.getTenantId());
        this.strictInsertFill(metaObject, "tenantId", Long.class, TenantIsolationThreadLocal.getTenantId());
        this.strictInsertFill(metaObject, "engineeringId", Long.class, TenantIsolationThreadLocal.getEngineeringId());
        this.strictInsertFill(metaObject, "moduleId", Long.class, TenantIsolationThreadLocal.getModuleId());
        this.strictInsertFill(metaObject, "spaceId", Long.class, TenantIsolationThreadLocal.getSpaceId());
        this.strictInsertFill(metaObject, "deleted", Integer.class, 0);
    }


    @Override
    public void updateFill(MetaObject metaObject) {
        String userJson = JwtUserInfoUtils.userJson();
        if (StringUtils.isNotBlank(userJson)) {
            com.alibaba.fastjson.JSONObject userJsonObject = JSON.parseObject(userJson);
            String userInfo = userJsonObject.getString("userInfo");
            JSONObject userInfoObject = JSON.parseObject(userInfo);
            Long userId = userInfoObject.getLong("id");
            String userName = userInfoObject.getString("empName");
            this.setFieldValByName("updatorId", userId, metaObject)
                    .setFieldValByName("updator", userName, metaObject)

                    .setFieldValByName("modifyBy", userId, metaObject)
                    .setFieldValByName("modifyByName", userName, metaObject)
                    ;
        }
        LocalDateTime now = LocalDateTime.now();
        this.setFieldValByName("updateTime", now, metaObject);
        this.strictInsertFill(metaObject, "modifyTime", Date.class, new Date());
    }
}