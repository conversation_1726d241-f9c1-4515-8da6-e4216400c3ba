package com.nti56.dcm.server.domain;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

import com.nti56.dcm.server.domain.enums.AuthEnum;
import com.nti56.dcm.server.domain.enums.AuthTypeEnum;
import com.nti56.dcm.server.entity.AuthEntity;

import lombok.Getter;

public class UserRoleAuth {

    @Getter
    private Long tenantId;
    @Getter
    private Long userId;
    private List<AuthEntity> authList;

    private UserRoleAuth(){}

    public static UserRoleAuth checkInfo(
        Long userId, 
        Long tenantId,
        List<AuthEntity> authList
    ) {
        UserRoleAuth userRoleAuth = new UserRoleAuth();
        userRoleAuth.tenantId = tenantId;
        userRoleAuth.userId = userId;
        userRoleAuth.authList = authList;
        return userRoleAuth;
    }

    public List<AuthEntity> listAuthMenuByIdType(Integer idType) {
        if(authList == null || authList.size() <= 0){
            return new ArrayList<>();
        }
        if(idType == null){
            return new ArrayList<>();
        }
        List<AuthEntity> collect = authList.stream()
            .filter(t -> {
                return AuthTypeEnum.MENU_AUTH.getValue().equals(t.getAuthType());
            }).filter(t -> {
                return idType.equals(t.getIdType());
            }).collect(Collectors.toList());
        if(collect == null){
            collect = new ArrayList<>();
        }
        return collect;
    }

    public List<AuthEntity> listAuthButtonByIdType(Integer idType) {
        if(authList == null || authList.size() <= 0){
            return new ArrayList<>();
        }
        if(idType == null){
            return new ArrayList<>();
        }
        List<AuthEntity> collect = authList.stream()
            .filter(t -> {
                return AuthTypeEnum.BUTTON_AUTH.getValue().equals(t.getAuthType());
            }).filter(t -> {
                return idType.equals(t.getIdType());
            }).collect(Collectors.toList());
        if(collect == null){
            collect = new ArrayList<>();
        }
        return collect;
    }

    public List<AuthEntity> listAuthSystemByIdType(Integer idType) {
        if(authList == null || authList.size() <= 0){
            return new ArrayList<>();
        }
        if(idType == null){
            return new ArrayList<>();
        }
        List<AuthEntity> collect = authList.stream()
            .filter(t -> {
                return AuthTypeEnum.SYSTEM_AUTH.getValue().equals(t.getAuthType());
            }).filter(t -> {
                return idType.equals(t.getIdType());
            }).collect(Collectors.toList());
        if(collect == null){
            collect = new ArrayList<>();
        }
        return collect;
    }


    public Boolean haveOrderAuth(Integer idType, AuthEnum authEnum) {
        Optional<AuthEntity> first = authList.stream().filter(t -> {
            return idType.equals(t.getIdType())
                    && authEnum.getName().equals(t.getAuthName());
        }).findFirst();
        if(first.isPresent()){
            return true;
        }
        return false;
    }

}
