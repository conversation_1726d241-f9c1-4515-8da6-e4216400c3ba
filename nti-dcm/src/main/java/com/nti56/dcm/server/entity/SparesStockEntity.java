package com.nti56.dcm.server.entity;

import java.math.BigDecimal;

import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import java.io.Serializable;

import com.baomidou.mybatisplus.annotation.Version;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 备件库存表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-05-08
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("spares_stock")
public class SparesStockEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * ID
     */
    private Long id;

    /**
     * 租户id
     */
    @TableField(fill = FieldFill.INSERT)
    private Long tenantId;

    /**
     * 身份类型, 1-供应商，2-客户
     */
    private Integer idType;

    /**
     * 备件id
     */
    private Long sparesId;

    /**
     * 货位id
     */
    private Long sparesLocationId;

    /**
     * 库存量
     */
    private BigDecimal stockQuantity;

    /**
     * 创建人ID
     */
    @TableField(fill = FieldFill.INSERT)
    private Long creatorId;

    /**
     * 创建人
     */
    @TableField(fill = FieldFill.INSERT)
    private String creator;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 更新人ID
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Long updatorId;

    /**
     * 更新人
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private String updator;

    /**
     * 更新时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    /**
     * 工程ID
     */
    @TableField(fill = FieldFill.INSERT)
    private Long engineeringId;

    /**
     * 模块ID
     */
    @TableField(fill = FieldFill.INSERT)
    private Long moduleId;

    /**
     * 空间ID
     */
    @TableField(fill = FieldFill.INSERT)
    private Long spaceId;

    /**
     * 版本号
     */
    @Version
    private Integer version;

    /**
     * 删除
     */
    @TableLogic
    private Integer deleted;


}
