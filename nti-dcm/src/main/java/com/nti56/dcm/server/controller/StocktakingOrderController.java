package com.nti56.dcm.server.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.nti56.nlink.common.dto.TenantIsolation;
import com.nti56.nlink.common.util.BeanUtilsIntensifier;
import com.nti56.nlink.common.util.PageParam;
import com.nti56.common.util.R;
import com.nti56.nlink.common.util.Result;
import com.nti56.nlink.common.util.ServiceCodeEnum;
import com.nti56.dcm.server.service.StocktakingOrderService;
import com.nti56.dcm.server.entity.StocktakingOrderEntity;
import com.nti56.dcm.server.model.dto.StocktakingOrderDto;
import com.nti56.dcm.server.model.dto.StocktakingOrderParam;
import com.nti56.dcm.server.model.dto.StocktakingOrderIdDto;
import com.nti56.dcm.server.model.vo.StocktakingOrderVo;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Optional;
import java.util.function.Function;

import lombok.extern.slf4j.Slf4j;


/**
 * 盘点单表 控制器
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2024-05-07 11:04:06
 * @since JDK 1.8
 */
@RestController
@RequestMapping("/stocktaking-order")
@Slf4j
public class StocktakingOrderController {

    @Autowired
    private StocktakingOrderService stocktakingOrderService;

    /**
     * 新增盘点单
     */
    @PostMapping("/create-stocktaking-order")
    public R<StocktakingOrderEntity> createStocktakingOrder(
        @RequestHeader("dcm_headers") TenantIsolation tenant, 
        @RequestBody StocktakingOrderDto dto
    ){
        Result<StocktakingOrderEntity> result = stocktakingOrderService.createStocktakingOrder(tenant.getTenantId(), dto);
        return R.result(result);
    }

    /**
     * 开始盘点单
     */
    @PostMapping("/start-stocktaking-order")
    public R<Void> startStocktakingOrder(
        @RequestHeader("dcm_headers") TenantIsolation tenant, 
        @RequestBody StocktakingOrderIdDto dto
    ){
        Result<Void> result = stocktakingOrderService.startStocktakingOrder(tenant.getTenantId(), dto);
        return R.result(result);
    }

    /**
     * 获取盘点单分页
     * @param pageParam 分页参数
     */
    @GetMapping("/page")
    public R<Page<StocktakingOrderVo>> page(
        @RequestHeader("dcm_headers") TenantIsolation tenant, 
        PageParam pageParam,
        StocktakingOrderParam params
    ){
        Page<StocktakingOrderVo> page = pageParam.toPage(StocktakingOrderVo.class);
        Result<Page<StocktakingOrderVo>> result = stocktakingOrderService.getPage(
            tenant.getTenantId(), params, page
        );
        return R.result(result);
    }
    
    /**
     * 更新盘点单
     */
    @PutMapping("/save-stocktaking-order")
    public R<Void> saveStocktakingOrder(
        @RequestHeader("dcm_headers") TenantIsolation tenant, 
        @RequestBody StocktakingOrderDto dto
    ){
        Result<Void> result = stocktakingOrderService.saveStocktakingOrder(tenant.getTenantId(), dto);
        return R.result(result);
    }

    /**
     * 删除盘点单
     * @param orderId 盘点单id
     */
    @DeleteMapping("/{orderId}")
    public R<Void> delete(
        @RequestHeader("dcm_headers") TenantIsolation tenant, 
        @PathVariable Long orderId
    ){
        Result<Void> result = stocktakingOrderService.deleteById(tenant.getTenantId(), orderId);
        return R.result(result);
    }


    /**
     * 获取盘点单
     * @param orderId 盘点单id
     */
    @GetMapping("/{orderId}")
    public R<StocktakingOrderDto> get(
        @RequestHeader("dcm_headers") TenantIsolation tenant, 
        @PathVariable Long orderId
    ){
        Result<StocktakingOrderDto> result = stocktakingOrderService.getById(tenant.getTenantId(), orderId);
        return R.result(result);
    }

    /**
     * 完成盘点单
     */
    @PostMapping("/finish-stocktaking-order")
    public R<Void> finishStocktakingOrder(
        @RequestHeader("dcm_headers") TenantIsolation tenant, 
        @RequestBody StocktakingOrderIdDto dto
    ){
        Result<Void> result = stocktakingOrderService.finishStocktakingOrder(tenant.getTenantId(), dto);
        return R.result(result);
    }


    
    // /**
    //  * 获取列表
    //  */
    // @GetMapping("/list")
    // public R<List<StocktakingOrderEntity>> list(@RequestHeader("dcm_headers") TenantIsolation tenant, StocktakingOrderEntity entity){
    //     Result<List<StocktakingOrderEntity>> result = stocktakingOrderService.list(tenant.getTenantId(), entity);
    //     return R.result(result);
    // }

}
