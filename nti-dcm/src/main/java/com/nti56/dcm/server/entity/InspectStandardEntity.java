package com.nti56.dcm.server.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import lombok.Data;

/**
 * <p>
 * 点巡检标准表 实体类
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @Ddate 2024-02-23 15:13:54
 * @since JDK 1.8
 */
@Data
@TableName("inspect_standard")
public class InspectStandardEntity extends BaseEntity{

        /**
        * 身份类型, 1-供应商，2-客户
        */
        private Integer idType;
       
        /**
        * 标准编码
        */
        private String standardNumber;

        /**
        * 标准名称
        */
        private String standardName;

        /**
        * 要求描述
        */
        private String requireDesc;

        /**
        * 适用设备类型
        */
        private String suitDeviceType;

        /**
         * 类型，1-通用，2-专用设备
         */
        private Integer type;

        /**
        * 适用设备类型id
        */
        private Long deviceTypeId;


}
