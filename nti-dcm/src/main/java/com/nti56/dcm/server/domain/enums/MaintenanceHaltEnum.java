package com.nti56.dcm.server.domain.enums;

import lombok.Getter;

public enum MaintenanceHaltEnum {
    NO(0, "no", "不停机"),
    YES(1, "yes", "停机"),
    ;

    @Getter
    private Integer value;

    @Getter
    private String name;

    @Getter
    private String nameDesc;

    MaintenanceHaltEnum(Integer value, String name, String nameDesc) {
        this.value = value;
        this.name = name;
        this.nameDesc = nameDesc;
    }

    public static MaintenanceHaltEnum typeOfValue(Integer value){
        MaintenanceHaltEnum[] values = MaintenanceHaltEnum.values();
        for (MaintenanceHaltEnum v : values) {
            if (v.value.equals(value)) {
                return v;
            }
        }
        return null;
    }

    public static MaintenanceHaltEnum typeOfName(String name){
        MaintenanceHaltEnum[] values = MaintenanceHaltEnum.values();
        for (MaintenanceHaltEnum v : values) {
            if (v.name.equals(name)) {
                return v;
            }
        }
        return null;
    }

    public static MaintenanceHaltEnum typeOfNameDesc(String nameDesc){
        MaintenanceHaltEnum[] values = MaintenanceHaltEnum.values();
        for (MaintenanceHaltEnum v : values) {
            if (v.nameDesc.equals(nameDesc)) {
                return v;
            }
        }
        return null;
    }
}
