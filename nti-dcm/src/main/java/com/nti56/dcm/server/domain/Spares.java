package com.nti56.dcm.server.domain;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.bean.copier.CopyOptions;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.nti56.dcm.server.domain.enums.FileBizTypeEnum;
import com.nti56.dcm.server.entity.DeviceEntity;
import com.nti56.dcm.server.entity.DeviceTypeEntity;
import com.nti56.dcm.server.entity.FileEntity;
import com.nti56.dcm.server.entity.SparesEntity;
import com.nti56.dcm.server.entity.SparesStockEntity;
import com.nti56.dcm.server.entity.SparesTypeEntity;
import com.nti56.dcm.server.entity.WorkGroupUserEntity;
import com.nti56.dcm.server.model.dto.SparesDto;
import com.nti56.dcm.server.model.vo.FileVo;
import com.nti56.dcm.server.model.vo.SparesVo;
import com.nti56.nlink.common.base.UniqueConstraint;
import com.nti56.nlink.common.fetcher.CommonFetcher;
import com.nti56.nlink.common.util.Result;
import lombok.extern.slf4j.Slf4j;

import java.math.BigDecimal;
import java.util.Arrays;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 类说明: 备件领域对象
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2023-11-06 09:45:11
 * @since JDK 1.8
 */
@Slf4j
public class Spares {

    public static final SerialNumber SERIALNUMBER = new SerialNumber("BJ");

    public static final UniqueConstraint sparesNoUniqueConstraint = new UniqueConstraint("spares_no");


    private SparesEntity entity;

    private Long id;

    private Spares() {
    }

    public static Result<Spares> checkCreate(SparesDto dto, CommonFetcher commonFetcher) {

        //必填校验
        if (ObjectUtil.isNull(dto.getSparesTypeId())) {
            return Result.error("备件类型不能为空！");
        }
        if (StrUtil.isBlank(dto.getSparesName())) {
            return Result.error("备件名称不能为空！");
        }
        if (dto.getPurchaseCycleNum()!=null && StrUtil.isBlank(dto.getPurchaseUnit())){
            return Result.error("填写了采购周期，但未选择采购周期单位！");
        }
        if (dto.getChangeCycleNum()!=null && StrUtil.isBlank(dto.getChangeUnit())){
            return Result.error("填写了更换周期，但未选择更换周期单位！");
        }
        SparesEntity entity = new SparesEntity();
        BeanUtil.copyProperties(dto, entity);
        Spares Spares = new Spares();
        Spares.entity = entity;
        return Result.ok(Spares);
    }

    public static Result<SparesVo> getAllInfos(Long id, CommonFetcher commonFetcher) {

        if (Objects.isNull(id)) {
            return Result.error("备件ID为空！");
        }
        SparesEntity entity = commonFetcher.get(id, SparesEntity.class);
        if (Objects.isNull(entity)) {
            return Result.error("备件不存在！");
        }
        SparesVo sparesVo = BeanUtil.copyProperties(entity, SparesVo.class);

        List<FileEntity> allFiles = commonFetcher.list("biz_id", id, FileEntity.class);
        if (CollectionUtil.isNotEmpty(allFiles)) {
            Map<Integer, List<FileEntity>> fileCollect = allFiles.stream().collect(Collectors.groupingBy(FileEntity::getBizType));
            fileCollect.forEach((k, v) -> {
                FileBizTypeEnum fileBizTypeEnum = FileBizTypeEnum.typeOfValue(k);
                switch (fileBizTypeEnum) {
                    case PART_PICTURE:
                        sparesVo.setPictures(BeanUtil.copyToList(v, FileVo.class).stream().sorted(Comparator.comparing(FileVo::getCreateTime).reversed()).collect(Collectors.toList()));
                        break;
                    default:
                        log.warn("备件不支持的文件类型");
                }
            });
        }
        if (StrUtil.isNotBlank(entity.getDeviceTypeIds())) {
            List<String> deviceTypeIdList = Arrays.asList(entity.getDeviceTypeIds().split(","));
            List<DeviceTypeEntity> deviceTypeEntityList = commonFetcher.preloader(DeviceTypeEntity.class).preload("id", deviceTypeIdList).list();
            String deviceTypeName = String.join("、", deviceTypeEntityList.stream().map(i -> i.getTypeName()).collect(Collectors.toList()));
            sparesVo.setDeviceTypeName(deviceTypeName);
        }
        SparesTypeEntity sparesTypeEntity = commonFetcher.get(entity.getSparesTypeId(), SparesTypeEntity.class);
        if (sparesTypeEntity!=null){
            sparesVo.setSparesTypeName(sparesTypeEntity.getTypeName());
        }


        return Result.ok(sparesVo);

    }

    public SparesEntity toEntity(){
        return entity;
    }

    public static Result<Void> checkRemove(Long id, CommonFetcher commonFetcher) {
        SparesEntity entity = commonFetcher.get(id, SparesEntity.class);
        if (entity == null) {
            return Result.error("备件不存在");
        }
        //检查是否存在库存
        List<SparesStockEntity> sparesStockEntities = commonFetcher.list("spares_id", id, SparesStockEntity.class);
        if (CollectionUtil.isNotEmpty(sparesStockEntities) && sparesStockEntities.stream().map(i->i.getStockQuantity()).reduce(BigDecimal.ZERO,BigDecimal::add).compareTo(BigDecimal.ZERO) > 0 ) {
            return Result.error("备件存在库存数据且库存量大于0，无法删除");
        }
        return Result.ok();
    }

    public static Result<Spares> checkInfo(Long deviceId, CommonFetcher commonFetcher) {
        return null;
    }


    public static Result<Spares> checkEdit(SparesDto dto, CommonFetcher commonFetcher) {

        if (Objects.isNull(dto.getId())) {
            return Result.error("备件ID为空！");
        }
        SparesEntity oldEntity = commonFetcher.get(dto.getId(), SparesEntity.class);
        if (Objects.isNull(oldEntity)) {
            return Result.error("备件不存在！");
        }
        BeanUtil.copyProperties(dto, oldEntity, CopyOptions.create().setIgnoreNullValue(false).setIgnoreProperties("tenantId"));

        Spares spares = new Spares();
        spares.entity = oldEntity;
        return Result.ok(spares);
    }

}
