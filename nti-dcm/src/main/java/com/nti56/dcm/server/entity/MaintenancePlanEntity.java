package com.nti56.dcm.server.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.Version;
import java.io.Serializable;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <p>
 * 保养计划表 实体类
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @Ddate 2024-03-01 14:27:47
 * @since JDK 1.8
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@TableName("maintenance_plan")
public class MaintenancePlanEntity implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 保养计划ID
     */
    private Long id;

    /**
     * 计划编号
     */
    private String planNumber;

    /**
     * 计划名称
     */
    private String planName;

    /**
     * 计划说明
     */
    private String planDesc;

    /**
     * 保养模式：1-单次，2-周期，3-多次
     */
    private Integer maintenanceMode;

    /**
     * 单次计划保养时间
     */
    private LocalDateTime singleMaintenanceTime;

    /**
     * 多次计划保养时间，年月日时分秒，逗号分隔
     */
    private String multiMaintenanceTime;

    /**
     * 周期性检查开始时间
     */
    private LocalDate periodMaintenanceBegin;

    /**
     * 周期性检查结束时间
     */
    private LocalDate periodMaintenanceEnd;

    /**
     * 执行周期，1-每日，2-每周，3-每月
     */
    private Integer periodMaintenanceType;

    /**
     * 周几/几号执行，1-7-周一到周日，1-31-1号到31号，多个用逗号隔开
     */
    private String periodMaintenanceDay;

    /**
     * 执行时间
     */
    private LocalTime periodMaintenanceTime;

    /**
     * 设备ID
     */
    private Long deviceId;

    /**
     * 是否停机：0-不停机，1-停机
     */
    private Integer halt;

    /**
     * 保养标准ID(多选 逗号隔开)
     */
    private String maintenanceStandardId;

    /**
     * 委外状态：0-内部，1-委外
     */
    private Integer outsource;

    /**
     * 外委供应商类型：1-租户型，2 数据型
     */
    private Integer outsourceSupplierType;

    /**
     * 外委客户类型：1-租户型，2 数据型
     */
    private Integer outsourceCustomerType;

    /**
     * 委外供应商ID
     */
    private Long supplierId;

    /**
     * 委外供应商名称
     */
    private String supplierName;

    /**
     * 是否结束：0-否，1-是
     */
    private Integer isStop;

    /**
     * 创建人
     */
    @TableField(fill = FieldFill.INSERT)
    private String creator;

    /**
     *创建人ID
     */
    @TableField(fill = FieldFill.INSERT)
    private Long creatorId;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 修改人
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private String updator;

    /**
     *更新人ID
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Long updatorId;

    /**
     * 修改时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    /**
     * 租户ID
     */
    @TableField(fill = FieldFill.INSERT)
    private Long tenantId;

    /**
     * 工程ID
     */
    @TableField(fill = FieldFill.INSERT)
    private Long engineeringId;

    /**
     * 模块ID
     */
    @TableField(fill = FieldFill.INSERT)
    private Long moduleId;

    /**
     * 空间ID
     */
    @TableField(fill = FieldFill.INSERT)
    private Long spaceId;

    /**
     * 版本号
     */
    @Version
    private Integer version;

    /**
     * 删除
     */
    @TableLogic
    private Integer deleted;

    /**
     * 客户ID
     */
    private Long customerId;

    /**
     * 客户名称
     */
    private String customerName;

    /**
     * 创建来源 1-供应商/集成商 2-客户
     */
    private Integer createSource;

    /**
     * 计划等级 1-紧急 2-重要 3-普通 4-其他
     */
    private Integer level;

    /**
     * 工单生成策略 1-提前生成 2-立即生成
     */
    private Integer orderCreateStrategy;

    /**
     * 提前生成数
     */
    private Integer createBeforeNumber;

    /**
     * 提前生成单位，1-天，2-小时
     */
    private Integer createBeforeUnit;

    /**
     * 最近一次工单生成时间
     */
    private LocalDateTime recentOrderCreateTime;

    /**
     * 下次工单生成时间
     */
    private LocalDateTime nextOrderCreateTime;

    /**
     * 状态，1-进行中，2-已结束
     */
    private Integer status;

    /**
     * 身份类型, 1-供应商，2-客户
     */
    private Integer idType;

}
