package com.nti56.dcm.server.controller;


import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.nti56.common.util.R;
import com.nti56.dcm.server.entity.WorkGroupEntity;
import com.nti56.dcm.server.model.dto.DeviceDto;
import com.nti56.dcm.server.model.dto.DeviceExcelTemplateDto;
import com.nti56.dcm.server.model.dto.DeviceImportErrorDto;
import com.nti56.dcm.server.model.dto.SparesDto;
import com.nti56.dcm.server.model.dto.SparesImportErrorDto;
import com.nti56.dcm.server.model.dto.SparesQueryDto;
import com.nti56.dcm.server.model.dto.WorkGroupDto;
import com.nti56.dcm.server.model.vo.SparesVo;
import com.nti56.dcm.server.service.ISparesService;
import com.nti56.nlink.common.dto.TenantIsolation;
import com.nti56.nlink.common.util.Result;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;

import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.UnsupportedEncodingException;
import java.util.List;

/**
 * <p>
 * 备件基础信息表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-05-08
 */
@Slf4j
@RestController
@RequestMapping("/spares")
@Tag(name = "备件台账")
public class SparesController {

    @Autowired
    private ISparesService sparesService;

    @GetMapping("/page")
    @Operation(summary = "备件分页查询")
    public R<Page<SparesVo>> page(@RequestHeader("dcm_headers") TenantIsolation tenantIsolation, SparesQueryDto sparesQueryDto) {
        Page<SparesQueryDto> page = sparesQueryDto.toPage(SparesQueryDto.class);
        Result<Page<SparesVo>> result = sparesService.getPage(sparesQueryDto, page, tenantIsolation);
        return R.result(result);
    }

    @PostMapping("")
    @Operation(summary = "新增备件")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "返回值",
                    content = {@Content(mediaType = MediaType.APPLICATION_JSON_VALUE,
                            schema = @Schema(implementation = SparesDto.class)
                    )})
    })
    public R create(@RequestHeader("dcm_headers") TenantIsolation tenantIsolation,
                             @RequestBody @Validated SparesDto dto) {
        return R.result(sparesService.create(dto, tenantIsolation));
    }


    @PutMapping("/{id}")
    @Operation(summary = "修改备件")
    public R edit(@RequestHeader("dcm_headers") TenantIsolation tenantIsolation,
                           @RequestBody @Validated SparesDto dto) {
        return R.result(sparesService.edit(dto, tenantIsolation));
    }

    @DeleteMapping("/{id}")
    @Operation(summary = "根据id删除备件")
    public R delete(@RequestHeader("dcm_headers") TenantIsolation tenantIsolation,
                             @PathVariable Long id) {
        return R.result(sparesService.deleteById(id, tenantIsolation));
    }

    @GetMapping("/{id}")
    @Operation(summary = "根据id查找备件详情")
    public R<SparesVo> getById(@RequestHeader("dcm_headers") TenantIsolation tenantIsolation, @PathVariable("id") Long id) {
        return R.result(sparesService.getSparesInfo(tenantIsolation, id));
    }

    @PostMapping("/template")
    @Operation(summary = "下载导入模板")
    public void getDeviceTemplate(@RequestHeader("dcm_headers") TenantIsolation tenantIsolation, HttpServletResponse response) throws UnsupportedEncodingException {
        sparesService.downloadTemplate(tenantIsolation,  response);

    }


    @Operation(summary = "导入批量创建备件")
    @PostMapping(value = "/uploadSpares", name = "模板导入创建备件")
    public R<List<SparesImportErrorDto>> uploadDataExcel(@RequestHeader("dcm_headers") TenantIsolation tenantIsolation, @RequestParam("file") MultipartFile file) {
        try {
            return R.result(sparesService.uploadData(file,  tenantIsolation));
        } catch (Exception ex) {
            log.error("导入备件异常，{}", ex);
            return R.error("导入失败！失败原因：" + ex.toString());
        }
    }
}
