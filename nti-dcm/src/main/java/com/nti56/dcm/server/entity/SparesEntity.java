package com.nti56.dcm.server.entity;

import java.math.BigDecimal;

import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import java.io.Serializable;

import com.baomidou.mybatisplus.annotation.Version;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 备件基础信息表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-05-08
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("spares")
public class SparesEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * ID
     */
    private Long id;

    /**
     * 租户id
     */
    @TableField(fill = FieldFill.INSERT)
    private Long tenantId;

    /**
     * 身份类型, 1-供应商，2-客户
     */
    private Integer idType;

    /**
     * 备件类型id
     */
    private Long sparesTypeId;

    /**
     * 备件名称
     */
    private String sparesName;

    /**
     * 备件编码
     */
    private String sparesNo;

    /**
     * 备件简称
     */
    private String sparesForShort;

    /**
     * 供应商
     */
    private String supplier;

    /**
     * 设备类型（多个id拼接逗号）
     */
    private String deviceTypeIds;

    /**
     * 规格
     */
    private String spec;

    /**
     * 单位
     */
    private String unit;

    /**
     * 价格
     */
    private BigDecimal price;

    /**
     * 库存上限
     */
    private BigDecimal stockUpperLimit;

    /**
     * 库存下限
     */
    private BigDecimal stockLowerLimit;

    /**
     * 采购周期
     */
    private Integer purchaseCycleNum;

    /**
     * 采购周期单位，天，月，年
     */
    private String purchaseUnit;

    /**
     * 更换周期
     */
    private Integer changeCycleNum;

    /**
     * 更换周期单位，天，月，年
     */
    private String changeUnit;

    /**
     * 创建人ID
     */
    @TableField(fill = FieldFill.INSERT)
    private Long creatorId;

    /**
     * 创建人
     */
    @TableField(fill = FieldFill.INSERT)
    private String creator;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 更新人ID
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Long updatorId;

    /**
     * 更新人
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private String updator;

    /**
     * 更新时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    /**
     * 工程ID
     */
    @TableField(fill = FieldFill.INSERT)
    private Long engineeringId;

    /**
     * 模块ID
     */
    @TableField(fill = FieldFill.INSERT)
    private Long moduleId;

    /**
     * 空间ID
     */
    @TableField(fill = FieldFill.INSERT)
    private Long spaceId;

    /**
     * 版本号
     */
    @Version
    private Integer version;

    /**
     * 删除
     */
    @TableLogic
    private Integer deleted;


}
