package com.nti56.dcm.server.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.nti56.nlink.common.dto.TenantIsolation;
import com.nti56.nlink.common.util.BeanUtilsIntensifier;
import com.nti56.nlink.common.util.PageParam;
import com.nti56.common.util.R;
import com.nti56.nlink.common.util.Result;
import com.nti56.nlink.common.util.ServiceCodeEnum;
import com.nti56.dcm.server.service.RoleAuthService;
import com.nti56.dcm.server.entity.AuthEntity;
import com.nti56.dcm.server.entity.RoleAuthEntity;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Optional;
import java.util.function.Function;

import lombok.extern.slf4j.Slf4j;


/**
 * 角色权限表 控制器
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2024-02-26 16:54:02
 * @since JDK 1.8
 */
@RestController
@RequestMapping("/roleAuth")
@Slf4j
public class RoleAuthController {

    @Autowired
    private RoleAuthService service;

    /**
     * 按角色获取权限列表
     */
    @GetMapping("/listAuthByRoleId")
    public R<List<AuthEntity>> listAuthByRoleId(
        @RequestHeader("dcm_headers") TenantIsolation tenant,
        @RequestParam Long roleId,
        @RequestParam Integer authType
    ){
        Result<List<AuthEntity>> result = service.listAuthByRoleId(tenant.getTenantId(), tenant.getIdType(), roleId, authType);
        return R.result(result);
    }

    // /**
    //  * 获取列表
    //  */
    // @GetMapping("/list")
    // public R<List<RoleAuthEntity>> list(@RequestHeader("dcm_headers") TenantIsolation tenant, RoleAuthEntity entity){
    //     Result<List<RoleAuthEntity>> result = service.list(tenant.getTenantId(), entity);
    //     return R.result(result);
    // }

    /**
     * 获取对象
     * @param entityId 对象id
     */
    @GetMapping("/{entityId}")
    public R<RoleAuthEntity> get(@RequestHeader("dcm_headers") TenantIsolation tenant, @PathVariable Long entityId){
        Result<RoleAuthEntity> result = service.getById(tenant.getTenantId(), entityId);
        return R.result(result);
    }

    // /**
    //  * 获取分页
    //  * @param pageParam 分页参数
    //  */
    // @GetMapping("/page")
    // public R<Page<RoleAuthEntity>> page(@RequestHeader("dcm_headers") TenantIsolation tenant, PageParam pageParam,RoleAuthEntity entity){
    //     Page<RoleAuthEntity> page = pageParam.toPage(RoleAuthEntity.class);
    //     Result<Page<RoleAuthEntity>> result = service.getPage(tenant.getTenantId(), entity,page);
    //     return R.result(result);
    // }


    // /**
    //  * 创建对象
    //  */
    // @PostMapping("/create")
    // public R<RoleAuthEntity> create(@RequestHeader("dcm_headers") TenantIsolation tenant, @RequestBody RoleAuthEntity entity){
    //     Result<RoleAuthEntity> result = service.save(tenant.getTenantId(), entity);
    //     return R.result(result);
    // }

    // /**
    //  * 更新对象
    //  */
    // @PutMapping("/update")
    // public R<Void> update(@RequestHeader("dcm_headers") TenantIsolation tenant, @RequestBody RoleAuthEntity entity){
    //     if (BeanUtilsIntensifier.checkBeanAndProperties(entity, RoleAuthEntity::getId)) {
    //         return R.error(ServiceCodeEnum.CODE_PARAM_ERROR.getCode());
    //     }
    //     Result<Void> result = service.update(tenant.getTenantId(), entity);
    //     return R.result(result);
    // }

    // /**
    //  * 删除对象
    //  * @param entityId 对象id
    //  */
    // @DeleteMapping("/{entityId}")
    // public R<Void> delete(@RequestHeader("dcm_headers") TenantIsolation tenant, @PathVariable Long entityId){
    //     Result<Void> result = service.deleteById(tenant.getTenantId(), entityId);
    //     return R.result(result);
    // }

    
}
