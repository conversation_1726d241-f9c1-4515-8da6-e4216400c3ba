package com.nti56.dcm.server.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.time.LocalDateTime;
import lombok.Data;

/**
 * <p>
 * 点巡检工单进度表 实体类
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @Ddate 2024-02-23 15:13:54
 * @since JDK 1.8
 */
@Data
@TableName("inspect_order_progress")
public class InspectOrderProgressEntity extends BaseEntity{
    /**
    * 所属工单id
    */
    private Long inspectOrderId;

    /**
    * 进度类型，1-生成工单，2-派工，3-接收工单，4-执行，5-验收，6-发起，7-供应商派工，8-供应商接收工单，9-供应商执行，10-撤销
    */
    private Integer progressType;

    /**
    * 进度执行人id
    */
    private Long userId;

    /**
    * 进度执行人姓名
    */
    private String userName;

    /**
    * 进度时间
    */
    private LocalDateTime progressTime;

    // /**
    // * 检查开始时间
    // */
    // private LocalDateTime inspectBegin;

    // /**
    // * 检查束时间
    // */
    // private LocalDateTime inspectEnd;

    // /**
    // * 检查耗时，单位分钟
    // */
    // private Integer inspectCostTime;

    /**
    * 验收结果，1-通过，0-不通过
    */
    private Integer acceptResult;

    /**
    * 验收说明
    */
    private String acceptDesc;

    // /**
    //  * 检查耗时，文本
    //  * @return
    //  */
    // public String getInspectCostTimeStr(){
    //     Integer t = getInspectCostTime();
    //     if(t == null){
    //         return "";
    //     }
    //     if(t < 60){
    //         return t + "分";
    //     }else{
    //         Integer hour = t/60;
    //         Integer minute = t%60;
    //         String s = hour + "小时";
    //         if(minute >= 0){
    //             s += minute + "分";
    //         }
    //         return s;
    //     }
    // }
}
