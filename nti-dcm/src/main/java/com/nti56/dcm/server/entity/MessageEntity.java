package com.nti56.dcm.server.entity;

import com.alibaba.fastjson.annotation.JSONField;
import com.alibaba.fastjson.serializer.ToStringSerializer;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import java.io.Serializable;

import com.baomidou.mybatisplus.annotation.Version;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 站内信表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-21
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("message")
public class MessageEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    private Long id;

    /**
     * 租户id
     */
    @JSONField(serializeUsing = ToStringSerializer.class)
    @TableField(fill = FieldFill.INSERT)
    private Long tenantId;

    /**
     * 消息类型，1-设备故障，2-设备离线，3-设备健康度较低，4-设备超时未保养，5-工单超时，6-备件库存警告，7-收到新工单
     */
    private Integer messageType;

    /**
     * 标题
     */
    private String title;

    /**
     * 内容
     */
    private String content;
    /**
     * 弹窗展示内容
     */
    private String showContent;

    /**
     * 工单类型，1-维修，2-保养，3-巡检
     */
    private Integer orderType;

    /**
     * 设备ID
     */
    @JSONField(serializeUsing = ToStringSerializer.class)
    private Long deviceId;

    /**
     * 工单编号
     */
    private String orderNo;

    /**
     * 工单状态，0-内部，1-外委
     */
    private Integer orderOutsourceStatus;

    /**
     * 创建人ID
     */
    @TableField(fill = FieldFill.INSERT)
    private Long creatorId;
    /**
     * 更新人ID
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Long updatorId;
    /**
     * 创建人
     */
    @TableField(fill = FieldFill.INSERT)
    private String creator;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 修改人
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private String updator;

    /**
     * 修改时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    /**
     * 工程ID
     */
    @TableField(fill = FieldFill.INSERT)
    private Long engineeringId;

    /**
     * 模块ID
     */
    @TableField(fill = FieldFill.INSERT)
    private Long moduleId;

    /**
     * 空间ID
     */
    @TableField(fill = FieldFill.INSERT)
    private Long spaceId;

    /**
     * 版本号
     */
    @Version
    private Integer version;

    /**
     * 删除
     */
    @TableLogic
    private Integer deleted;

    /**
     * 所属身份,1-供应商，2-客户
     */
    private Integer idType;

    /**
     * 客户ID
     */
    @JSONField(serializeUsing = ToStringSerializer.class)
    private Long customerId;

    private Integer warrantyExpireState;

    private Integer maintenanceExpireState;

    private Integer planScrapState;

    /**
     * 备件编号
     */
    private String itemCode;

}
