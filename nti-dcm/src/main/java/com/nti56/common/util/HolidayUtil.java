package com.nti56.common.util;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.ObjUtil;
import cn.hutool.http.HttpUtil;
import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.time.LocalDate;
import java.time.temporal.ChronoUnit;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Slf4j
@Component
public class HolidayUtil {

    public static List<String> holidayList;
    public static List<String> fixWorkList;

    public static Boolean isHoliday(String date) {
        // 如果是补班直接返回fals】e
        if (CollUtil.isNotEmpty(holidayList) && fixWorkList.contains(date)){
            return false;
        }
        Boolean holidayFlag = false;
        if (CollUtil.isNotEmpty(holidayList)) {
            holidayFlag = holidayList.contains(date);
        }
        // 节假日或周末返回true
        return holidayFlag || DateUtil.isWeekend(DateUtil.parseDate(date));
    }

    public static void main(String[] args) {

        String date = "2024-06-10";
        Boolean holiday = isHoliday(date);
        System.out.println("日期:【" + date + "】 是否为节假日:" + holiday);
    }
}
