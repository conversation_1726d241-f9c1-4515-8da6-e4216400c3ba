package com.nti56.dcm.server.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.nti56.dcm.server.entity.MaintenanceItemEntity;
import com.nti56.dcm.server.service.MaintenanceItemService;
import com.nti56.nlink.common.util.*;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.function.Function;


/**
 * <p>
 * 保养明细表 控制器
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @Ddate 2024-03-01 16:28:19
 * @since JDK 1.8
 */
@RestController
@RequestMapping("/")
@Tag(name = "保养明细表模块")
public class MaintenanceItemController {

    @Autowired
    MaintenanceItemService service;

    @GetMapping("maintenance_item/page")
    @Operation(summary = "获取分页")
    public R page(PageParam pageParam,MaintenanceItemEntity entity){
        Page<MaintenanceItemEntity> page = pageParam.toPage(MaintenanceItemEntity.class);
        Result<Page<MaintenanceItemEntity>> result = service.getPage(entity,page);
        return R.result(result);
    }

    @GetMapping("maintenance_item/list")
    @Operation(summary = "获取列表" )
    public R list(MaintenanceItemEntity entity){
        Result<List<MaintenanceItemEntity>> result = service.list(entity);
        return R.result(result);
    }

    @PutMapping("maintenance_item")
    @Operation(summary = "更新")
    public R update(@RequestBody MaintenanceItemEntity entity){
        if (BeanUtilsIntensifier.checkBeanAndProperties(entity, MaintenanceItemEntity::getId)) {
            return R.error(ServiceCodeEnum.CODE_PARAM_ERROR);
        }
        Result<Void> result = service.update(entity);
        return R.result(result);
    }

    @DeleteMapping("maintenance_item/{entityId}")
    @Operation(summary = "删除对象")
    public R delete(@PathVariable Long entityId){
        Result<Void> result = service.deleteById(entityId);
        return R.result(result);
    }

    private R checkParamAndDoSomething(MaintenanceItemEntity entity, Function<MaintenanceItemEntity,Result> func){
        //TODO: do check params
        //if (BeanUtilsIntensifier.checkBeanAndProperties(entity, MaintenanceItemEntity::getName)) {
        //    return R.error(ServiceCodeEnum.CODE_PARAM_ERROR);
        //}
        Result result = func.apply(entity);
        return R.result(result);
    }
    
}
