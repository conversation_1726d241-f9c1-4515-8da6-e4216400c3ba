package com.nti56.dcm.server.controller;


import com.nti56.common.util.R;
import com.nti56.dcm.server.entity.DeviceBomEntity;
import com.nti56.dcm.server.model.dto.DeviceBomDto;
import com.nti56.dcm.server.model.dto.DeviceBomQueryTreeDto;
import com.nti56.dcm.server.model.dto.DeviceBomTreeDto;
import com.nti56.dcm.server.service.IDeviceBomService;
import com.nti56.nlink.common.dto.TenantIsolation;
import com.nti56.nlink.common.util.Result;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <p>
 * 设备bom表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-09
 */
@RestController
@RequestMapping("/deviceBom")
@Tag(name = "设备Bom")
public class DeviceBomController {

    @Autowired
    private IDeviceBomService deviceBomService;

    @GetMapping("/getTree")
    @Operation(summary = "获取设备Bom树形数据")
    public R<List<DeviceBomTreeDto>> getTree(@RequestHeader("dcm_headers") TenantIsolation tenantIsolation, DeviceBomQueryTreeDto dto) {
        Result<List<DeviceBomTreeDto>> result = deviceBomService.getTree(tenantIsolation, dto);
        return R.result(result);
    }

    @PostMapping("")
    @Operation(summary = "新增设备Bom")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "返回值",
                    content = {@Content(mediaType = MediaType.APPLICATION_JSON_VALUE,
                            schema = @Schema(implementation = DeviceBomEntity.class)
                    )})
    })
    public R createDeviceBom(@RequestHeader("dcm_headers") TenantIsolation tenantIsolation,
                             @RequestBody @Validated DeviceBomDto dto) {
        return R.result(deviceBomService.create(dto, tenantIsolation));
    }

    @PutMapping("/{id}")
    @Operation(summary = "修改班组设备Bom")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "返回值",
                    content = {@Content(mediaType = MediaType.APPLICATION_JSON_VALUE,
                            schema = @Schema(implementation = DeviceBomEntity.class)
                    )})
    })
    public R updateDeviceBom(@RequestHeader("dcm_headers") TenantIsolation tenantIsolation,
                             @RequestBody @Validated DeviceBomDto dto) {
        return R.result(deviceBomService.edit(dto, tenantIsolation));
    }

    @DeleteMapping("/{id}")
    @Operation(summary = "根据id删除班组设备Bom")
    public R deleteDeviceBom(@RequestHeader("dcm_headers") TenantIsolation tenantIsolation,
                             @PathVariable Long id) {
        return R.result(deviceBomService.delete(id, tenantIsolation));
    }

}
