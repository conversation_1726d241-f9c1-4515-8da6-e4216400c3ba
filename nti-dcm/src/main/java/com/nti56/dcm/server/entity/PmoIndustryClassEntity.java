package com.nti56.dcm.server.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import java.io.Serializable;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 行业分类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-16
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("pmo_industry_class")
public class PmoIndustryClassEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 租户id
     */
    private Long clientId;

    /**
     * 分类名称
     */
    private String className;

    /**
     * 分类编码
     */
    private String classCode;

    /**
     * 排序
     */
    private Integer sortNo;

    /**
     * 上级ID
     */
    private Long pId;

    /**
     * 类型; 0:目录  1:内容
     */
    private Integer type;

    /**
     * 0启用;1禁用
     */
    private Integer status;

    /**
     * 删除状态; 0:未删除，1：已删除
     */
    private Integer deleted;

    /**
     * 创建人
     */
    private String createBy;

    /**
     * 创建人姓名
     */
    private String createByName;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 修改人
     */
    private String modifyBy;

    /**
     * 创建人姓名
     */
    private String modifyByName;

    /**
     * 更新时间
     */
    private LocalDateTime modifyTime;

    /**
     * 版本号
     */
    private Long version;


}
