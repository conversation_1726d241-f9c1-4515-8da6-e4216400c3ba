package com.nti56.dcm.server.domain.enums;

import lombok.Getter;

public enum TimeoutConfigTypeEnum {
    REPORT(1, "report", "报修"),
    INSIDE(2, "inside", "内部"),
    OUTSIDE(3, "outside", "外委"),
    FLOW_INSIDE(4, "flow_inside", "内部工单流转"),
    FLOW_OUTSIDE(5, "flow_outside", "外委工单流转"),
    ;

    @Getter
    private Integer value;

    @Getter
    private String name;

    @Getter
    private String nameDesc;

    TimeoutConfigTypeEnum(Integer value, String name, String nameDesc) {
        this.value = value;
        this.name = name;
        this.nameDesc = nameDesc;
    }

    public static TimeoutConfigTypeEnum typeOfValue(Integer value){
        TimeoutConfigTypeEnum[] values = TimeoutConfigTypeEnum.values();
        for (TimeoutConfigTypeEnum v : values) {
            if (v.value.equals(value)) {
                return v;
            }
        }
        return null;
    }

    public static TimeoutConfigTypeEnum typeOfName(String name){
        TimeoutConfigTypeEnum[] values = TimeoutConfigTypeEnum.values();
        for (TimeoutConfigTypeEnum v : values) {
            if (v.name.equals(name)) {
                return v;
            }
        }
        return null;
    }

    public static TimeoutConfigTypeEnum typeOfNameDesc(String nameDesc){
        TimeoutConfigTypeEnum[] values = TimeoutConfigTypeEnum.values();
        for (TimeoutConfigTypeEnum v : values) {
            if (v.nameDesc.equals(nameDesc)) {
                return v;
            }
        }
        return null;
    }
}
