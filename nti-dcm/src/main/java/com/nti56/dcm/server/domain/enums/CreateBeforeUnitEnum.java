package com.nti56.dcm.server.domain.enums;

import lombok.Getter;

public enum CreateBeforeUnitEnum {
    DAY(1, "DAY", "天"),
    HOUR(2, "HOUR", "小时"),
    ;

    @Getter
    private Integer value;

    @Getter
    private String name;

    @Getter
    private String nameDesc;

    CreateBeforeUnitEnum(Integer value, String name, String nameDesc) {
        this.value = value;
        this.name = name;
        this.nameDesc = nameDesc;
    }

    public static CreateBeforeUnitEnum typeOfValue(Integer value){
        CreateBeforeUnitEnum[] values = CreateBeforeUnitEnum.values();
        for (CreateBeforeUnitEnum v : values) {
            if (v.value.equals(value)) {
                return v;
            }
        }
        return null;
    }

    public static CreateBeforeUnitEnum typeOfName(String name){
        CreateBeforeUnitEnum[] values = CreateBeforeUnitEnum.values();
        for (CreateBeforeUnitEnum v : values) {
            if (v.name.equals(name)) {
                return v;
            }
        }
        return null;
    }

    public static CreateBeforeUnitEnum typeOfNameDesc(String nameDesc){
        CreateBeforeUnitEnum[] values = CreateBeforeUnitEnum.values();
        for (CreateBeforeUnitEnum v : values) {
            if (v.nameDesc.equals(nameDesc)) {
                return v;
            }
        }
        return null;
    }
}
