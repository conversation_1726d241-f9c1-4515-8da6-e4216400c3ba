package com.nti56.dcm.server.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import lombok.Data;

/**
 * <p>
 * 点巡检计划设备标准表 实体类
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @Ddate 2024-03-05 09:33:21
 * @since JDK 1.8
 */
@Data
@TableName("inspect_plan_device_standard")
public class InspectPlanDeviceStandardEntity extends BaseEntity{
        /**
        * 点巡检计划设备id
        */
        private Long inspectPlanDeviceId;

        /**
        * 点巡检标准id
        */
        private Long inspectStandardId;



}
