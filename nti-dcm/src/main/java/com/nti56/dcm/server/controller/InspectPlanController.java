package com.nti56.dcm.server.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.nti56.nlink.common.dto.TenantIsolation;
import com.nti56.nlink.common.util.PageParam;
import com.nti56.common.util.R;
import com.nti56.nlink.common.util.Result;
import com.nti56.dcm.server.service.InspectPlanService;
import com.nti56.dcm.server.service.UserCenterService;
import com.nti56.dcm.server.entity.InspectPlanEntity;
import com.nti56.dcm.server.model.dto.InspectPlanDto;
import com.nti56.dcm.server.model.dto.InspectPlanParams;
import com.nti56.dcm.server.model.vo.InspectPlanVo;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import lombok.extern.slf4j.Slf4j;


/**
 * 点巡检计划表 控制器
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2024-02-26 16:54:02
 * @since JDK 1.8
 */
@RestController
@RequestMapping("/inspectPlan")
@Slf4j
public class InspectPlanController {

    @Autowired
    private InspectPlanService service;

    @Autowired
    private UserCenterService userCenterService;

    /**
     * 创建点巡检计划
     */
    @PostMapping("/create")
    public R<InspectPlanEntity> create(@RequestHeader("dcm_headers") TenantIsolation tenant, @RequestBody InspectPlanDto dto){
        Result<String> tenantNameResult = userCenterService.getTenantNameById(tenant.getTenantId());
        if(!tenantNameResult.getSignal()){
            return R.error(tenantNameResult.getMessage());
        }
        String tenantName = tenantNameResult.getResult();

        Result<InspectPlanEntity> result = service.create(tenant.getTenantId(), tenant.getIdType(), tenantName, dto);
        return R.result(result);
    }

    /**
     * 获取点巡检计划分页
     * @param pageParam 分页参数
     */
    @GetMapping("/page")
    public R<Page<InspectPlanVo>> page(
        @RequestHeader("dcm_headers") TenantIsolation tenant, 
        PageParam pageParam,
        InspectPlanParams params
    ){
        Page<InspectPlanEntity> page = pageParam.toPage(InspectPlanEntity.class);
        Result<Page<InspectPlanVo>> result = service.getPage(tenant.getTenantId(), tenant.getIdType(), params, page);
        return R.result(result);
    }

    /**
     * 停止点巡检计划
     * @param inspectPlanId 点巡检计划id
     */
    @PutMapping("stopPlan")
    public R<Void> stopPlan(@RequestHeader("dcm_headers") TenantIsolation tenant, @RequestParam("inspectPlanId") Long inspectPlanId){

        Result<Void> result = service.stopPlan(tenant.getTenantId(), tenant.getIdType(), inspectPlanId);
        return R.result(result);
    }

    /**
     * 获取点巡检计划详情
     * @param entityId 对象id
     */
    @GetMapping("/getPlanDetailById/{inspectPlanId}")
    public R<InspectPlanDto> get(@RequestHeader("dcm_headers") TenantIsolation tenant, @PathVariable Long inspectPlanId){
        Result<InspectPlanDto> result = service.getPlanDetailById(tenant.getTenantId(), inspectPlanId);
        return R.result(result);
    }

    // /**
    //  * 获取列表
    //  */
    // @GetMapping("/list")
    // public R<List<InspectPlanEntity>> list(@RequestHeader("dcm_headers") TenantIsolation tenant, InspectPlanEntity entity){
    //     Result<List<InspectPlanEntity>> result = service.list(tenant.getTenantId(), entity);
    //     return R.result(result);
    // }

    // /**
    //  * 更新对象
    //  */
    // @PutMapping("/update")
    // public R<Void> update(@RequestHeader("dcm_headers") TenantIsolation tenant, @RequestBody InspectPlanEntity entity){
    //     if (BeanUtilsIntensifier.checkBeanAndProperties(entity, InspectPlanEntity::getId)) {
    //         return R.error(ServiceCodeEnum.CODE_PARAM_ERROR.getCode());
    //     }
    //     Result<Void> result = service.update(tenant.getTenantId(), entity);
    //     return R.result(result);
    // }

    // /**
    //  * 删除对象
    //  * @param entityId 对象id
    //  */
    // @DeleteMapping("/{entityId}")
    // public R<Void> delete(@RequestHeader("dcm_headers") TenantIsolation tenant, @PathVariable Long entityId){
    //     Result<Void> result = service.deleteById(tenant.getTenantId(), entityId);
    //     return R.result(result);
    // }

    
}
