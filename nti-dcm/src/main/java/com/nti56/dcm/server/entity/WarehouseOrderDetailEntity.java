package com.nti56.dcm.server.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.nti56.dcm.server.domain.enums.DeviceMaintenanceOperationTypeEnum;
import com.nti56.dcm.server.domain.enums.WarehouseOperationTypeEnum;
import com.nti56.dcm.server.domain.enums.WarehouseOrderTypeEnum;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 
 * @TableName warehouse_order_detail
 */
@TableName(value ="warehouse_order_detail")
@Data
@AllArgsConstructor
@NoArgsConstructor
public class WarehouseOrderDetailEntity implements Serializable {
    /**
     * 位置ID
     */
    @TableId
    private Long id;
    /**
     * 所属身份 1-供应商 2-客户
     */
    private Integer idType;
    /**
     * 操作类型
     * @see WarehouseOperationTypeEnum
     */
    private Integer operationType;

    /**
     * 出入库单号
     */
    private String serialNumber;

    /**
     * 出入库单ID
     */
    private Long warehouseOrderId;

    /**
     * 出库类型
     * @see WarehouseOrderTypeEnum
     */
    private Long outboundType;
    /**
     * 仓库ID
     */
    private Long sparesWarehouseId;

    /**
     * 区域ID
     */
    private Long sparesAreaId;
    /**
     * 货位ID
     */
    private Long sparesLocationId;
    /**
     * 是否完成
     */
    private Boolean isDone;
    /**
     * 是否借出
     */
    private Boolean isLend;
    /**
     * 数量
     */
    private BigDecimal quantity;

    /**
     * 备件ID
     */
    private Long sparesId;


    /**
     * 创建人
     */
    @TableField(fill = FieldFill.INSERT)
    private String creator;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 修改人
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private String updator;

    /**
     * 修改时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    /**
     * 租户ID
     */
    @TableField(fill = FieldFill.INSERT)
    private Long tenantId;

    /**
     * 工程ID
     */
    @TableField(fill = FieldFill.INSERT)
    private Long engineeringId;

    /**
     * 模块ID
     */
    @TableField(fill = FieldFill.INSERT)
    private Long moduleId;

    /**
     * 空间ID
     */
    @TableField(fill = FieldFill.INSERT)
    private Long spaceId;

    /**
     * 版本号
     */
    @Version
    private Integer version;

    /**
     * 删除
     */
    @TableLogic
    private Integer deleted;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;

}