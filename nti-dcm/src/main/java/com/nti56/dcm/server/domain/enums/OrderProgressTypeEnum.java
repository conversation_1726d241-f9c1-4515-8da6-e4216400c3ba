package com.nti56.dcm.server.domain.enums;

import lombok.Getter;

public enum OrderProgressTypeEnum {
    CREATE(1, "CREATE", "生成工单"),
    DISPATCH(2, "DISPATCH", "派工"),
    RECEIVE(3, "RECEIVE", "接收工单"),
    EXECUTE(4, "EXECUTE", "执行"),
    ACCEPT(5, "ACCEPT", "验收"),
    BEGIN(6, "BEGIN", "发起"),
    SUPPLIER_DISPATCH(7, "SUPPLIER_DISPATCH", "供应商派工"),
    SUPPLIER_RECEIVE(8, "SUPPLIER_RECEIVE", "供应商接收工单"),
    SUPPLIER_EXECUTE(9, "SUPPLIER_EXECUTE", "供应商执行"),
    CANCLE(10, "CANCLE", "撤销"),
    FORWARD(11, "FORWARD", "转交"),
    WITHDRAW(12, "WITHD<PERSON><PERSON>", "驳回"),
    ;

    @Getter
    private Integer value;

    @Getter
    private String name;

    @Getter
    private String nameDesc;

    OrderProgressTypeEnum(Integer value, String name, String nameDesc) {
        this.value = value;
        this.name = name;
        this.nameDesc = nameDesc;
    }

    public static OrderProgressTypeEnum typeOfValue(Integer value){
        OrderProgressTypeEnum[] values = OrderProgressTypeEnum.values();
        for (OrderProgressTypeEnum v : values) {
            if (v.value.equals(value)) {
                return v;
            }
        }
        return null;
    }

    public static OrderProgressTypeEnum typeOfName(String name){
        OrderProgressTypeEnum[] values = OrderProgressTypeEnum.values();
        for (OrderProgressTypeEnum v : values) {
            if (v.name.equals(name)) {
                return v;
            }
        }
        return null;
    }

    public static OrderProgressTypeEnum typeOfNameDesc(String nameDesc){
        OrderProgressTypeEnum[] values = OrderProgressTypeEnum.values();
        for (OrderProgressTypeEnum v : values) {
            if (v.nameDesc.equals(nameDesc)) {
                return v;
            }
        }
        return null;
    }
}
