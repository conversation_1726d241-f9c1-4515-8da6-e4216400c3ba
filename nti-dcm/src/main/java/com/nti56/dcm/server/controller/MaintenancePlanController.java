package com.nti56.dcm.server.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.nti56.dcm.server.model.dto.MaintenancePlanDto;
import com.nti56.dcm.server.model.dto.QueryMaintenancePlanDto;
import com.nti56.dcm.server.model.vo.MaintenancePlanVo;
import com.nti56.dcm.server.service.MaintenancePlanService;
import com.nti56.nlink.common.dto.TenantIsolation;
import com.nti56.nlink.common.util.*;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

/**
 * <p>
 * 保养计划表
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @Ddate 2024-03-01 14:27:47
 * @since JDK 1.8
 */
@Slf4j
@RestController
@RequestMapping("maintenancePlan")
@Tag(name = "保养计划表模块")
public class MaintenancePlanController {

    @Autowired
    private MaintenancePlanService maintenancePlanService;

    @PostMapping("")
    @Operation(summary = "创建保养计划")
    public R create(@RequestHeader("dcm_headers") TenantIsolation tenantIsolation,
                    @Validated @RequestBody MaintenancePlanDto dto){
        return R.result(maintenancePlanService.save(tenantIsolation, dto));
    }

    @GetMapping("/page")
    @Operation(summary = "获取分页")
    public R page(@RequestHeader("dcm_headers") TenantIsolation tenantIsolation,
                  QueryMaintenancePlanDto dto){
        Page<MaintenancePlanVo> page = dto.toPage(MaintenancePlanVo.class);
        dto.setTenantId(tenantIsolation.getTenantId());
        dto.setIdType(tenantIsolation.getIdType());
        Result<Page<MaintenancePlanVo>> result = maintenancePlanService.getPage(dto, page);
        return R.result(result);
    }

    @PutMapping("stopPlan/{id}")
    @Operation(summary = "结束保养计划")
    public R delete(@RequestHeader("dcm_headers") TenantIsolation tenantIsolation, @PathVariable Long id){
        return R.result(maintenancePlanService.stopPlan(tenantIsolation, id));
    }

    @GetMapping("/{id}")
    @Operation(summary = "获取计划详情")
    public R get(@RequestHeader("dcm_headers") TenantIsolation tenantIsolation, @PathVariable Long id){
        return R.result(maintenancePlanService.getPlanDetailById(tenantIsolation, id));
    }

}
