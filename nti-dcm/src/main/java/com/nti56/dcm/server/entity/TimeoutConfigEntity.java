package com.nti56.dcm.server.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 超时配置表 实体类
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @Ddate 2024-06-24 17:21:42
 * @since JDK 1.8
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@TableName("timeout_config")
public class TimeoutConfigEntity implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
    * id
    */
    private Long id;

    /**
     * 身份，1-供应商，2-客户
     */
    private Integer idType;

    /**
    * 租户id
    */
    @TableField(fill = FieldFill.INSERT)
    private Long tenantId;

    /**
    * 工单类型, 1-维修，2-保养，3-巡检
    */
    private Integer orderType;

    /**
    * 配置类型, 1-报修，2-内部，3-外委
    */
    private Integer configType;

    /**
    * 超时数值
    */
    private Integer timeoutNumber;

    /**
    * 超时单位，1-天，2-小时
    */
    private Integer timeoutUnit;

    /**
     * 超时秒数
     */
    private Integer timeoutSecond;

    /**
    * 是否启用, 0-禁用，1-启用
    */
    private Integer status;

    /**
    * 版本号
    */
    @Version
    private Integer version;

    /**
    * 删除
    */
    @TableLogic
    private Integer deleted;

    /**
    * 创建人ID
    */
    private Long creatorId;

    /**
    * 创建人
    */
    @TableField(fill = FieldFill.INSERT)
    private String creator;

    /**
    * 创建时间
    */
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
    * 更新人ID
    */
    private Long updatorId;

    /**
    * 更新人
    */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private String updator;

    /**
    * 更新时间
    */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    /**
    * 工程ID
    */
    @TableField(fill = FieldFill.INSERT)
    private Long engineeringId;

    /**
    * 模块ID
    */
    @TableField(fill = FieldFill.INSERT)
    private Long moduleId;

    /**
    * 空间ID
    */
    @TableField(fill = FieldFill.INSERT)
    private Long spaceId;



}
