package com.nti56.dcm.server.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import lombok.Data;

/**
 * <p>
 * 点巡检结果项目表 实体类
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @Ddate 2024-02-23 15:13:54
 * @since JDK 1.8
 */
@Data
@TableName("inspect_result_item")
public class InspectResultItemEntity extends BaseEntity{
        /**
        * 所属检查结果id
        */
        private Long inspectResultId;

        /**
        * 检查项目id
        */
        private Long inspectStandardItemId;

        /**
        * 检查值，选项名（多选的话逗号分隔）、数值、文本
        */
        private String resultValue;

        /**
        * 是否异常，1-异常，0-正常
        */
        private Integer isError;
        
}
