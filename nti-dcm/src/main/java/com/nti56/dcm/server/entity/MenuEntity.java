package com.nti56.dcm.server.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import lombok.Data;

/**
 * <p>
 * 菜单表 实体类
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @Ddate 2024-02-27 15:58:37
 * @since JDK 1.8
 */
@Data
@TableName("menu")
public class MenuEntity extends BaseEntity{
        /**
        * 身份类型, 1-供应商，2-客户
        */
        private Integer idType;

        /**
        * 菜单名
        */
        private String menuName;

        /**
        * 菜单简短描述
        */
        private String menuShort;

        /**
        * 菜单中文描述
        */
        private String menuDesc;

        /**
        * 父级菜单id
        */
        private Long parentId;

        /**
        * 是否叶子菜单
        */
        private Integer isLeaf;



}
