package com.nti56.dcm.server.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * <p>
 * 物联设备接入进度表 实体类
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @Ddate 2024-12-16 15:11:19
 * @since JDK 1.8
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@TableName("device_connect_plan")
public class DeviceConnectPlanEntity implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
    * id
    */
    private Long id;

    /**
    * 租户id
    */
    @TableField(fill = FieldFill.INSERT)
    private Long tenantId;

    /**
    * 项目id
    */
    private Long projectId;

    /**
    * 项目名称
    */
    private String projectName;

    /**
    * 客户id
    */
    private Long customerId;

    /**
    * 客户名称
    */
    private String customerName;

    /**
    * 实施负责人
    */
    private String implementationManager;

    /**
    * 计划开始时间
    */
    private LocalDate planDate;

    /**
    * 预计完成时间
    */
    private LocalDate expectFinishDate;

    /**
    * 计划接入数
    */
    private Integer planCount;

    /**
    * 已接入数
    */
    private Integer finishCount;

    /**
    * 接入进度，1-接入筹备中，2-接入中，3-接入完成：全部设备接入，4-接入完成：部分设备接入，5-接入完成：无设备接入
    */
    private Integer status;

    /**
    * 版本号
    */
    @Version
    private Integer version;

    /**
    * 删除
    */
    @TableLogic
    private Integer deleted;

    /**
    * 创建人ID
    */
    private Long creatorId;

    /**
    * 创建人
    */
    @TableField(fill = FieldFill.INSERT)
    private String creator;

    /**
    * 创建时间
    */
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
    * 更新人ID
    */
    private Long updatorId;

    /**
    * 更新人
    */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private String updator;

    /**
    * 更新时间
    */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    /**
    * 工程ID
    */
    @TableField(fill = FieldFill.INSERT)
    private Long engineeringId;

    /**
    * 模块ID
    */
    @TableField(fill = FieldFill.INSERT)
    private Long moduleId;

    /**
    * 空间ID
    */
    @TableField(fill = FieldFill.INSERT)
    private Long spaceId;



}
