package com.nti56.dcm.server.domain.enums;

import lombok.Getter;

public enum InspectResultStatusEnum {
    INSPECTED(1, "INSPECTED", "已检查"),
    NOT_INSPECT(0, "NOT_INSPECT", "未检查"),
    ;

    @Getter
    private Integer value;

    @Getter
    private String name;

    @Getter
    private String nameDesc;

    InspectResultStatusEnum(Integer value, String name, String nameDesc) {
        this.value = value;
        this.name = name;
        this.nameDesc = nameDesc;
    }

    public static InspectResultStatusEnum typeOfValue(Integer value){
        InspectResultStatusEnum[] values = InspectResultStatusEnum.values();
        for (InspectResultStatusEnum v : values) {
            if (v.value.equals(value)) {
                return v;
            }
        }
        return null;
    }

    public static InspectResultStatusEnum typeOfName(String name){
        InspectResultStatusEnum[] values = InspectResultStatusEnum.values();
        for (InspectResultStatusEnum v : values) {
            if (v.name.equals(name)) {
                return v;
            }
        }
        return null;
    }

    public static InspectResultStatusEnum typeOfNameDesc(String nameDesc){
        InspectResultStatusEnum[] values = InspectResultStatusEnum.values();
        for (InspectResultStatusEnum v : values) {
            if (v.nameDesc.equals(nameDesc)) {
                return v;
            }
        }
        return null;
    }
}
