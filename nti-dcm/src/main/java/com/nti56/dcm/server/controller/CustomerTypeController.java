package com.nti56.dcm.server.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.nti56.dcm.server.entity.CustomerTypeEntity;
import com.nti56.dcm.server.model.dto.CustomerTypeDto;
import com.nti56.dcm.server.service.CustomerTypeService;
import com.nti56.nlink.common.dto.TenantIsolation;
import com.nti56.nlink.common.util.*;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;


/**
 * <p>
 * 客户类型表 控制器
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @Ddate 2024-06-12 14:44:25
 * @since JDK 1.8
 */
@RestController
@RequestMapping("/customerType")
@Tag(name = "客户类型表模块")
public class CustomerTypeController {

    @Autowired
    private CustomerTypeService service;

    @GetMapping("page")
    @Operation(summary = "获取分页")
    public R page(@RequestHeader("dcm_headers") TenantIsolation tenantIsolation,
                  PageParam pageParam, CustomerTypeEntity entity){
        Page<CustomerTypeEntity> page = pageParam.toPage(CustomerTypeEntity.class);
        Result<Page<CustomerTypeEntity>> result = service.getPage(tenantIsolation, entity, page);
        return R.result(result);
    }

    @GetMapping("list")
    @Operation(summary = "获取列表" )
    public R list(@RequestHeader("dcm_headers") TenantIsolation tenantIsolation, CustomerTypeEntity entity){
        Result<List<CustomerTypeEntity>> result = service.list(tenantIsolation, entity);
        return R.result(result);
    }

    @PostMapping("")
    @Operation(summary = "创建对象")
    public R create(@RequestHeader("dcm_headers") TenantIsolation tenantIsolation, @RequestBody CustomerTypeEntity entity){
        return R.result(service.save(tenantIsolation, entity));
    }

    @PutMapping("")
    @Operation(summary = "更新")
    public R update(@RequestHeader("dcm_headers") TenantIsolation tenantIsolation, @RequestBody CustomerTypeEntity entity){
        return R.result(service.update(tenantIsolation, entity));
    }

    @DeleteMapping("{entityId}")
    @Operation(summary = "删除对象")
    public R delete(@RequestHeader("dcm_headers") TenantIsolation tenantIsolation, @PathVariable Long entityId){
        Result<Void> result = service.deleteById(entityId);
        return R.result(result);
    }

    @GetMapping("{entityId}")
    @Operation(summary = "获取对象")
    public R get(@RequestHeader("dcm_headers") TenantIsolation tenantIsolation, @PathVariable Long entityId){
        Result<CustomerTypeEntity> result = service.selectById(entityId);
        return R.result(result);
    }

    @PostMapping("saveBatch")
    @Operation(summary = "批量保存")
    public R saveBatch(@RequestHeader("dcm_headers") TenantIsolation tenantIsolation,
                       @Validated @RequestBody CustomerTypeDto dto) {
        return R.result(service.saveBatchType(tenantIsolation, dto));
    }
}
