package com.nti56.dcm.server.domain.enums;

import cn.hutool.core.util.StrUtil;
import com.nti56.dcm.server.model.dto.DeviceExpImportErrorDto;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.function.Function;


public enum DeviceExpImportEnum {
    deviceName("deviceName", "适用设备", (obj) -> {
        String deviceName = (String) obj;
        if (StrUtil.isNotBlank(deviceName)) {
            if (deviceName.length() > 255) {
                return new DeviceExpImportErrorDto("适用设备长度大于255，数据超长！");
            }
        }
        return null;
    }),
    deviceBomName("deviceBomName", "设备部位", (obj) -> {
        String deviceBomName = (String) obj;
        if (StrUtil.isNotBlank(deviceBomName)) {
            if (deviceBomName.length() > 255) {
                return new DeviceExpImportErrorDto("设备部位长度大于64，数据超长！");
            }
        }
        return null;
    }),
    deviceType("deviceType", "设备类型", (obj) -> {
        String deviceType = (String) obj;
        if (StrUtil.isBlank(deviceType)) {
            return new DeviceExpImportErrorDto("设备类型不能为空！");
        }
        if (StrUtil.isNotBlank(deviceType)) {
            if (deviceType.length() > 255) {
                return new DeviceExpImportErrorDto("设备类型长度大于255，无法插入！");
            }
        }
        return null;
    }),
    faultType("faultType", "故障类型", (obj) -> {
        String faultType = (String) obj;
        if (StrUtil.isBlank(faultType)) {
            return new DeviceExpImportErrorDto("故障类型不能为空！");
        }
        return null;
    }),
    faultReason("faultReason", "故障原因", (obj) -> {
        String validField = (String) obj;
        if (StrUtil.isBlank(validField)) {
            return new DeviceExpImportErrorDto("故障原因不能为空！");
        }
        if (validField.length() > 512) {
            return new DeviceExpImportErrorDto("故障原因长度大于255，无法插入！");
        }
        return null;
    }),
    repairProcess("repairProcess", "处理描述", (obj) -> {
        String validField = (String) obj;
        if (StrUtil.isBlank(validField)) {
            return new DeviceExpImportErrorDto("处理描述不能为空！");
        }
        if (validField.length() > 512) {
            return new DeviceExpImportErrorDto("处理描述长度大于255，无法插入！");
        }
        return null;
    });
    /**
     * 数据库的列名
     * 类中的变量名
     */
    private String objColName;

    /**
     * 对应的列名（excel）
     */
    private String excelColTitle;

    /**
     * 校验列数据的方法
     */
    private Function<Object, DeviceExpImportErrorDto> function;

    DeviceExpImportEnum(String objColName, String excelColTitle, Function<Object, DeviceExpImportErrorDto> function) {
        this.objColName = objColName;
        this.excelColTitle = excelColTitle;
        this.function = function;
    }

    public String getObjColName() {
        return objColName;
    }

    public void setObjColName(String objColName) {
        this.objColName = objColName;
    }

    public String getExcelColTitle() {
        return excelColTitle;
    }

    public void setExcelColTitle(String excelColTitle) {
        this.excelColTitle = excelColTitle;
    }

    public static List<String> getObjColNames() {
        List<String> list = new ArrayList<>();
        DeviceExpImportEnum[] values = DeviceExpImportEnum.values();
        for (DeviceExpImportEnum disctBBuMappingEnum : values) {
            list.add(disctBBuMappingEnum.getObjColName());
        }
        return list;
    }

    public static List<String> getExcelColTitles() {
        List<String> list = new ArrayList<>();
        DeviceExpImportEnum[] values = DeviceExpImportEnum.values();
        for (DeviceExpImportEnum exportEnum : values) {
            list.add(exportEnum.getExcelColTitle());
        }
        return list;
    }


    /**
     * excel列名为key, 对象属性为 value
     *
     * @return
     */
    public static Map<String, String> getObjNameMap() {
        Map<String, String> colMap = new HashMap<>();
        for (DeviceExpImportEnum obj : DeviceExpImportEnum.values()) {
            colMap.put(obj.getExcelColTitle(), obj.getObjColName());
        }
        return colMap;
    }

    public static DeviceExpImportErrorDto executeValidFunctionByColName(String objColName, Object value) {
        if (StrUtil.isBlank(objColName)) {
            return null;
        }
        for (DeviceExpImportEnum obj : DeviceExpImportEnum.values()) {
            if (obj.objColName.equals(objColName)) {
                return obj.function.apply(value);
            }
        }
        return null;
    }

    public static String getValueStringTemplate() {
        StringBuilder sb = new StringBuilder();
        sb.append("[");
        DeviceExpImportEnum[] values = DeviceExpImportEnum.values();
        for (int i = 0; i < values.length; i++) {
            DeviceExpImportEnum exportEnums = values[i];
            if (i == 0) {
                sb.append(exportEnums.getExcelColTitle());
            } else {
                sb.append(", " + exportEnums.getExcelColTitle());
            }
        }
        sb.append("]");
        return sb.toString();
    }
}
