package com.nti56.dcm.server.domain.enums;

import lombok.Getter;

public enum YesNoEnum {
    YES(1, "yes", "是"),
    NO(0, "no", "否"),
    ;

    @Getter
    private Integer value;

    @Getter
    private String name;

    @Getter
    private String nameDesc;

    YesNoEnum(Integer value, String name, String nameDesc) {
        this.value = value;
        this.name = name;
        this.nameDesc = nameDesc;
    }

    public static YesNoEnum typeOfValue(Integer value){
        YesNoEnum[] values = YesNoEnum.values();
        for (YesNoEnum v : values) {
            if (v.value.equals(value)) {
                return v;
            }
        }
        return null;
    }

    public static YesNoEnum typeOfName(String name){
        YesNoEnum[] values = YesNoEnum.values();
        for (YesNoEnum v : values) {
            if (v.name.equals(name)) {
                return v;
            }
        }
        return null;
    }

    public static YesNoEnum typeOfNameDesc(String nameDesc){
        YesNoEnum[] values = YesNoEnum.values();
        for (YesNoEnum v : values) {
            if (v.nameDesc.equals(nameDesc)) {
                return v;
            }
        }
        return null;
    }
}
