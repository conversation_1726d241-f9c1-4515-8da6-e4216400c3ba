package com.nti56.dcm.server.config;

import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.annotation.Order;
import org.springframework.core.io.ClassPathResource;
import org.springframework.stereotype.Component;

import java.security.KeyPair;

/*@Slf4j
@Configuration
@Order(Integer.MIN_VALUE)
//@EnableAuthorizationServer
public class OauthServerConfig {

    @Bean
    public TokenStore tokenStore() {
        InMemoryTokenStore tokenStore = new InMemoryTokenStore();
//        tokenStore.setPrefix(RedisConstant.PREX_REDIS_KEY);
        return tokenStore;
    }


    @Bean
    public ResourceServerTokenServices tokenServices() {
        DefaultTokenServices tokenServices = new DefaultTokenServices();
        tokenServices.setSupportRefreshToken(true);
        tokenServices.setTokenStore(tokenStore());
        return tokenServices;
    }
}*/
