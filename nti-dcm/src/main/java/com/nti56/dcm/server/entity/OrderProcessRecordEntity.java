package com.nti56.dcm.server.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <p>
 * 工单流程记录表 实体类
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @Ddate 2024-06-17 09:27:30
 * @since JDK 1.8
 */
@Data
@TableName(value = "order_process_record",autoResultMap = true)
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class OrderProcessRecordEntity extends BaseEntity{

        /**
         * 操作名称
         */
        private String operation;

        /**
         * 工单ID
         */
        private Long orderId;

        /**
         * 工单类型，1-维修，2-保养，3-巡检
         */
        private Integer orderType;


        /**
         * 流程实例ID
         */
        private String processInstanceId;

        /**
         * 工作流ID
         */
        private String flowId;

        /**
         * 执行人ID
         */
        private Long executorId;

        /**
         * 执行人名称
         */
        private String executorName;

        /**
         * 目标ID
         */
        private Long targetId;

        /**
         * 目标名称
         */
        private String targetName;

        /**
         * 备注
         */
        private String remark;

}
