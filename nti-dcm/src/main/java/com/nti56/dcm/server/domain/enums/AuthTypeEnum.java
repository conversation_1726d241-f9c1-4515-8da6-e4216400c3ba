package com.nti56.dcm.server.domain.enums;

import lombok.Getter;

public enum AuthTypeEnum {
    SYSTEM_AUTH(1, "SYSTEM_AUTH", "系统固定权限"),
    MENU_AUTH(2, "MENU_AUTH", "菜单权限"),
    BUTTON_AUTH(3, "BUTTON_AUTH", "按钮权限"),
    ;

    @Getter
    private Integer value;

    @Getter
    private String name;

    @Getter
    private String nameDesc;

    AuthTypeEnum(Integer value, String name, String nameDesc) {
        this.value = value;
        this.name = name;
        this.nameDesc = nameDesc;
    }

    public static AuthTypeEnum typeOfValue(Integer value){
        AuthTypeEnum[] values = AuthTypeEnum.values();
        for (AuthTypeEnum v : values) {
            if (v.value.equals(value)) {
                return v;
            }
        }
        return null;
    }

    public static AuthTypeEnum typeOfName(String name){
        AuthTypeEnum[] values = AuthTypeEnum.values();
        for (AuthTypeEnum v : values) {
            if (v.name.equals(name)) {
                return v;
            }
        }
        return null;
    }

    public static AuthTypeEnum typeOfNameDesc(String nameDesc){
        AuthTypeEnum[] values = AuthTypeEnum.values();
        for (AuthTypeEnum v : values) {
            if (v.nameDesc.equals(nameDesc)) {
                return v;
            }
        }
        return null;
    }
}
