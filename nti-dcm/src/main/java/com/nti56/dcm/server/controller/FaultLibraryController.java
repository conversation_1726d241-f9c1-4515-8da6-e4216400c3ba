package com.nti56.dcm.server.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.nti56.dcm.server.entity.FaultLibraryEntity;
import com.nti56.dcm.server.model.dto.FaultLibraryDto;
import com.nti56.dcm.server.model.vo.FaultLibraryVo;
import com.nti56.dcm.server.service.IFaultLibraryService;
import com.nti56.nlink.common.dto.TenantIsolation;
import com.nti56.nlink.common.util.*;
import com.nti56.common.util.R;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import org.springframework.validation.annotation.Validated;

/**
 * <p>
 * 故障库表 控制器
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @Ddate 2025-06-04 14:42:27
 * @since JDK 1.8
 */
@RestController
@RequestMapping("faultLibrary")
@Tag(name = "故障库表模块")
public class FaultLibraryController {

    @Autowired
    private IFaultLibraryService service;

    @GetMapping("page")
    @ApiOperation("获取分页")
    public R page(@RequestHeader("dcm_headers") TenantIsolation tenantIsolation,
                  PageParam pageParam, FaultLibraryDto dto) {
        Page<FaultLibraryVo> page = pageParam.toPage(FaultLibraryVo.class);
        Result<Page<FaultLibraryVo>> result = service.getPage(tenantIsolation, dto, page);
        return R.result(result);
    }

    @GetMapping("list")
    @ApiOperation("获取列表")
    public R list(@RequestHeader("dcm_headers") TenantIsolation tenantIsolation,
                  FaultLibraryEntity entity) {
        Result<List<FaultLibraryEntity>> result = service.list(tenantIsolation, entity);
        return R.result(result);
    }

    @PostMapping("")
    @ApiOperation("创建故障库记录")
    public R create(@RequestHeader("dcm_headers") TenantIsolation tenantIsolation,
                    @ApiParam("故障库对象") @RequestBody @Validated FaultLibraryEntity entity) {
        return R.result(service.save(tenantIsolation, entity));
    }

    @PutMapping("")
    @ApiOperation("更新故障库记录")
    public R update(@RequestHeader("dcm_headers") TenantIsolation tenantIsolation,
                    @ApiParam("故障库对象") @RequestBody @Validated FaultLibraryEntity entity) {
        return R.result(service.update(tenantIsolation, entity));
    }

    @DeleteMapping("/{entityId}")
    @ApiOperation("删除故障库记录")
    public R delete(@ApiParam("目标ID") @PathVariable Long entityId) {
        Result<Void> result = service.deleteById(entityId);
        return R.result(result);
    }

    @PostMapping("/batchDelete")
    @Operation(summary = "批量删除故障库记录")
    public R batchDelete(@RequestHeader("dcm_headers") TenantIsolation tenantIsolation, @RequestBody List<Long> ids) {
        return R.result(service.batchDelete(ids));
    }

    @GetMapping("/{entityId}")
    @ApiOperation("获取故障库记录")
    public R get(@ApiParam("目标ID") @PathVariable Long entityId) {
        Result<FaultLibraryEntity> result = service.getById(entityId);
        return R.result(result);
    }

    @GetMapping("syncOTEvent")
    @ApiOperation("同步OT事件")
    public R syncOTEvent() {
        service.syncOTEvent();
        return R.ok();
    }

}
