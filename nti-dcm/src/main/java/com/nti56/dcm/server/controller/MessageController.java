package com.nti56.dcm.server.controller;


import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.nti56.common.util.JwtUserInfoUtils;
import com.nti56.common.util.R;
import com.nti56.dcm.server.domain.enums.MessageTypeEnum;
import com.nti56.dcm.server.model.dto.DeviceMessagePushDto;
import com.nti56.dcm.server.model.dto.MessageDto;
import com.nti56.dcm.server.model.dto.MessageQueryDto;
import com.nti56.dcm.server.model.dto.MessageSendDto;
import com.nti56.dcm.server.service.IMessageService;
import com.nti56.nlink.common.dto.TenantIsolation;
import com.nti56.nlink.common.util.PageParam;
import com.nti56.nlink.common.util.Result;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.Arrays;

/**
 * <p>
 * 站内信表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-21
 */
@RestController
@RequestMapping("/message")
@Tag(name = "站内信")
public class MessageController {
    @Autowired
    private IMessageService messageService;

    @GetMapping("/page")
    @Operation(summary = "获取我的站内信分页")
    public R<Page<MessageDto>> page(@RequestHeader("dcm_headers") TenantIsolation tenantIsolation, PageParam pageParam, MessageQueryDto dto) {
        Page<MessageQueryDto> page = pageParam.toPage(MessageQueryDto.class);
        Result<Page<MessageDto>> result = messageService.pageMessage(page, dto, tenantIsolation);
        return R.result(result);
    }

    @GetMapping("/getNotReadCount")
    @Operation(summary = "获取当前用户未读消息数量")
    public R<Long> getNotReadCount(@RequestHeader("dcm_headers") TenantIsolation tenantIsolation) {
        return R.result(messageService.getNotReadCount(tenantIsolation));
    }

    @PostMapping("/sendMsgTest")
    @Operation(summary = "发送消息")
    public R sendMsg(@RequestHeader("dcm_headers") TenantIsolation tenantIsolation) {
        MessageSendDto messageSendDto = new MessageSendDto();
        MessageTypeEnum messageTypeEnum = MessageTypeEnum.DEVICE_FAULT;
        messageSendDto.setShowContent(messageTypeEnum.buildShowContent("高温老化1-4堆垛机_Crane_001", "堆垛机", "2024-06-24 14:33:00"))
                .setTitle(messageTypeEnum.getTitle())
                .setContent(messageTypeEnum.buildSaveContent("2024-06-24 14:33:00", "高温老化1-4堆垛机_Crane_001", "一段测试故障描述！"))
                .setMessageType(messageTypeEnum.getValue())
                .setTenantId(tenantIsolation.getTenantId());
        return R.result(messageService.sendMessage(messageSendDto, Arrays.asList(JwtUserInfoUtils.getUserId())));
    }
    @PostMapping("/sendApkTest")
    @Operation(summary = "发送测试今天app消息")
    public R sendApkTest(@RequestHeader("dcm_headers") TenantIsolation tenantIsolation) {
        DeviceMessagePushDto dto =  DeviceMessagePushDto.init();
        MessageTypeEnum messageTypeEnum = MessageTypeEnum.DEVICE_FAULT;
        dto.setTitle(messageTypeEnum.getTitle()+"【测试】");
        dto.setContent(messageTypeEnum.buildSaveContent("2024-06-24 14:33:00", "高温老化1-4堆垛机_Crane_001", "一段测试故障描述！"));
        return R.result(messageService.sendNtiApkMessage(dto, Arrays.asList(JwtUserInfoUtils.getUserId())));
    }


    @PutMapping("/readMessage/{id}")
    @Operation(summary = "已读消息")
    public R readMessage(@RequestHeader("dcm_headers") TenantIsolation tenantIsolation, @PathVariable Long id) {
        return R.result(messageService.readMessage(id, tenantIsolation));
    }

    @PutMapping("/readAllMessage")
    @Operation(summary = "一键已读全部消息")
    public R page(@RequestHeader("dcm_headers") TenantIsolation tenantIsolation, @RequestParam(required = false) Integer type) {
        return R.result(messageService.readAllMessage(tenantIsolation, type));
    }
}
