package com.nti56.common.tenant;

import com.nti56.nlink.common.dto.TenantIsolation;

/**
 * 类说明: <br/>
 *
 * <AUTHOR> <br/>
 * @version 1.0
 * @date 2022/4/19 14:38<br/>
 * @since JDK 1.8
 */

public class TenantIsolationThreadLocal {

    private final static ThreadLocal<TenantIsolation> threadLocal = ThreadLocal.withInitial(TenantIsolation::new);

    public static void set(TenantIsolation tenantIsolation){
        threadLocal.set(tenantIsolation);
    }

    public static TenantIsolation get(){
        return threadLocal.get();
    }

    public static Long getTenantId(){
        return threadLocal.get().getTenantId();
    }

    public static Long getSpaceId(){
        return threadLocal.get().getSpaceId();
    }

    public static Long getEngineeringId(){
        return threadLocal.get().getEngineeringId();
    }

    public static Long getModuleId(){
        return threadLocal.get().getModuleId();
    }

    public static void remove(){
        threadLocal.remove();
    }
}
