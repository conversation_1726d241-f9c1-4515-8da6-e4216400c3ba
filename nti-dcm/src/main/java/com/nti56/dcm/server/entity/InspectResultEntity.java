package com.nti56.dcm.server.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.time.LocalDateTime;
import lombok.Data;

/**
 * <p>
 * 点巡检结果表 实体类
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @Ddate 2024-02-23 15:13:54
 * @since JDK 1.8
 */
@Data
@TableName("inspect_result")
public class InspectResultEntity extends BaseEntity{
        /**
        * 所属工单id
        */
        private Long inspectOrderId;

        /**
        * 被检查的设备id
        */
        private Long deviceId;

        /**
         * 检查状态，0-未检查，1-已检查
         */
        private Integer status;

        /**
        * 检查开始时间
        */
        private LocalDateTime inspectBegin;

        /**
        * 检查束时间
        */
        private LocalDateTime inspectEnd;

        /**
        * 是否异常，1-异常，0-正常
        */
        private Integer isError;

}
