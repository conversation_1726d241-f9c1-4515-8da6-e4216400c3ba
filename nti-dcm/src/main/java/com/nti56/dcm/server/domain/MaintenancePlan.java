package com.nti56.dcm.server.domain;

import cn.hutool.core.collection.CollUtil;
import com.nti56.dcm.server.entity.MaintenancePlanEntity;
import com.nti56.dcm.server.model.dto.MaintenancePlanDto;
import com.nti56.nlink.common.fetcher.CommonFetcher;
import com.nti56.nlink.common.util.Result;
import lombok.Getter;
import org.springframework.beans.BeanUtils;

import java.util.List;
import java.util.Objects;

/**
 * 类说明: <br/>
 *
 * <AUTHOR> <br/>
 * @version 1.0
 * @date 2024/3/1 15:35<br/>
 * @since JDK 1.8
 */
public class MaintenancePlan {

    public static final SerialNumber serialNumber = new SerialNumber("BYJH");

    @Getter
    private MaintenancePlanEntity entity;

    public static Result<MaintenancePlan> checkCreate(MaintenancePlanDto dto, CommonFetcher commonFetcher) {
        // 校验是否存在重名
        List<MaintenancePlanEntity> repeatPlan = commonFetcher.list("plan_name", dto.getPlanName(), MaintenancePlanEntity.class);
        if (CollUtil.isNotEmpty(repeatPlan)) {
            return Result.error("保养计划名称已存在：" + dto.getPlanName());
        }
        MaintenancePlanEntity entity = new MaintenancePlanEntity();
        BeanUtils.copyProperties(dto, entity);
        MaintenancePlan maintenancePlan = new MaintenancePlan();
        maintenancePlan.entity = entity;
        return Result.ok(maintenancePlan);
    }

    public static Result<Void> checkDelete(Long id, CommonFetcher commonFetcher) {
        MaintenancePlanEntity maintenancePlan = commonFetcher.get(id, MaintenancePlanEntity.class);
        if (Objects.isNull(maintenancePlan)) {
            return Result.error("保养计划不存在，无法结束！");
        }
        return Result.ok();
    }

}
