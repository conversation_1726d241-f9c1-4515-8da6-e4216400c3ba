package com.nti56.dcm.server.entity;

import java.math.BigDecimal;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import java.io.Serializable;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 货品表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-16
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("pmo_equipment")
public class PmoEquipmentEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键id
     */
    @TableId(value = "id", type = IdType.NONE)
    private Long id;

    /**
     * 所属集团
     */
    private Long clientId;

    /**
     * 所属仓库
     */
    private Long whseId;

    /**
     * 货品编码
     */
    private String itemCode;

    /**
     * 货品条码
     */
    private String itemBarcode;

    /**
     * 货品名称
     */
    private String itemName;

    /**
     * 所属货类
     */
    private Long categoryId;

    /**
     * 描述
     */
    private String itemDescription;

    /**
     * 货品规格
     */
    private String itemSpec;

    /**
     * 品牌
     */
    private String itemBrand;

    /**
     * 单位名称
     */
    private String unit;

    /**
     * 单位
     */
    private Long unitId;

    /**
     * 订单单位
     */
    private Long orderUnitId;

    /**
     * 采购单价
     */
    private BigDecimal buyingPrice;

    /**
     * 销售单价
     */
    private BigDecimal sellingPrice;

    /**
     * 库存下限
     */
    private BigDecimal minQty;

    /**
     * 库存上限
     */
    private BigDecimal maxQty;

    /**
     * 商品图片
     */
    private String itemPicture;

    /**
     * 状态
     */
    private Integer status;

    /**
     * 货期（天）
     */
    private Integer delivery;

    /**
     * 备注
     */
    private String remark;

    /**
     * 公司代码
     */
    private String companyCode;

    /**
     * 公司名称
     */
    private String companyName;

    /**
     * 删除状态 0:未删除，1：已删除
     */
    private Integer deleted;

    /**
     * 创建人
     */
    private String createBy;

    /**
     * 创建人姓名
     */
    private String createByName;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 修改人
     */
    private String modifyBy;

    /**
     * 创建人姓名
     */
    private String modifyByName;

    /**
     * 更新时间
     */
    private LocalDateTime modifyTime;

    /**
     * 版本号
     */
    private Integer version;


}
