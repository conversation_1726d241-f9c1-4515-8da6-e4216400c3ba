package com.nti56.dcm.server.controller;

import com.nti56.dcm.server.service.IPmoSysUcenterOrgResultService;
import com.nti56.nlink.common.util.*;
import io.swagger.annotations.ApiOperation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * <p>
 * 基础数据-部门结果表 控制器
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @Ddate 2024-12-16 09:26:01
 * @since JDK 1.8
 */
@RestController
@RequestMapping("/pmoSysUcenterOrgRresult")
@Tag(name = "基础数据-部门结果表模块")
public class PmoSysUcenterOrgResultController {

    @Autowired
    IPmoSysUcenterOrgResultService service;

    @GetMapping("list")
    @ApiOperation(value = "获取今天国际一级部门" )
    public R list(@RequestParam("pcFlag") boolean pcFlag){
        return R.result(service.listOrg(pcFlag));
    }
    
}
