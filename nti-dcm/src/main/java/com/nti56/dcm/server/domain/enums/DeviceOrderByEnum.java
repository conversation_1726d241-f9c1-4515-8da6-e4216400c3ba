package com.nti56.dcm.server.domain.enums;

import lombok.Getter;

public enum DeviceOrderByEnum {
    TIME(1, "time", "按时间排序"),
    NAME(2, "name", "按名称排序"),
    ;

    @Getter
    private Integer value;

    @Getter
    private String name;

    @Getter
    private String nameDesc;

    DeviceOrderByEnum(Integer value, String name, String nameDesc) {
        this.value = value;
        this.name = name;
        this.nameDesc = nameDesc;
    }

    public static DeviceOrderByEnum typeOfValue(Integer value){
        DeviceOrderByEnum[] values = DeviceOrderByEnum.values();
        for (DeviceOrderByEnum v : values) {
            if (v.value.equals(value)) {
                return v;
            }
        }
        return null;
    }

    public static DeviceOrderByEnum typeOfName(String name){
        DeviceOrderByEnum[] values = DeviceOrderByEnum.values();
        for (DeviceOrderByEnum v : values) {
            if (v.name.equals(name)) {
                return v;
            }
        }
        return null;
    }

    public static DeviceOrderByEnum typeOfNameDesc(String nameDesc){
        DeviceOrderByEnum[] values = DeviceOrderByEnum.values();
        for (DeviceOrderByEnum v : values) {
            if (v.nameDesc.equals(nameDesc)) {
                return v;
            }
        }
        return null;
    }
}
