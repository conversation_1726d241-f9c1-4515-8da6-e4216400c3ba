<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.nti56.dcm.server.mapper.FaultLibraryMapper">

    <select id="listFaultLibrary" resultType="com.nti56.dcm.server.model.vo.FaultLibraryVo">
        select dt.type_name deviceTypeName, 0 isPmo,
               fl.id, fl.device_type_id, fl.fault_code, fl.fault_desc, fl.fault_event, fl.solve_method, fl.prevent_strategy,
               fl.tenant_id, fl.id_type, fl.create_time, fl.update_time, fl.deleted, fl.creator, fl.updator
        from fault_library fl
                 left join device_type dt on fl.device_type_id = dt.id
        where fl.deleted = 0 and fl.tenant_id = #{dto.tenantId}
          and fl.id_type = #{dto.idType}
        <if test="dto.deviceTypeId != null">
            and fl.device_type_id = #{dto.deviceTypeId}
        </if>
        <if test="dto.faultCode != null and dto.faultCode != ''">
            and fl.fault_code like CONCAT('%', #{dto.faultCode},'%')
        </if>
        <if test="dto.faultDesc != null and dto.faultDesc != ''">
            and fl.fault_desc like CONCAT('%', #{dto.faultDesc},'%')
        </if>
        <if test="dto.faultEvent != null and dto.faultEvent != ''">
            and fl.fault_event like CONCAT('%', #{dto.faultEvent},'%')
        </if>
        <if test="dto.solveMethod != null and dto.solveMethod != ''">
            and fl.solve_method like CONCAT('%', #{dto.solveMethod},'%')
        </if>
        <if test="dto.preventStrategy!= null and dto.preventStrategy!= ''">
            and fl.prevent_strategy like CONCAT('%', #{dto.preventStrategy},'%')
        </if>
        order by fl.create_time desc
    </select>

    <select id="listFaultLibraryByPmo" resultType="com.nti56.dcm.server.model.vo.FaultLibraryVo">
        select dt.type_name deviceTypeName, 1 isPmo,
               fl.id, fl.device_type_id, fl.fault_code, fl.fault_desc, fl.fault_event, fl.solve_method, fl.prevent_strategy,
               fl.tenant_id, fl.id_type, fl.create_time, fl.update_time, fl.deleted, fl.creator, fl.updator
        from fault_library fl
        left join device_type dt on fl.device_type_id = dt.id
        left join customer_relation cr on fl.tenant_id = cr.supplier_id
        where fl.deleted = 0 and fl.id_type = 1
            and cr.status = 1 and cr.deleted = 0 and cr.id_type = 2 and cr.customer_id = #{dto.customerId}
        <if test="dto.deviceTypeIds != null and dto.deviceTypeIds.size > 0">
            and fl.device_type_id in
            <foreach collection="dto.deviceTypeIds" item="id" open="(" close=")" separator=",">
                #{id}
            </foreach>
        </if>
        <if test="dto.deviceTypeId != null">
            and fl.device_type_id = #{dto.deviceTypeId}
        </if>
        <if test="dto.faultCode != null and dto.faultCode != ''">
            and fl.fault_code like CONCAT('%', #{dto.faultCode},'%')
        </if>
        <if test="dto.faultDesc != null and dto.faultDesc != ''">
            and fl.fault_desc like CONCAT('%', #{dto.faultDesc},'%')
        </if>
        <if test="dto.faultEvent != null and dto.faultEvent != ''">
            and fl.fault_event like CONCAT('%', #{dto.faultEvent},'%')
        </if>
        <if test="dto.solveMethod != null and dto.solveMethod != ''">
            and fl.solve_method like CONCAT('%', #{dto.solveMethod},'%')
        </if>
        <if test="dto.preventStrategy!= null and dto.preventStrategy!= ''">
            and fl.prevent_strategy like CONCAT('%', #{dto.preventStrategy},'%')
        </if>
        order by fl.create_time desc
    </select>

</mapper>