package com.nti56.dcm.server.domain.enums;

import lombok.Getter;

public enum OverdueEnum {
    NO(1, "no", "未逾期"),
    YES(2, "yes", "逾期"),
    ;

    @Getter
    private Integer value;

    @Getter
    private String name;

    @Getter
    private String nameDesc;

    OverdueEnum(Integer value, String name, String nameDesc) {
        this.value = value;
        this.name = name;
        this.nameDesc = nameDesc;
    }

    public static OverdueEnum typeOfValue(Integer value){
        OverdueEnum[] values = OverdueEnum.values();
        for (OverdueEnum v : values) {
            if (v.value.equals(value)) {
                return v;
            }
        }
        return null;
    }

    public static OverdueEnum typeOfName(String name){
        OverdueEnum[] values = OverdueEnum.values();
        for (OverdueEnum v : values) {
            if (v.name.equals(name)) {
                return v;
            }
        }
        return null;
    }

    public static OverdueEnum typeOfNameDesc(String nameDesc){
        OverdueEnum[] values = OverdueEnum.values();
        for (OverdueEnum v : values) {
            if (v.nameDesc.equals(nameDesc)) {
                return v;
            }
        }
        return null;
    }
}
