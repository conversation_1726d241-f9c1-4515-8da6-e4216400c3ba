package com.nti56.dcm.server.controller;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.nti56.common.util.R;
import com.nti56.dcm.server.entity.DeviceTypeMonitorEntity;
import com.nti56.dcm.server.model.dto.DeviceTypeMonitorDto;
import com.nti56.dcm.server.service.DeviceTypeMonitorService;
import com.nti56.nlink.common.dto.TenantIsolation;
import com.nti56.nlink.common.util.Result;

import lombok.extern.slf4j.Slf4j;

@RestController
@RequestMapping("device-type-monitor")
@Slf4j
public class DeviceTypeMonitorController {
    
    @Autowired
    private DeviceTypeMonitorService deviceTypeMonitorService;

    /**
     * 创建设备类型监控数据
     * @param dto 设备类型监控dto
     * @return
     */
    @PostMapping("/create")
    public R<DeviceTypeMonitorEntity> create(@RequestHeader("dcm_headers")TenantIsolation tenant, @Validated @RequestBody DeviceTypeMonitorDto dto){
        Result<DeviceTypeMonitorEntity> result = deviceTypeMonitorService.create(tenant.getTenantId(), dto);
        return R.result(result);
    }


    /**
     * 删除设备类型监控
     * @param deviceTypeMonitorId 设备类型监控id
     * @return
     */
    @DeleteMapping("/remove/{deviceTypeMonitorId}")
    public R<Long> removePart(@RequestHeader("dcm_headers")TenantIsolation tenant, @PathVariable("deviceTypeMonitorId") Long deviceTypeMonitorId){
        Result<Long> result = deviceTypeMonitorService.remove(tenant.getTenantId(), deviceTypeMonitorId);
        return R.result(result);
    }

    
    /**
     * 查询设备类型监控
     * @param deviceTypeId 设备类型id
     * @return
     */
    @GetMapping("/list/{deviceTypeId}")
    public R<List<DeviceTypeMonitorEntity>> list(@RequestHeader("dcm_headers") TenantIsolation tenant, @PathVariable("deviceTypeId") Long deviceTypeId){
        
        Result<List<DeviceTypeMonitorEntity>> result = deviceTypeMonitorService.listByDeviceTypeId(tenant.getTenantId(), deviceTypeId);
        return R.result(result);
    }

}
