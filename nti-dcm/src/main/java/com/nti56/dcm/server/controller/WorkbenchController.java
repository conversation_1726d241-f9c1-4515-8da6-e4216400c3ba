package com.nti56.dcm.server.controller;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.nti56.common.util.R;
import com.nti56.dcm.server.model.dto.DeviceCountVo;
import com.nti56.dcm.server.model.dto.InspectOrderMySummaryVo;
import com.nti56.dcm.server.model.vo.WaitProcessMaintenanceCountVo;
import com.nti56.dcm.server.service.IDeviceRepairService;
import com.nti56.dcm.server.service.InspectOrderService;
import com.nti56.dcm.server.service.MaintenanceOrderService;
import com.nti56.nlink.common.dto.TenantIsolation;
import com.nti56.nlink.common.util.Result;

import lombok.extern.slf4j.Slf4j;

/**
 * 类说明: 
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2024-05-17 11:40:24
 * @since JDK 1.8
 */
@RestController
@RequestMapping("/workbench")
@Slf4j
public class WorkbenchController {
    
    @Autowired
    private InspectOrderService inspectOrderService;

    @Autowired
    private IDeviceRepairService deviceRepairService;
    
    @Autowired
    private MaintenanceOrderService maintenanceOrderService;

    /**
     * 统计我的工单概况
     */
    @GetMapping("/my-summary")
    public R<DeviceCountVo> mySummary(@RequestHeader("dcm_headers") TenantIsolation tenant){
        DeviceCountVo result = deviceRepairService.getWaitRepairCount(tenant);

        Result<InspectOrderMySummaryVo> r1 = inspectOrderService.mySummary(tenant.getTenantId(), tenant.getIdType());
        if(!r1.getSignal()){
            return R.error(r1.getMessage());
        }
        InspectOrderMySummaryVo inspect = r1.getResult();
        result.setInSourceInspectCount(inspect.getMyProcessingInspectOrderCount());
        result.setOutSourceInspectCount(inspect.getMyProcessingOutsourceInspectOrderCount());
        
        Result<WaitProcessMaintenanceCountVo> r2 = maintenanceOrderService.countWaitProcessMaintenanceOrder(tenant);
        if(!r2.getSignal()){
            return R.error(r2.getMessage());
        }
        WaitProcessMaintenanceCountVo maintenance = r2.getResult();
        result.setInSourceMaintenanceCount(maintenance.getInsourceCount());
        result.setOutSourceMaintenanceCount(maintenance.getOutsourceCount());
        
        return R.ok(result);
    }


}
