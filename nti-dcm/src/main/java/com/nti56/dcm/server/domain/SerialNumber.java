package com.nti56.dcm.server.domain;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;

import cn.hutool.core.util.RandomUtil;
import lombok.Getter;

public class SerialNumber {
    
    @Getter
    private String prefix;

    private SerialNumber(){}
    
    public SerialNumber(String prefix){
        this.prefix = prefix;
    }

    private static final DateTimeFormatter yyMMdd = DateTimeFormatter.ofPattern("yyMMdd");

    public String createPrefixDate(LocalDate date, String tenantCode){
        String d = yyMMdd.format(date);
        return prefix + "-" + tenantCode + "-" + d;
    }

    public String create(LocalDate date, Integer number, String tenantCode){
        String d = yyMMdd.format(date);
        String num = number.toString();
        if(num.length() < 4){
            num = String.format("%04d", number);
        }
        return prefix + "-" + tenantCode + "-" + d + num;
    }
    
    public String createRandom(String tenantCode){
        String randomStr10 = RandomUtil.randomStringUpper(10);
        return prefix + "-" + tenantCode + "-" + randomStr10;
    }

}
