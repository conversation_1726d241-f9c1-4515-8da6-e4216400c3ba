package com.nti56.dcm.server.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.nti56.dcm.server.entity.DeviceTypeEntity;
import com.nti56.dcm.server.model.dto.ChangeStatusDto;
import com.nti56.dcm.server.model.dto.DeviceTemplateRelationDto;
import com.nti56.dcm.server.model.dto.DeviceTypeCopyDto;
import com.nti56.dcm.server.model.dto.DeviceTypeDto;
import com.nti56.dcm.server.model.dto.DeviceTypeTreeDto;
import com.nti56.dcm.server.model.vo.DeviceTypeRelationVo;
import com.nti56.dcm.server.service.DeviceTypeService;
import com.nti56.nlink.common.dto.TenantIsolation;
import com.nti56.nlink.common.util.PageParam;
import com.nti56.common.util.R;
import com.nti56.nlink.common.util.Result;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;
import liquibase.change.Change;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/deviceType")
@Tag(name = "设备类型模块")
public class DeviceTypeController {

    @Autowired
    private DeviceTypeService deviceTypeService;

    @GetMapping("/typeTree")
    @Operation(summary = "获取设备类型树")
    public R<DeviceTypeTreeDto> typeTree(@RequestHeader("dcm_headers") TenantIsolation tenantIsolation,boolean filterIsPmo){
        return R.result(deviceTypeService.getTypeTree(tenantIsolation,filterIsPmo));
    }

    @GetMapping("/page")
    @Operation(summary = "获取设备类型分页")
    public R<Page<DeviceTypeEntity>> page(@RequestHeader("dcm_headers") TenantIsolation tenantIsolation, PageParam pageParam, DeviceTypeDto deviceTypeDto){
        Page<DeviceTypeEntity> page = pageParam.toPage(DeviceTypeEntity.class);
        Result<Page<DeviceTypeEntity>> result = deviceTypeService.getDeviceTypePage(deviceTypeDto,page,tenantIsolation);
        return  R.result(result);
    }

    @PostMapping("")
    @Operation(summary = "新增设备类型")
    public R<DeviceTypeEntity> createDeviceType(@RequestHeader("dcm_headers") TenantIsolation tenantIsolation,
                           @RequestBody @Validated DeviceTypeDto deviceTypeDto){
        return R.result(deviceTypeService.createDeviceType(deviceTypeDto,tenantIsolation));
    }


    @PutMapping("/{id}")
    @Operation(summary = "修改设备类型")
    public R<Void> editChannel(@RequestHeader("dcm_headers") TenantIsolation tenantIsolation,
                         @RequestBody DeviceTypeDto deviceTypeDto){
        return R.result(deviceTypeService.editDeviceType(deviceTypeDto,tenantIsolation));
    }

    @DeleteMapping("/{id}")
    @Operation(summary = "根据id删除设备类型")
    public R deleteDeviceType(@RequestHeader("dcm_headers") TenantIsolation tenantIsolation,
                              @PathVariable Long id){
        return R.result(deviceTypeService.deleteDeviceType(id,tenantIsolation));
    }

    @GetMapping("/dict")
    @Operation(summary = "获取设备类型字典/下拉数据")
    public R typeDict(@RequestHeader("dcm_headers") TenantIsolation tenantIsolation){
        return  R.result(deviceTypeService.getDeviceTypeDict(tenantIsolation));
    }

    @PostMapping("/changeStatus")
    @Operation(summary = "批量更改启用、停用状态")
    public R<Void> batchChangeStatus(@RequestHeader("dcm_headers") TenantIsolation tenantIsolation,
                               @RequestBody ChangeStatusDto changeStatusDto){
        return R.result(deviceTypeService.batchUpdateStatus(changeStatusDto,tenantIsolation));
    }

    @GetMapping("/{id}")
    @Operation(summary = "获取设备类型明细")
    public R<DeviceTypeDto> getDeviceType(@RequestHeader("dcm_headers") TenantIsolation tenantIsolation, @PathVariable Long id){
        return R.result(deviceTypeService.getDeviceType(id,tenantIsolation));
    }

    @GetMapping("/list-core-device-type")
    @Operation(summary = "获取核心设备类型列表")
    public R<List<DeviceTypeEntity>> listCoreDeviceType(@RequestHeader("dcm_headers") TenantIsolation tenantIsolation){
        return R.result(deviceTypeService.listCoreDeviceType(tenantIsolation.getTenantId()));
    }

    @GetMapping("/monitor/{id}")
    @Operation(summary = "获取设备类型监控数据")
    public R<List<DeviceTemplateRelationDto>> queryMonitorData(@RequestHeader("dcm_headers") TenantIsolation tenantIsolation, @PathVariable Long id){
        return R.result(deviceTypeService.queryMonitorData(tenantIsolation,id));
    }



    @PutMapping("/updateMonitorData")
    @Operation(summary = "修改设备类型监控数据")
    public R<Void> updateMoniotrData(@RequestHeader("dcm_headers") TenantIsolation tenantIsolation,
                               @RequestBody DeviceTemplateRelationDto deviceTemplateRelationDto){
        return R.result(deviceTypeService.updateMonitorData(deviceTemplateRelationDto,tenantIsolation));
    }


    @PutMapping("/copyDeviceType")
    @Operation(summary = "复制设备类型")
    public R<Void> copyDeviceType(@RequestHeader("dcm_headers") TenantIsolation tenantIsolation,
                                     @Validated @RequestBody DeviceTypeCopyDto deviceTypeCopyDto){
        return R.result(deviceTypeService.copyDeviceType(deviceTypeCopyDto,tenantIsolation));
    }

    @GetMapping("/copy")
    @Operation(summary = "将设备类型分组复制为设备类型")
    public void copy(){
        deviceTypeService.copy();
    }

}
