package com.nti56.dcm.server.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.util.Date;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 
 * @TableName device_document
 */
@TableName(value ="device_document")
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class DeviceDocumentEntity implements Serializable {
    /**
     * 档案ID
     */
    @TableId
    private Long id;

    /**
     * 资源路径
     */
    private String url;

    /**
     * 设备ID
     */
    private Long deviceId;

    /**
     * 资源名称
     */
    private String resourceName;

    /**
     * 资源类型 设备文档/设备图片 4/3
     */
    private Integer documentType;

    /**
     * 创建人
     */
    private String creator;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 租户ID
     */
    private Long tenantId;

    /**
     * 工程ID
     */
    private Long engineeringId;

    /**
     * 模块ID
     */
    private Long moduleId;

    /**
     * 空间ID
     */
    private Long spaceId;

    /**
     * 版本号
     */
    private Integer version;

    /**
     * 删除
     */
    private Integer deleted;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;

}