package com.nti56.dcm.server.domain.enums;

import lombok.Getter;

/**
 * 类说明: 文件类型
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2023-11-02 11:05:28
 * @since JDK 1.8
 */
public enum FileTypeEnum {
    PICTURE(1, "picture", "图片"), 
    TEXT(2, "text", "text文件"),
    PDF(3, "pdf", "pdf文件"),
    DOC(4, "doc", "word文件"),
    EXCEL(5, "excel", "excel文件"),
    PPT(6, "ppt", "ppt文件"),
    JSON(7, "json", "json文件"),
    MARKDOWN(7, "markdown", "markdown文件"),
    BINARY(8, "binary", "二进制文件"),
    APK(9, "apk", "apk文件"),
    ;

    @Getter
    private Integer value;

    @Getter
    private String name;

    @Getter
    private String nameDesc;

    FileTypeEnum(Integer value, String name, String nameDesc) {
        this.value = value;
        this.name = name;
        this.nameDesc = nameDesc;
    }

    public static FileTypeEnum typeOfValue(Integer value){
        FileTypeEnum[] values = FileTypeEnum.values();
        for (FileTypeEnum v : values) {
            if (v.value.equals(value)) {
                return v;
            }
        }
        return null;
    }

    public static FileTypeEnum typeOfName(String name){
        FileTypeEnum[] values = FileTypeEnum.values();
        for (FileTypeEnum v : values) {
            if (v.name.equals(name)) {
                return v;
            }
        }
        return null;
    }

    public static FileTypeEnum typeOfNameDesc(String nameDesc){
        FileTypeEnum[] values = FileTypeEnum.values();
        for (FileTypeEnum v : values) {
            if (v.nameDesc.equals(nameDesc)) {
                return v;
            }
        }
        return null;
    }
}
