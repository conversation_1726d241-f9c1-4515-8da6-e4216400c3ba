package com.nti56.dcm.server.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <p>
 * 基础数据-部门结果表 实体类
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @Ddate 2024-12-16 09:26:01
 * @since JDK 1.8
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@TableName("pmo_sys_ucenter_org_result")
public class PmoSysUcenterOrgResultEntity implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
    * 部门id
    */
    private Long id;

    /**
    * 租户id
    */
    private Long clientId;

    /**
    * 父级部门ID
    */
    private String parentOrgId;

    /**
    * 钉钉部门id
    */
    private String orgCode;

    /**
    * 部门名称
    */
    private String orgName;

    /**
    * 部门深度
    */
    private String depth;

    /**
    * 上级部门树
    */
    private String orgCodePath;

    /**
    * 上级部门树名称
    */
    private String orgNamePath;



}
