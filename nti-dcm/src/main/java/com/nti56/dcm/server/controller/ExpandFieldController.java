package com.nti56.dcm.server.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.nti56.common.util.R;
import com.nti56.dcm.server.entity.ExpandFieldEntity;
import com.nti56.dcm.server.service.ExpandFieldService;
import com.nti56.nlink.common.dto.TenantIsolation;
import com.nti56.nlink.common.util.PageParam;
import com.nti56.nlink.common.util.Result;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.apache.ibatis.annotations.Param;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;


/**
 * <p>
 * 拓展字段表 控制器
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @Ddate 2024-08-09 14:53:11
 * @since JDK 1.8
 */
@RestController
@RequestMapping("/expandField")
@Tag(name = "拓展字段表模块")
public class ExpandFieldController {

    @Autowired
    private ExpandFieldService service;

    @GetMapping("page")
    @Operation(summary = "获取分页")
    public R page(@RequestHeader("dcm_headers") TenantIsolation tenantIsolation,
                  PageParam pageParam, ExpandFieldEntity entity){
        Page<ExpandFieldEntity> page = pageParam.toPage(ExpandFieldEntity.class);
        Result<Page<ExpandFieldEntity>> result = service.getPage(tenantIsolation, entity, page);
        return R.result(result);
    }

    @GetMapping("list")
    @Operation(summary = "获取列表")
    public R list(@RequestHeader("dcm_headers") TenantIsolation tenantIsolation, ExpandFieldEntity entity){
        Result<List<ExpandFieldEntity>> result = service.getList(tenantIsolation, entity);
        return R.result(result);
    }

    @PostMapping("create")
    @Operation(summary = "创建对象")
    public R create(@RequestHeader("dcm_headers") TenantIsolation tenantIsolation, @Validated @RequestBody ExpandFieldEntity entity){
        return R.result(service.create(tenantIsolation, entity));
    }

    @PutMapping("update")
    @Operation(summary = "更新")
    public R update(@RequestHeader("dcm_headers") TenantIsolation tenantIsolation, @RequestBody ExpandFieldEntity entity){
        Result<Void> result = service.update(tenantIsolation, entity);
        return R.result(result);
    }

    @DeleteMapping("delete/{entityId}")
    @Operation(summary = "删除对象")
    public R delete(@RequestHeader("dcm_headers") TenantIsolation tenantIsolation, @PathVariable Long entityId){
        Result<Void> result = service.deleteById(entityId);
        return R.result(result);
    }

    @GetMapping("getById/{entityId}")
    @Operation(summary = "获取对象")
    public R get(@RequestHeader("dcm_headers") TenantIsolation tenantIsolation, @PathVariable Long entityId){
        Result<ExpandFieldEntity> result = service.selectById(entityId);
        return R.result(result);
    }

    @GetMapping("/download-template")
    @Operation(summary = "下载模板")
    public void downloadTemplate(HttpServletResponse response) throws IOException {
        service.downloadTemplate(response);
    }

    @PostMapping("import")
    @Operation(summary = "导入")
    public R importField(@RequestHeader("dcm_headers") TenantIsolation tenantIsolation,
                         @RequestParam("file") MultipartFile file,
                         @Param("idType") Integer idType) {
        tenantIsolation.setIdType(idType);
        return R.result(service.importField(tenantIsolation, file));
    }

    @GetMapping("export")
    @Operation(summary = "导出")
    public void exportField(@RequestHeader("dcm_headers") TenantIsolation tenantIsolation,
                            @Param("idType") Integer idType,
                            HttpServletResponse response) throws IOException {
        service.exportField(tenantIsolation.getTenantId(), idType, response);
    }
    
}
