package com.nti56.dcm.server.config;

import com.nti56.dcm.server.feign.OtController;
import com.nti56.dcm.server.feign.OtOpenController;
import feign.Feign;
import feign.gson.GsonDecoder;
import feign.gson.GsonEncoder;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * 类说明: 
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2022-05-26 09:25:26
 * @since JDK 1.8
 */
@Configuration
@Slf4j
public class FeignConfig {
    
    @Value("${ot.url}")
    private String otUrl;

    @Bean
    public OtController otDeviceController(){
        OtController proxy = Feign.builder()
            .decoder(new GsonDecoder())
            .encoder(new GsonEncoder())
            .target(OtController.class, otUrl);
        log.info("FeignConfig OtDeviceController on:{}", proxy);
        return proxy;
    }

    @Bean
    public OtOpenController OtOpenController(){
        OtOpenController proxy = Feign.builder()
            .decoder(new GsonDecoder())
            .encoder(new GsonEncoder())
            .target(OtOpenController.class, otUrl + "/it");
        log.info("FeignConfig OtOpenController on:{}", proxy);
        return proxy;
    }

}
