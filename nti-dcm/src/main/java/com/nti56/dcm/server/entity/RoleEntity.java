package com.nti56.dcm.server.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import lombok.Data;

/**
 * <p>
 * 角色表 实体类
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @Ddate 2024-02-23 15:13:54
 * @since JDK 1.8
 */
@Data
@TableName("role")
public class RoleEntity extends BaseEntity{
        /**
        * 身份类型, 1-供应商，2-客户
        */
        private Integer idType;

        /**
        * 角色名
        */
        private String roleName;

        /**
        * 角色描述
        */
        private String roleDesc;

        /**
        * 角色类型，1-系统固定角色，2-自定义角色
        */
        private Integer roleType;



}
