package com.nti56.dcm.server.domain.enums;

import lombok.Getter;

public enum ChannelTypeEnum {
    EMAIL(0, "eMail", "邮件"),
    SHORT_MSG(1, "shortMessage", "短信"),
    DINGTALK_ROBOT(2, "dingTalk robot", "钉钉机器人"),
    DINGTALK_CARD(3, "dingTalk card", "钉钉卡片"),
    WECOM_ROBOT(4, "WeCom robot", "企业微信机器人"),
    NTI_APK(98, "nti cloud apk", "今天云APP"),
    MESSAGE(99, "message", "站内信"),
    ;

    @Getter
    private Integer value;

    @Getter
    private String name;

    @Getter
    private String nameDesc;

    ChannelTypeEnum(Integer value, String name, String nameDesc) {
        this.value = value;
        this.name = name;
        this.nameDesc = nameDesc;
    }

    public static ChannelTypeEnum typeOfValue(Integer value){
        ChannelTypeEnum[] values = ChannelTypeEnum.values();
        for (ChannelTypeEnum v : values) {
            if (v.value.equals(value)) {
                return v;
            }
        }
        return null;
    }

    public static ChannelTypeEnum typeOfName(String name){
        ChannelTypeEnum[] values = ChannelTypeEnum.values();
        for (ChannelTypeEnum v : values) {
            if (v.name.equals(name)) {
                return v;
            }
        }
        return null;
    }

    public static ChannelTypeEnum typeOfNameDesc(String nameDesc){
        ChannelTypeEnum[] values = ChannelTypeEnum.values();
        for (ChannelTypeEnum v : values) {
            if (v.nameDesc.equals(nameDesc)) {
                return v;
            }
        }
        return null;
    }
}
