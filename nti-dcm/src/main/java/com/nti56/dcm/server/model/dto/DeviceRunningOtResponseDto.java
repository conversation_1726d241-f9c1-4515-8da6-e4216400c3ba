package com.nti56.dcm.server.model.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;


@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder

// 主数据类
public class DeviceRunningOtResponseDto {

    // 字段信息列表
    private List<ColumnInfo> columns;

    // 记录列表
    private List<DeviceRunningOtResponseRecordDto> records;

    // 分组键信息列表
    private List<GroupKeyInfo> groupKey;

    // setter 和 getter 方法
    public List<ColumnInfo> getColumns() {
        return columns;
    }

    public void setColumns(List<ColumnInfo> columns) {
        this.columns = columns;
    }

    public List<DeviceRunningOtResponseRecordDto> getRecords() {
        return records;
    }

    public void setRecords(List<DeviceRunningOtResponseRecordDto> records) {
        this.records = records;
    }

    public List<GroupKeyInfo> getGroupKey() {
        return groupKey;
    }

    public void setGroupKey(List<GroupKeyInfo> groupKey) {
        this.groupKey = groupKey;
    }
}

// 列信息类
class ColumnInfo {
    private int index;
    private String label;
    private String dataType;
    private boolean group;
    private String defaultValue;

    // setter 和 getter 方法
    public int getIndex() {
        return index;
    }

    public void setIndex(int index) {
        this.index = index;
    }

    public String getLabel() {
        return label;
    }

    public void setLabel(String label) {
        this.label = label;
    }

    public String getDataType() {
        return dataType;
    }

    public void setDataType(String dataType) {
        this.dataType = dataType;
    }

    public boolean isGroup() {
        return group;
    }

    public void setGroup(boolean group) {
        this.group = group;
    }

    public String getDefaultValue() {
        return defaultValue;
    }

    public void setDefaultValue(String defaultValue) {
        this.defaultValue = defaultValue;
    }
}


// 分组键信息类
class GroupKeyInfo {
    private int index;
    private String label;
    private String dataType;
    private boolean group;
    private String defaultValue;

    // setter 和 getter 方法
    public int getIndex() {
        return index;
    }

    public void setIndex(int index) {
        this.index = index;
    }

    public String getLabel() {
        return label;
    }

    public void setLabel(String label) {
        this.label = label;
    }

    public String getDataType() {
        return dataType;
    }

    public void setDataType(String dataType) {
        this.dataType = dataType;
    }

    public boolean isGroup() {
        return group;
    }

    public void setGroup(boolean group) {
        this.group = group;
    }

    public String getDefaultValue() {
        return defaultValue;
    }

    public void setDefaultValue(String defaultValue) {
        this.defaultValue = defaultValue;
    }
}