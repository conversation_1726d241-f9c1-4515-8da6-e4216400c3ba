package com.nti56.dcm.server.domain.enums;

import lombok.Getter;

/**
 * 类说明: 启用停用枚举
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2023-11-02 11:05:35
 * @since JDK 1.8
 */
public enum StatusEnum {
    ENABLE(1, "enable", "启用"),
    DISABLE(0, "disable", "停用"),
    ;

    @Getter
    private Integer value;

    @Getter
    private String name;

    @Getter
    private String nameDesc;

    StatusEnum(Integer value, String name, String nameDesc) {
        this.value = value;
        this.name = name;
        this.nameDesc = nameDesc;
    }

    public static StatusEnum typeOfValue(Integer value){
        StatusEnum[] values = StatusEnum.values();
        for (StatusEnum v : values) {
            if (v.value.equals(value)) {
                return v;
            }
        }
        return null;
    }

    public static StatusEnum typeOfName(String name){
        StatusEnum[] values = StatusEnum.values();
        for (StatusEnum v : values) {
            if (v.name.equals(name)) {
                return v;
            }
        }
        return null;
    }

    public static StatusEnum typeOfNameDesc(String nameDesc){
        StatusEnum[] values = StatusEnum.values();
        for (StatusEnum v : values) {
            if (v.nameDesc.equals(nameDesc)) {
                return v;
            }
        }
        return null;
    }
}
