package com.nti56.dcm.server.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 类说明: 关系管理
 *
 * <AUTHOR> <br/>
 * @version 1.0
 * @date 2024/2/21 9:34<br/>
 * @since JDK 1.8
 */
@TableName(value ="customer_relation")
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class CustomerRelationEntity implements Serializable {

    private static final long serialVersionUID = 4756517273377843535L;

    /**
     *id
     */
    private Long id;

    /**
     * 客户ID
     */
    private Long customerId;

    /**
     * 客户名称
     */
    private String customerName;

    /**
     * 客户简称
     */
    private String customerNickname;

    /**
     * 供应商ID
     */
    private Long supplierId;

    /**
     * 供应商名称
     */
    private String supplierName;
    /**
     * 展示用供应商名称  带类型
     */
    @TableField(exist = false)
    private String showSupplierName;

    /**
     * 申请状态（0：待通过 1：已通过 2：未通过 3：已解除）
     */
    private Integer status;

    /**
     * 身份，1-供应商，2-客户
     */
    private Integer idType;

    /**
     * 类型: 1-租户 2-自定义
     */
    private Integer type;

    /**
     * 所属行业
     */
    private Long industryId;

    /**
     * 所属地域
     */
    private Long regionId;

    /**
     * 负责人
     */
    private String director;

    /**
     * 所属部门ID
     */
    private String section;

    /**
     * 所属部门名称
     */
    private String sectionName;

    /**
     * 联系电话
     */
    private String tel;

    /**
     * 备注
     */
    private String remark;

    /**
     * 审核原因
     */
    private String reason;

    /**
     * 创建人
     */
    @TableField(fill = FieldFill.INSERT)
    private String creator;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 修改人
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private String updator;

    /**
     * 修改时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    /**
     * 租户ID
     */
    @TableField(fill = FieldFill.INSERT)
    private Long tenantId;

    /**
     * 工程ID
     */
    @TableField(fill = FieldFill.INSERT)
    private Long engineeringId;

    /**
     * 模块ID
     */
    @TableField(fill = FieldFill.INSERT)
    private Long moduleId;

    /**
     * 空间ID
     */
    @TableField(fill = FieldFill.INSERT)
    private Long spaceId;

    /**
     * 版本号
     */
    @Version
    private Integer version;

    /**
     * 删除
     */
    @TableLogic
    private Integer deleted;

    /**
     * 客户拓展字段
     */
    private String cusExpandFieldJson;

    /**
     * 供应商拓展字段
     */
    private String supExpandFieldJson;

}
