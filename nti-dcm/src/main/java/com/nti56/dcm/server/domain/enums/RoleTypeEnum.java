package com.nti56.dcm.server.domain.enums;

import lombok.Getter;

public enum RoleTypeEnum {
    SYSTEM_ROLE(1, "SYSTEM_ROLE", "系统固定角色"),
    CUSTOM_ROLE(2, "CUSTOM_ROLE", "自定义角色"),
    ;

    @Getter
    private Integer value;

    @Getter
    private String name;

    @Getter
    private String nameDesc;

    RoleTypeEnum(Integer value, String name, String nameDesc) {
        this.value = value;
        this.name = name;
        this.nameDesc = nameDesc;
    }

    public static RoleTypeEnum typeOfValue(Integer value){
        RoleTypeEnum[] values = RoleTypeEnum.values();
        for (RoleTypeEnum v : values) {
            if (v.value.equals(value)) {
                return v;
            }
        }
        return null;
    }

    public static RoleTypeEnum typeOfName(String name){
        RoleTypeEnum[] values = RoleTypeEnum.values();
        for (RoleTypeEnum v : values) {
            if (v.name.equals(name)) {
                return v;
            }
        }
        return null;
    }

    public static RoleTypeEnum typeOfNameDesc(String nameDesc){
        RoleTypeEnum[] values = RoleTypeEnum.values();
        for (RoleTypeEnum v : values) {
            if (v.nameDesc.equals(nameDesc)) {
                return v;
            }
        }
        return null;
    }
}
